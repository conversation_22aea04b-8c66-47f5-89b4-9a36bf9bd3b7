import { environment } from '@/env/environment'
import { LibraryService } from '@/services/library.service'
import { ProfileService } from '@/services/profile.service'
import { ToasterService } from '@/services/toaster.service'
import { CommonModule, isPlatformBrowser } from '@angular/common'
import { Component, inject, input, output, PLATFORM_ID, signal } from '@angular/core'
import { FormsModule } from '@angular/forms'

@Component({
  selector: 'app-book-purchase-modal',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './book-purchase-modal.component.html',
  styleUrl: './book-purchase-modal.component.scss'
})
export class BookPurchaseModalComponent {
  private libraryService = inject(LibraryService);
  private profileService = inject(ProfileService);
  private toasterService = inject(ToasterService);
  private platformId = inject(PLATFORM_ID);

  bookData = input.required<any>();
  isVisible = input<boolean>(false);
  close = output<void>();
  purchaseComplete = output<void>();
  paymentType = signal<string>(environment.hideStripe ? 'yookassa' : 'stripe');
  isLoading = signal<boolean>(false);
  environment = environment;

  onClose() {
    this.close.emit();
  }

  onPurchase() {
    if (!this.profileService.profile) {
      this.toasterService.showToast('Необходимо авторизоваться', 'error', 'bottom-middle');
      return;
    }

    this.isLoading.set(true);

    this.libraryService.purchase(this.bookData().id, this.paymentType()).subscribe({
      next: (res: any) => {
        if (isPlatformBrowser(this.platformId)) {
          localStorage.setItem('redirect', location.href);
          location.href = res.paymentUrl;
        }
      },
      error: (err) => {
        this.isLoading.set(false);
        this.toasterService.showToast(err.error?.message || 'Ошибка при покупке', 'error', 'bottom-middle');
      }
    });
  }

  onPaymentTypeChange(type: string) {
    this.paymentType.set(type);
  }
}
