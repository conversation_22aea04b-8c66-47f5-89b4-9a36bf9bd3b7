.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal-content {
  background: var(--background-color, #fff);
  border-radius: 12px;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  margin-bottom: 24px;

  h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color, #111827);
  }

  .close-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    color: var(--text-color-secondary, #6b7280);
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--hover-background, #f3f4f6);
      color: var(--text-color, #111827);
    }
  }
}

.modal-body {
  padding: 0 24px;

  .book-info {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
    padding: 16px;
    background-color: var(--card-background, #f9fafb);
    border-radius: 8px;

    .book-cover {
      width: 60px;
      height: 80px;
      object-fit: cover;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .book-details {
      flex: 1;

      h4 {
        margin: 0 0 8px 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-color, #111827);
        line-height: 1.4;
      }

      .book-author {
        margin: 0;
        color: var(--text-color-secondary, #6b7280);
        font-size: 0.9rem;
      }
    }
  }

  .price-info {
    margin-bottom: 24px;
    text-align: center;

    .price {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--primary-color, #DA9C50);
      padding: 12px 16px;
      background-color: var(--primary-background, #FFF6E0);
      border-radius: 8px;
      border: 1px solid var(--primary-border, #EDC17F);
    }
  }

  .payment-options {
    h5 {
      margin: 0 0 16px 0;
      font-size: 1rem;
      font-weight: 600;
      color: var(--text-color, #111827);
    }

    .payment-option {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 16px;
      border: 2px solid var(--border-color, #e5e7eb);
      border-radius: 8px;
      margin-bottom: 12px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        border-color: var(--primary-color, #DA9C50);
        background-color: var(--primary-background, #FFF6E0);
      }

      input[type="radio"] {
        margin: 0;
        accent-color: var(--primary-color, #DA9C50);
      }

      label {
        cursor: pointer;
        font-weight: 500;
        color: var(--text-color, #111827);
        flex: 1;
      }

      &:has(input:checked) {
        border-color: var(--primary-color, #DA9C50);
        background-color: var(--primary-background, #FFF6E0);
      }
    }
  }
}

.modal-footer {
  display: flex;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid var(--border-color, #e5e7eb);

  button {
    flex: 1;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  .cancel-button {
    background-color: transparent;
    color: var(--text-color-secondary, #6b7280);
    border-color: var(--border-color, #e5e7eb);

    &:hover:not(:disabled) {
      background-color: var(--hover-background, #f3f4f6);
      color: var(--text-color, #111827);
    }
  }

  .purchase-button {
    background-color: var(--primary-color, #DA9C50);
    color: white;
    border-color: var(--primary-color, #DA9C50);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    &:hover:not(:disabled) {
      background-color: var(--primary-hover, #BA7A2C);
      border-color: var(--primary-hover, #BA7A2C);
    }

    .loading-spinner {
      width: 16px;
      height: 16px;
      border: 2px solid transparent;
      border-top: 2px solid currentColor;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// Responsive design
@media (max-width: 640px) {
  .modal-content {
    width: 95%;
    margin: 20px;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding-left: 16px;
    padding-right: 16px;
  }

  .modal-footer {
    flex-direction: column;

    button {
      width: 100%;
    }
  }

  .book-info {
    flex-direction: column;
    text-align: center;

    .book-cover {
      align-self: center;
    }
  }
}
