import { environment } from '@/env/environment'
import { navigateToUrl } from '@/utils/navigation.util'
import { CommonModule, isPlatformBrowser } from '@angular/common'
import {
  Component,
  computed,
  effect,
  inject,
  input,
  OnDestroy,
  OnInit,
  output,
  PLATFORM_ID,
  signal
} from '@angular/core'
import { Router } from '@angular/router'
import { TranslocoService, TranslocoPipe } from '@jsverse/transloco'
import { Subject, takeUntil } from 'rxjs'
import { AdvertisementDataService } from '../../services/advertisement-data.service'
import { TagTranslatePipe } from '../../pipes/tag-translate.pipe'
import mantrasData from './1000-names.json'
import ekadashiData from './ekadashi.json'

interface MantraTranslation {
  lang: string;
  text: string;
}

interface Mantra {
  mantraTranslations: MantraTranslation[];
  translateTranslations: MantraTranslation[];
  date: string;
  id: number;
}

interface EkadashiTranslation {
  lang: string;
  text: string;
}

interface Ekadashi {
  date: string;
  nameTranslations: EkadashiTranslation[];
  descriptionTranslations: EkadashiTranslation[];
}

@Component({
  selector: 'app-daily-sidebar',
  standalone: true,
  imports: [CommonModule, TranslocoPipe, TagTranslatePipe],
  templateUrl: './daily-sidebar.component.html',
  styleUrl: './daily-sidebar.component.scss'
})
export class DailySidebarComponent implements OnInit, OnDestroy {
  readonly showDailySidebar = signal(false);
  readonly isDarkMarker = input<boolean>(false);
  readonly showMobileSidebar = input<boolean>(false);
  readonly closeDailySidebar = output<void>();
  readonly isAnimationCompleted = signal(true);

  private advertisementDataService = inject(AdvertisementDataService);
  private router = inject(Router);
  private platformId = inject(PLATFORM_ID);
  private translocoService = inject(TranslocoService);

  schedule: any[] = [];
  viewSchedule: any[] = [];

  // Subject для управления подписками
  private destroy$ = new Subject<void>();

  // Сигнал для отслеживания текущего языка
  private currentLang = signal(this.translocoService.getActiveLang());



  // Функция для форматирования месяца в зависимости от языка
  private formatMonth(date: Date, lang: string): string {
    const month = date.getMonth();

    // Массив названий месяцев для каждого языка
    const monthNames = {
      ru: ['января', 'февраля', 'марта', 'апреля', 'мая', 'июня',
           'июля', 'августа', 'сентября', 'октября', 'ноября', 'декабря'],
      en: ['January', 'February', 'March', 'April', 'May', 'June',
           'July', 'August', 'September', 'October', 'November', 'December'],
      de: ['Januar', 'Februar', 'März', 'April', 'Mai', 'Juni',
           'Juli', 'August', 'September', 'Oktober', 'November', 'Dezember'],
      ua: ['січня', 'лютого', 'березня', 'квітня', 'травня', 'червня',
           'липня', 'серпня', 'вересня', 'жовтня', 'листопада', 'грудня'],
      it: ['gennaio', 'febbraio', 'marzo', 'aprile', 'maggio', 'giugno',
           'luglio', 'agosto', 'settembre', 'ottobre', 'novembre', 'dicembre']
    };

    return monthNames[lang as keyof typeof monthNames]?.[month] || monthNames.ru[month];
  }



  constructor() {
    // Эффект для отслеживания изменений языка
    effect(() => {
      // Обновляем сигнал языка при изменении активного языка
      const activeLang = this.translocoService.getActiveLang();
      if (this.currentLang() !== activeLang) {
        this.currentLang.set(activeLang);
      }
    });

    // Подписываемся на изменения языка в TranslocoService
    this.translocoService.langChanges$
      .pipe(takeUntil(this.destroy$))
      .subscribe(lang => {
        this.currentLang.set(lang);
      });
  }

  // Оптимизированная дата - вычисляется один раз с учетом языка и переводов
  readonly currentDate = computed(() => {
    const today = new Date();
    const lang = this.currentLang();
    const day = today.getDate();
    const month = today.getMonth();
    const year = today.getFullYear();

    // Массив названий месяцев для каждого языка
    const monthNames = {
      ru: ['января', 'февраля', 'марта', 'апреля', 'мая', 'июня',
           'июля', 'августа', 'сентября', 'октября', 'ноября', 'декабря'],
      en: ['January', 'February', 'March', 'April', 'May', 'June',
           'July', 'August', 'September', 'October', 'November', 'December'],
      de: ['Januar', 'Februar', 'März', 'April', 'Mai', 'Juni',
           'Juli', 'August', 'September', 'Oktober', 'November', 'Dezember'],
      ua: ['січня', 'лютого', 'березня', 'квітня', 'травня', 'червня',
           'липня', 'серпня', 'вересня', 'жовтня', 'листопада', 'грудня'],
      it: ['gennaio', 'febbraio', 'marzo', 'aprile', 'maggio', 'giugno',
           'luglio', 'agosto', 'settembre', 'ottobre', 'novembre', 'dicembre']
    };

    const monthName = monthNames[lang as keyof typeof monthNames]?.[month] || monthNames.ru[month];

    // Форматирование в зависимости от языка
    if (lang === 'en') {
      return `${monthName} ${day}, ${year}`;
    } else if (lang === 'de') {
      return `${day}. ${monthName} ${year}`;
    } else {
      // Русский формат
      return `${day} ${monthName} ${year} г.`;
    }
  });

  // Мантра дня - вычисляется на основе текущей даты
  readonly dailyMantra = computed(() => {
    const mantras = mantrasData as Mantra[];
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Начальная дата - 22.12.2018
    const startDate = new Date('2018-12-22');
    startDate.setHours(0, 0, 0, 0);

    // Вычисляем количество дней от начальной даты
    const daysDiff = Math.floor((today.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

    // Используем модуло для циклического повторения (1000 мантр)
    const mantraIndex = daysDiff % 1000;

    const selectedMantra = mantras[mantraIndex];
    const currentLang = this.currentLang(); // Используем сигнал вместо прямого вызова

    // Получаем переводы для текущего языка
    const mantraText = selectedMantra.mantraTranslations?.find(t => t.lang === currentLang)?.text ||
                      selectedMantra.mantraTranslations?.find(t => t.lang === 'ru')?.text ||
                      (selectedMantra as any).mantra || ''; // Fallback для старой структуры

    const translateText = selectedMantra.translateTranslations?.find(t => t.lang === currentLang)?.text ||
                         selectedMantra.translateTranslations?.find(t => t.lang === 'ru')?.text ||
                         (selectedMantra as any).translate || ''; // Fallback для старой структуры

    return {
      mantra: mantraText,
      translate: translateText,
      date: selectedMantra.date,
      id: selectedMantra.id
    };
  });

  // Информация об экадаши - проверяем, является ли сегодня экадаши
  readonly todayEkadashi = computed(() => {
    const ekadashis = ekadashiData as Ekadashi[];
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const currentLang = this.currentLang(); // Используем сигнал вместо прямого вызова

    // Форматируем дату в YYYY-MM-DD с учетом локального времени
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const todayStr = `${year}-${month}-${day}`;

    // Проверяем, является ли сегодня экадаши
    const todayEkadashi = ekadashis.find(e => e.date === todayStr);

    if (todayEkadashi) {
      const name = todayEkadashi.nameTranslations?.find(t => t.lang === currentLang)?.text ||
                   todayEkadashi.nameTranslations?.find(t => t.lang === 'ru')?.text ||
                   (todayEkadashi as any).name || ''; // Fallback для старой структуры

      const description = todayEkadashi.descriptionTranslations?.find(t => t.lang === currentLang)?.text ||
                         todayEkadashi.descriptionTranslations?.find(t => t.lang === 'ru')?.text ||
                         (todayEkadashi as any).description || ''; // Fallback для старой структуры

      return {
        isEkadashi: true,
        name,
        description
      };
    }

    // Если сегодня не экадаши, находим ближайший будущий экадаши
    const upcomingEkadashi = ekadashis.find(e => {
      const ekadashiDate = new Date(e.date);
      ekadashiDate.setHours(0, 0, 0, 0);
      return ekadashiDate > today;
    });

    if (upcomingEkadashi) {
      const ekadashiDate = new Date(upcomingEkadashi.date);
      const day = ekadashiDate.getDate();
      const monthName = this.formatMonth(ekadashiDate, currentLang);
      const formattedDate = `${day} ${monthName}`;

      const name = upcomingEkadashi.nameTranslations?.find(t => t.lang === currentLang)?.text ||
                   upcomingEkadashi.nameTranslations?.find(t => t.lang === 'ru')?.text ||
                   (upcomingEkadashi as any).name || ''; // Fallback для старой структуры

      const description = upcomingEkadashi.descriptionTranslations?.find(t => t.lang === currentLang)?.text ||
                         upcomingEkadashi.descriptionTranslations?.find(t => t.lang === 'ru')?.text ||
                         (upcomingEkadashi as any).description || ''; // Fallback для старой структуры

      return {
        isEkadashi: false,
        name: `${name} (${formattedDate})`,
        description
      };
    }

    // Если не найден будущий экадаши (конец списка), возвращаем стандартный текст
    const defaultTexts = {
      ru: {
        name: 'Благоприятный день для духовной практики',
        description: 'Благоприятный день для медитации, чтения священных текстов и духовных практик. Рекомендуется легкое питание или пост.'
      },
      en: {
        name: 'Auspicious day for spiritual practice',
        description: 'Auspicious day for meditation, reading sacred texts and spiritual practices. Light diet or fasting is recommended.'
      },
      de: {
        name: 'Günstiger Tag für spirituelle Praxis',
        description: 'Günstiger Tag für Meditation, das Lesen heiliger Texte und spirituelle Praktiken. Leichte Kost oder Fasten wird empfohlen.'
      },
      ua: {
        name: 'Сприятливий день для духовної практики',
        description: 'Сприятливий день для медитації, читання священних текстів та духовних практик. Рекомендується легке харчування або піст.'
      },
      it: {
        name: 'Giorno propizio per la pratica spirituale',
        description: 'Giorno propizio per la meditazione, la lettura di testi sacri e le pratiche spirituali. Si raccomanda una dieta leggera o il digiuno.'
      }
    };

    const defaultText = defaultTexts[currentLang as keyof typeof defaultTexts] || defaultTexts.ru;

    return {
      isEkadashi: false,
      name: defaultText.name,
      description: defaultText.description
    };
  });

  ngOnInit() {
    if (isPlatformBrowser(this.platformId)) {
      this.advertisementDataService.getActiveCalendarEvents()
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (events: any[]) => {
            const activeEvents = events.filter((event: any) => event.active === true);
            this.processCalendarEvents(activeEvents);
          },
          error: (error: any) => {
            this.schedule = [];
            this.viewSchedule = [];
          }
        });
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }



  private processCalendarEvents(events: any[]) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const sortedEvents = events
      .filter(event => {
        if (!event.startDate) {
          return false;
        }

        // Проверяем, что событие еще не закончилось
        const endDate = event.endDate ? new Date(event.endDate) : new Date(event.startDate);
        endDate.setHours(23, 59, 59, 999); // Конец дня

        const isValid = endDate >= today;
        return isValid;
      })
      .sort((a, b) => new Date(a.startDate).getTime() - new Date(b.startDate).getTime())
      .slice(0, 4);


    const groupedByDay: { [key: string]: any[] } = {};

    sortedEvents.forEach(event => {
      const eventDate = new Date(event.startDate);
      const dayKey = eventDate.toDateString();

      if (!groupedByDay[dayKey]) {
        groupedByDay[dayKey] = [];
      }

      groupedByDay[dayKey].push({
        name: event.title,
        description: event.description,
        date: event.startDate,
        imageUrl: event.image ? `${environment.serverUrl}/upload/${event.image.name}` : '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
        tag: event.type || "Мероприятие",
        link: event.link
      });
    });

    this.schedule = Object.keys(groupedByDay).map(dayKey => {
      const date = new Date(dayKey);
      const today = new Date();
      const isToday = date.toDateString() === today.toDateString();

      const lang = this.currentLang();

      return {
        day: isToday ? this.translocoService.translate('today') : date.getDate().toString(),
        month: this.formatMonth(date, lang),
        events: groupedByDay[dayKey],
        isToday
      };
    });

    this.viewSchedule = this.schedule.slice(0, 3);
  }

  loadMore() {
    const currentLength = this.viewSchedule.length;
    const nextItems = this.schedule.slice(currentLength, currentLength + 2);
    this.viewSchedule = this.viewSchedule.concat(nextItems);
  }

  navigateToCalendar() {
    const lang = this.currentLang();
    this.router.navigate([`/${lang}/calendar`]);
    this.closeSidebar()
  }

  openSidebar() {
    this.showDailySidebar.set(true);
    this.isAnimationCompleted.set(true);
  }

  toogleSidebar() {
    this.showDailySidebar.set(!this.showDailySidebar());
    if (!this.showDailySidebar()) {
      this.isAnimationCompleted.set(false);
      setTimeout(() => {
        this.isAnimationCompleted.set(true);
      }, 400);
    }
  }
  
  closeSidebar() {
    this.showDailySidebar.set(false);
    this.closeDailySidebar.emit();
    this.isAnimationCompleted.set(false);
    setTimeout(() => {
      this.isAnimationCompleted.set(true);
    }, 400);
  }

  navigateToEvent(event: any) {
    if (event.link) {
      navigateToUrl(event.link, this.router);
    }
    this.closeSidebar();
  }
}
