@if (showDailySidebar() || showMobileSidebar()) {
  <div class="sidebar-mask" (click)="closeSidebar()"></div>
}
<div class="daily-sidebar-container" [class.animation-completed]="isAnimationCompleted()" [class.daily-sidebar-is-open]="showDailySidebar() || showMobileSidebar()">
  <div class="marker-section">
    <div
      (click)="toogleSidebar()"
      class="marker cursor-pointer"
      [class.dark-wheel]="isDarkMarker() && !showDailySidebar()"
    >
    </div>
  </div>
  <div class="main-sidebar-section">
    <div class="side-bar-header">
      {{currentDate()}}
      <svg class="cursor-pointer" (click)="closeSidebar()" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M6 6L17.3137 17.3137" stroke="#351F04" stroke-width="2" stroke-linecap="round"
          stroke-linejoin="round" />
        <path d="M6 17L17.3137 5.68629" stroke="#351F04" stroke-width="2" stroke-linecap="round"
          stroke-linejoin="round" />
      </svg>
    </div>
    <div class="sidebar-body">
      <div class="daily-name">{{todayEkadashi().name}}</div>
      <div class="daily-description">
        {{todayEkadashi().description}}
      </div>
      <div class="daily-mantra-box">
        <div class="mantra-title">{{ 'mantra_of_the_day' | transloco }}</div>
        <div class="mantra-name">
          {{dailyMantra().mantra}}<br>
          {{dailyMantra().translate}}
        </div>
      </div>
      <div class="upcoming-events-box">
        <div class="upcoming-events-box_title">{{ 'upcoming_events' | transloco }}</div>
        <div class="schedule-section">
          <div class="schedule-dash-marker"></div>
          @for (item of viewSchedule; track $index) {
            <div class="schedule-point-box">
              <div 
                class="day-box" 
                [class.items-center]="item.events?.length === 1"
                >
                <div class="date"
                  [class.today]="item.isToday"
                  [class.default-m]="item.events?.length >= 2"
                >
                  @if (item.isToday) {
                    {{ 'today' | transloco }}
                  } @else {
                    <div class="day">{{item.day}}</div>
                    <div class="month">{{item.month}}</div>
                  }
                </div>
              </div>
              <div class="schedule-point-info-cards">
                @for (event of item.events; track event.name) {
                  <div class="info-card">
                    <div class="img-section" [style.background-image]="'url(' + event.imageUrl + ')'">
                      <div class="img-section_mask"></div>
                    </div>
                    <div class="info-box">
                      <div class="title" (click)="navigateToEvent(event)">{{event.name}}</div>
                      <div class="tag">{{event.tag | tagTranslate}}</div>
                    </div>
                  </div>
                }
              </div>
            </div>
          }
          <div class="load-more" (click)="navigateToCalendar()">{{ 'view_all_events' | transloco }}</div>
        </div>

      </div>
    </div>