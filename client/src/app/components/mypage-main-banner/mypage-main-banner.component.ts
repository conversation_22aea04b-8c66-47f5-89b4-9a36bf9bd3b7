// banner.component.ts
import { environment } from "@/env/environment"
import { navigateToUrl } from '@/utils/navigation.util'; // banner.model.ts
import { CommonModule } from '@angular/common'
import { Component, inject, input, PLATFORM_ID } from '@angular/core'
import { Router } from '@angular/router'
export interface BannerBg {
  name: string;
  originalName: string;
  description: string;
  id: number;
  sort: number;
}

export interface BannerImage {
  id: number;
  name: string;
  originalName: string;
  description: string;
  sort: number;
}
export interface BannerButton {
  index: number | null;
  link: string | null;
  name: string | null;
}

export interface BannerData {
  lang: string;
  id: number;
  title: string;
  description: string;
  seo_title?: string | null;
  seo_description?: string | null;
  content?: string | null;
  bg?: BannerBg | null;
  telegram?: string | null;
  youtube?: string | null;
  facebook?: string | null;
  instagram?: string | null;
  email?: string | null;
  phone?: string | null;
  image?: BannerImage | null;
  buttons?: BannerButton[];
  carousel?: any[];
}


@Component({
  selector: 'app-banner',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './mypage-main-banner.component.html',
  styleUrls: ['./mypage-main-banner.component.scss']
})
export class MypageMainBannerComponent {
  readonly data = input<BannerData | null>(null);
  readonly environment = environment;
  readonly platformId = inject(PLATFORM_ID);
  readonly router = inject(Router);
  navigateToUrl = navigateToUrl;

  get backgroundImage(): string {
    return this.data()?.bg ? `${this.environment.serverUrl + '/upload/' + this.data()?.bg?.name}` : 'none';
  }
}
