@import '../../../assets/styles/new-palette';
@import '../../../assets/styles/new-typography';

/* banner.component.scss */
.banner {
  position: relative;

  &__bg {
    width: 100%;
    height: 360px;
    object-fit: cover;
    object-position: center;
  }

  &__content {
    position: relative;
    z-index: 2;
    display: flex;
    gap: 30px;
    width: 100%;
    max-width: 1440px;
    padding: 40px 15px;
    margin: 0 auto;

    @media (max-width: 1200px) {
      flex-wrap: wrap;
    }

    @media (max-width: 650px) {
      flex-direction: column;
      text-align: center;
      gap: 40px;
      padding: 0px 15px 40px;
    }
  }

  &__text {
    flex: 1 1 0;
  }

  &__title {
    @include h2;
    color: main(600);
    margin-bottom: 30px;

    @media (max-width: 1100px) {
      @include h3;
    }

    @media (max-width: 650px) {
      @include h4;
      text-align: center;
    }
  }

  &__description {
    @include body-1;
    color: main(500);

    @media (max-width: 1100px) {
      @include body-2;
    }

    @media (max-width: 650px) {
      @include body-3;
      text-align: center;
    }
  }

  .buttons-box {
    max-width: 460px;
    min-width: 460px;
    display: flex;
    gap: 20px;
    flex-wrap: wrap;

    @media (max-width: 1200px) {
      min-width: 100%;
      max-width: 100%;
      margin-top: 26px;
    }
    @media (max-width: 650px) {
      min-width: 100%;
      max-width: 360px;
      justify-content: center;
      margin-top: 0;
    }

    .primary-button {
      @include button-4;
      color: main(600);
      background: url(../../../assets/images/main-v2/stroke-button-big.webp) no-repeat center;
      background-size: contain;
      border: none;
      cursor: pointer;
      width: fit-content;
      width: 216px;
      height: 52px;
      transition: all 0.2s ease-in-out;
      display: flex;
      align-items: center;
      justify-content: center;
      text-decoration: none;

      span {
        text-align: center;
        max-width: 80%;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      &:hover {
        background: url(../../../assets/images/main-v2/stroke-button-big-hover.webp) no-repeat center;
        background-size: contain;
      }

      &:active {
        color: main(800);
        background: url(../../../assets/images/main-v2/stroke-button-big-active.webp) no-repeat center;
        background-size: contain;
      }

      @media (max-width: 1100px) {
        background: url(../../../assets/images/main-v2/stroke-button-small.webp) no-repeat center;
        background-size: contain;
        width: 157px;
        height: 52px;

        span {
          width: 130px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          margin: auto;
        }

        &:hover {
          background: url(../../../assets/images/main-v2/stroke-button-small-hover.webp) no-repeat center;
          background-size: contain;
        }

        &:active {
          background: url(../../../assets/images/main-v2/stroke-button-small-active.webp) no-repeat center;
          background-size: contain;
        }
      }
    }

  }

  &__image {
    width: 210px;
    height: 210px;
    min-width: 210px;
    border-radius: 50%;
    padding: 10px;
    background-color: main(50);
    position: sticky;
    margin-top: -74px;

    @media (max-width: 1100px) {
      width: 160px;
      min-width: 160px;
      height: 160px;
      padding: 8px;
    }
     @media (max-width: 650px) {
      width: 167px;
      min-width: 167px;
      height: 167px;
      margin: -85px auto -12px;
      padding: 6px;
     }


    img {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      object-fit: cover;
      object-position: center;
    }
  }

  .social-media-buttons {
    display: flex;
    gap: 24px;
    position: absolute;
    top: -70px;
    right: 15px;
    z-index: 2;

    .social-media-button {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: main(50);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease-in-out;

      &:hover {
        background-color: main(100);

        svg {
          opacity: 0.8;
        }
      }

      svg {
        transition: all 0.2s ease-in-out;
        opacity: 1;
      }
    }

    @media (max-width: 650px) {
      position: static;
      margin: 0 auto 0 auto;
      justify-content: center;
      width: 340px;
      max-width: 90vw;
    }
  }
}