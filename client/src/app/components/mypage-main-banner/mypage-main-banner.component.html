<!-- banner.component.html -->
<section class="banner main_banner">
  <img class="banner__bg" [src]="backgroundImage" [alt]="data()?.bg?.description || ''">
  
  <div class="banner__content container">
    <div class="banner__image" *ngIf="data()?.image">
      @if(data()?.image?.name) {
        <img 
          [src]="environment.serverUrl + '/upload/' + data()?.image?.name" 
          [alt]="data()?.image?.description || data()?.image?.originalName || ''"
        >
      }
    </div>
    <div class="banner__text">
      <h1 class="banner__title">{{ data()?.title || '' }}</h1>
      <p class="banner__description">{{ data()?.description || '' }}</p>
    </div>

    <div class="social-media-buttons">
      @if(data()?.youtube) {
        <div class="social-media-button" (click)="navigateToUrl(data()?.youtube || '', router)">
          <svg width="22" height="16" viewBox="0 0 22 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M8.58008 0.509905C10.6523 0.489457 12.7246 0.498193 14.7926 0.559547C16.0709 0.596362 17.3535 0.698618 18.6232 0.870419C20.2327 1.09133 21.2347 2.11394 21.5277 3.66013C21.723 4.69906 21.7872 5.75021 21.8084 6.80141C21.8424 8.37627 21.842 9.95151 21.6424 11.5223C21.5787 12.0374 21.5026 12.569 21.3201 13.0514C20.853 14.2908 19.8887 14.9823 18.5426 15.1705C16.8355 15.4119 15.1151 15.4691 13.3994 15.4896C11.3568 15.5141 9.3098 15.5021 7.27148 15.4448C5.96359 15.408 4.65144 15.3057 3.35628 15.1299C1.75107 14.9172 0.710373 13.8822 0.46403 12.3361C0.336647 11.5058 0.290152 10.6631 0.205241 9.82468C0.192501 9.70606 0.179732 9.58279 0.166992 9.46417V6.57273C0.192471 6.18007 0.204922 5.78699 0.251628 5.39841C0.336548 4.71132 0.391773 4.02002 0.544596 3.34519C0.850354 1.97488 1.993 1.01712 3.43685 0.824846C5.14402 0.595776 6.86441 0.526267 8.58008 0.509905ZM8.85189 11.2887C10.8264 10.1885 12.7796 9.10039 14.7625 7.99607C12.7753 6.88766 10.8264 5.80365 8.85189 4.70343V11.2887Z" fill="#532E00"/>
          </svg>
        </div>
      }
      @if(data()?.facebook) {
        <div class="social-media-button" (click)="navigateToUrl(data()?.facebook || '', router)">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9.3701 0C9.78748 0 10.2049 0 10.6222 0C10.8622 0.0312907 11.097 0.0521512 11.337 0.0886571C13.4813 0.391134 15.3647 1.26206 16.909 2.79009C19.5124 5.36115 20.4932 8.47979 19.768 12.0574C19.1471 15.1134 17.3629 17.3664 14.6238 18.8579C14.0395 19.176 13.4239 19.4159 12.7822 19.5932C12.7822 17.1265 12.7822 14.6754 12.7822 12.2034C13.5021 12.2034 14.2012 12.2034 14.916 12.2034C15.0256 11.369 15.1299 10.5502 15.2395 9.70013C14.3995 9.70013 13.5908 9.70013 12.7822 9.70013C12.7822 9.03259 12.7561 8.39113 12.7926 7.74967C12.8187 7.2751 13.0795 7.01434 13.5543 6.94654C13.9143 6.89439 14.2795 6.91004 14.6447 6.89961C14.8638 6.89439 15.0829 6.89961 15.3021 6.89961C15.3021 6.14342 15.3021 5.40808 15.3021 4.65189C14.8325 4.62581 14.3734 4.57888 13.9143 4.58409C13.4343 4.58931 12.9439 4.57888 12.4796 4.68318C11.1283 4.97523 10.2727 6.02868 10.1996 7.44198C10.1683 8.11473 10.184 8.7927 10.1736 9.47067C10.1736 9.54889 10.1736 9.6219 10.1736 9.72099C9.44836 9.72099 8.74926 9.72099 8.04493 9.72099C8.04493 10.5658 8.04493 11.3846 8.04493 12.2295C8.75969 12.2295 9.4588 12.2295 10.1579 12.2295C10.1579 14.8318 10.1579 17.4081 10.1579 19.9896C10.1266 19.9948 10.1057 20 10.0849 20C10.0327 20 9.98052 20 9.92835 20C8.48318 19.9844 7.0954 19.7027 5.79632 19.0717C2.70773 17.5541 0.819102 15.1082 0.156516 11.734C0.0834753 11.3638 0.0521721 10.9883 0 10.618C0 10.2008 0 9.78357 0 9.36636C0.0156516 9.23598 0.0313032 9.11082 0.0469549 8.98044C0.281729 6.82138 1.11127 4.91786 2.56687 3.30639C4.11116 1.60626 6.02066 0.542373 8.29536 0.151239C8.65013 0.0886571 9.01533 0.0521512 9.3701 0Z" fill="#532E00"/>
          </svg>
        </div>
      }
      @if(data()?.telegram) {
        <div class="social-media-button" (click)="navigateToUrl(data()?.telegram || '', router)">
          <svg width="20" height="17" viewBox="0 0 20 17" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M0.833984 6.6543C0.968602 6.1223 1.34553 5.8696 1.85708 5.70556C7.11165 4.00315 12.3617 2.28743 17.6163 0.57615C18.3612 0.332315 19.0388 0.691418 19.1554 1.40519C19.1824 1.57366 19.1599 1.75986 19.124 1.92833C18.0695 6.37944 17.0105 10.8261 15.9515 15.2772C15.7317 16.1905 14.8163 16.5053 14.0759 15.9112C12.99 15.0422 11.913 14.1689 10.8361 13.2866C10.7194 13.1935 10.6521 13.1935 10.5354 13.2822C9.68285 13.9206 8.82578 14.5501 7.9732 15.1841C7.91936 15.224 7.87 15.2595 7.78923 15.3127C7.7982 15.2506 7.7982 15.2107 7.80717 15.1797C8.1841 13.8231 8.56552 12.4665 8.94694 11.1098C8.97386 11.0212 9.03668 10.9325 9.10399 10.8616C11.0066 8.88872 12.9137 6.92031 14.8208 4.95189C14.8791 4.89426 14.9329 4.82775 14.9778 4.76125C15.1034 4.57505 15.0945 4.34008 14.9643 4.16275C14.8297 3.98541 14.5919 3.90561 14.372 3.97655C14.2868 4.00315 14.206 4.04748 14.1297 4.08738C11.0515 5.77206 7.97769 7.46118 4.90392 9.15029C4.78277 9.21679 4.68405 9.21679 4.5584 9.16359C3.61608 8.75129 2.67376 8.33012 1.72246 7.93998C1.27374 7.75378 0.946166 7.48778 0.833984 7.00897C0.833984 6.88927 0.833984 6.76957 0.833984 6.6543Z" fill="#532E00"/>
            <path d="M11.581 6.75191C11.24 7.10658 10.899 7.46125 10.5535 7.81149C9.77717 8.61393 9.00536 9.41637 8.22458 10.2144C8.0765 10.3695 7.97778 10.538 7.91944 10.7419C7.53803 12.1207 7.15212 13.4995 6.76622 14.8783C6.75276 14.9315 6.73032 14.9847 6.69442 15.0911C6.58673 14.5945 6.48801 14.1512 6.38929 13.7079C6.12903 12.5197 5.87326 11.336 5.60851 10.1523C5.58158 10.0282 5.60851 9.96611 5.72518 9.90404C7.62328 8.86663 9.52139 7.82479 11.4195 6.78738C11.4644 6.76078 11.5092 6.74304 11.5586 6.72088C11.5676 6.72974 11.5766 6.74304 11.581 6.75191Z" fill="#532E00"/>
          </svg>
        </div>
      }
      @if(data()?.instagram) {
        <div class="social-media-button" (click)="navigateToUrl(data()?.instagram || '', router)">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19.1701 9.99965C19.1701 11.681 19.1777 13.3661 19.1663 15.0474C19.1549 17.0551 17.8344 18.6757 15.884 19.0856C15.607 19.1426 15.3149 19.1653 15.0303 19.1653C11.6797 19.1691 8.32534 19.1729 4.97477 19.1653C2.95608 19.1615 1.32443 17.837 0.914618 15.8824C0.8577 15.6129 0.834933 15.3283 0.834933 15.0512C0.834933 11.6886 0.831138 8.32212 0.838728 4.95568C0.842522 2.95175 2.1744 1.32356 4.121 0.917458C4.398 0.860528 4.69018 0.837756 4.97477 0.837756C8.32534 0.833961 11.6797 0.830166 15.0303 0.837756C17.049 0.841552 18.6806 2.16232 19.0866 4.12071C19.1511 4.42433 19.1663 4.74693 19.1663 5.05815C19.1739 6.70532 19.1701 8.35248 19.1701 9.99965ZM15.941 10.0034C15.941 6.73188 13.2962 4.07516 10.0215 4.06378C6.73543 4.05998 4.07167 6.70911 4.06408 9.99206C4.06028 13.2712 6.71646 15.9355 9.99493 15.9393C13.2772 15.9431 15.9372 13.2864 15.941 10.0034ZM17.3222 3.8854C17.3222 3.18706 16.7644 2.62915 16.07 2.62915C15.3756 2.62915 14.8178 3.18706 14.814 3.8854C14.814 4.57614 15.3794 5.14165 16.07 5.14165C16.7644 5.14165 17.3222 4.57994 17.3222 3.8854Z" fill="#532E00"/>
            <path d="M13.3646 9.99519C13.3684 11.8549 11.8772 13.3578 10.0179 13.3654C8.15096 13.373 6.64453 11.8701 6.64453 10.0028C6.64453 8.14308 8.13958 6.64393 10.0027 6.64393C11.862 6.64013 13.3609 8.13549 13.3646 9.99519Z" fill="#532E00"/>
          </svg>
        </div>
      }
    </div>
    
    @if(data()?.buttons?.length) {
      <div class="buttons-box">
        @for(btn of data()!.buttons!; track btn.name) {
          <div
            class="primary-button"
            (click)="navigateToUrl(btn?.link || '', router)"
          >
            <span>
              {{ btn?.name || '' }}
            </span>
          </div>
        }
      </div>
    }
  </div>
</section>
