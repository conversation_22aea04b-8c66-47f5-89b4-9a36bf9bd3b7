import { CommonModule, isPlatformBrowser } from '@angular/common'
import { Component, EventEmitter, inject, Input, Output, PLATFORM_ID, signal } from '@angular/core'
import { FormsModule } from '@angular/forms'
import { ContentService } from '../../services/content.service'
import { ProfileService } from '../../services/profile.service'
import { ToasterService } from '../../services/toaster.service'
import { TranslocoService } from '@jsverse/transloco'

@Component({
  selector: 'app-content-purchase-modal',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './content-purchase-modal.component.html',
  styleUrls: ['./content-purchase-modal.component.scss']
})
export class ContentPurchaseModalComponent {
  @Input() contentData = signal<any>(null);
  @Input() isVisible = signal<boolean>(false);
  @Output() close = new EventEmitter<void>();
  @Output() purchaseComplete = new EventEmitter<void>();

  paymentType = signal<string>('stripe');
  isLoading = signal<boolean>(false);

  contentService = inject(ContentService);
  profileService = inject(ProfileService);
  toasterService = inject(ToasterService);
  translocoService = inject(TranslocoService);
  private platformId = inject(PLATFORM_ID);

  onClose() {
    this.close.emit();
  }

  onPurchase() {
    if (!this.profileService.profile) {
      this.toasterService.showToast(this.translocoService.translate('toast.auth_required'), 'error', 'bottom-middle');
      return;
    }

    this.isLoading.set(true);

    this.contentService.purchase(this.contentData().id, this.paymentType()).subscribe({
      next: (res: any) => {
        if (isPlatformBrowser(this.platformId)) {
          localStorage.setItem('redirect', location.href);
          location.href = res.paymentUrl;
        }
      },
      error: (err) => {
        this.isLoading.set(false);
        this.toasterService.showToast(err.error?.message || this.translocoService.translate('toast.purchase_error'), 'error', 'bottom-middle');
      }
    });
  }

  onPaymentTypeChange(type: string) {
    this.paymentType.set(type);
  }
}
