<div class="modal-overlay" *ngIf="isVisible()" (click)="onClose()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h3>Покупка контента</h3>
      <button class="close-btn" (click)="onClose()">×</button>
    </div>
    
    <div class="modal-body">
      <div class="content-info" *ngIf="contentData()">
        <h4>{{ contentData().title }}</h4>
        <div class="price-info">
          <div *ngIf="contentData().priceEur" class="price">
            <span class="currency">EUR:</span>
            <span class="amount">{{ contentData().priceEur }}&nbsp;€</span>
          </div>
          <div *ngIf="contentData().priceRub" class="price">
            <span class="currency">RUB:</span>
            <span class="amount">{{ contentData().priceRub }}&nbsp;₽</span>
          </div>
        </div>
      </div>

      <div class="payment-methods">
        <h5>Выберите способ оплаты:</h5>
        
        <div class="payment-option" *ngIf="contentData()?.priceEur">
          <label>
            <input 
              type="radio" 
              name="paymentType" 
              value="stripe" 
              [checked]="paymentType() === 'stripe'"
              (change)="onPaymentTypeChange('stripe')"
            />
            <span class="payment-label">
              Stripe (Европа) ({{ contentData().priceEur }}&nbsp;€)
            </span>
          </label>
        </div>

        <div class="payment-option" *ngIf="contentData()?.priceRub">
          <label>
            <input 
              type="radio" 
              name="paymentType" 
              value="yookassa" 
              [checked]="paymentType() === 'yookassa'"
              (change)="onPaymentTypeChange('yookassa')"
            />
            <span class="payment-label">
              ЮКасса (СНГ) ({{ contentData().priceRub }}&nbsp;₽)
            </span>
          </label>
        </div>
      </div>
    </div>

    <div class="modal-footer">
      <button 
        class="btn btn-primary" 
        (click)="onPurchase()" 
        [disabled]="isLoading()"
      >
        <span *ngIf="!isLoading()">Купить</span>
        <span *ngIf="isLoading()">Обработка...</span>
      </button>
      <button class="btn btn-secondary" (click)="onClose()">Отмена</button>
    </div>
  </div>
</div>
