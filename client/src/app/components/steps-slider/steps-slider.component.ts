import { Component, OnInit, On<PERSON><PERSON>roy, HostListener, signal, ElementRef, inject, PLATFORM_ID, viewChild, output, } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { TranslocoPipe } from '@jsverse/transloco';
import { STEP_SLIDER_CONFIG } from './config/step-slider.config';

export interface StepSliderConfig {
  id: number | string,
  title: string,
  subtitle: string,
  description: string,
  question1: string,
  answer1: string,
  question2: string,
  answer2: string,
  stepsTitle: string,
  steps: string[],
  immerseInPracticeUrl: string,
  hearInstructionsUrl: string,
  mobileImgUrl: string,
  tabletImgUrl: string,
  desktopImgUrl: string,
}

@Component({
  selector: 'app-steps-slider',
  standalone: true,
  imports: [CommonModule, TranslocoPipe],
  templateUrl: './steps-slider.component.html',
  styleUrl: './steps-slider.component.scss'
})
export class StepsSliderComponent implements OnInit, OnDestroy {
  private elementRef = inject(ElementRef);
  private platformId = inject(PLATFORM_ID);
  
  readonly stepsConfig = STEP_SLIDER_CONFIG;
  readonly stepOpacities = signal<number[]>(new Array(this.stepsConfig.length).fill(0));
  readonly cardTransforms = signal<string[]>(new Array(this.stepsConfig.length).fill('translateY(0px)'));
  readonly isFixed = signal(false);
  readonly currentSlideIndex = signal(0);
  readonly isMobileScreen = signal<boolean>(false);
  readonly isTabletScreen = signal<boolean>(false);
  readonly selectedStep = signal<StepSliderConfig | null>(null);

  readonly sliderScrolled = output<boolean>();
  readonly skipToNextSection = output<void>();

  detailModal = viewChild<ElementRef<HTMLDialogElement>>('detailModal');

  ngOnInit() {
    this.checkScreenSize();
    
    // первый слайд видимый
    const initialOpacities = new Array(this.stepsConfig.length).fill(0);
    initialOpacities[0] = 1;
    this.stepOpacities.set(initialOpacities);
    
    // позиция карточек
    const initialTransforms = this.stepsConfig.map((_, index) => {
      if (index === 0) return 'translateY(0px)'; // Первая карточка
      return 'translateY(100vh)'; // Остальные снизу экрана
    });
    this.cardTransforms.set(initialTransforms);
    
    setTimeout(() => this.updateStepOpacities(), 100);
  }

  ngOnDestroy() {}

  @HostListener('window:resize')
  checkScreenSize(): void {
    if (isPlatformBrowser(this.platformId)) {
      const currentWindow = window.innerWidth;

      if (currentWindow <= 920 && currentWindow > 650) {
        this.isTabletScreen.set(true);
        this.isMobileScreen.set(false);
      } else if (currentWindow <= 650) {
        this.isMobileScreen.set(true);
        this.isTabletScreen.set(false);
      } else {
        this.isTabletScreen.set(false);
        this.isMobileScreen.set(false);
      }
    }
  }

  @HostListener('window:scroll', ['$event'])
  onScroll() {
    this.updateStepOpacities();
  }

  private updateStepOpacities() {
    const windowHeight = window.innerHeight;
    const scrollTop = window.pageYOffset;
    const containerElement = this.elementRef.nativeElement;
    const containerTop = containerElement.offsetTop;
    const containerHeight = containerElement.offsetHeight;
    const containerBottom = containerTop + containerHeight;
    
    // Определяем, находится ли скролл в области слайдера
    const isInSliderArea = scrollTop >= containerTop && scrollTop <= containerBottom - windowHeight;
    
    // Эмитим событие о том, что пользователь проскролил слайдер
    const hasScrolledPastSlider = scrollTop > containerBottom - 100; // Немного раньше конца слайдера
    this.sliderScrolled.emit(hasScrolledPastSlider);
    
    if (!isInSliderArea) {
      // Если мы не в области слайдера, убираем fixed позиционирование
      this.isFixed.set(false);
      
      if (scrollTop < containerTop) {
        // До слайдера - показываем первый слайд
        const newOpacities = new Array(this.stepsConfig.length).fill(0);
        newOpacities[0] = 1;
        this.stepOpacities.set(newOpacities);
        this.currentSlideIndex.set(0);
        
        // Сбрасываем позиции карточек
        const resetTransforms = this.stepsConfig.map((_, index) => {
          if (index === 0) return 'translateY(0px)';
          return 'translateY(100vh)';
        });
        this.cardTransforms.set(resetTransforms);
      } else {
        // После слайдера - показываем последний слайд
        const newOpacities = new Array(this.stepsConfig.length).fill(0);
        newOpacities[this.stepsConfig.length - 1] = 1;
        this.stepOpacities.set(newOpacities);
        this.currentSlideIndex.set(this.stepsConfig.length - 1);
        
        // Устанавливаем финальные позиции карточек
        const finalTransforms = this.stepsConfig.map((_, index) => {
          if (index === this.stepsConfig.length - 1) return 'translateY(0px)';
          return 'translateY(-100vh)';
        });
        this.cardTransforms.set(finalTransforms);
      }
      return;
    }

    // Включаем fixed позиционирование когда в области слайдера
    this.isFixed.set(true);
    
    // Вычисляем прогресс внутри слайдера
    const sliderProgress = (scrollTop - containerTop) / (containerHeight - windowHeight);
    const slideTransition = Math.min(sliderProgress * (this.stepsConfig.length - 1), this.stepsConfig.length - 1);
    const currentSlideIndex = Math.floor(slideTransition);
    const transitionProgress = slideTransition - currentSlideIndex;
    
    this.currentSlideIndex.set(currentSlideIndex);
    
    // Вычисляем opacity и transform для каждого слайда
    const newOpacities = new Array(this.stepsConfig.length).fill(0);
    const newTransforms = new Array(this.stepsConfig.length).fill('translateY(100vh)');
    
    this.stepsConfig.forEach((_, index) => {
      if (index === currentSlideIndex) {
        // Текущий слайд
        if (index === this.stepsConfig.length - 1) {
          // Последний слайд остается видимым
          newOpacities[index] = 1;
          newTransforms[index] = 'translateY(0px)';
        } else {
          // Текущий слайд исчезает
          const opacity = Math.max(0, 1 - transitionProgress);
          newOpacities[index] = opacity;
          
          // Карточка движется вверх пропорционально исчезновению слайда
          const moveUpDistance = windowHeight * transitionProgress;
          newTransforms[index] = `translateY(-${moveUpDistance}px)`;
        }
      } else if (index === currentSlideIndex + 1 && index < this.stepsConfig.length) {
        // Следующий слайд появляется
        const opacity = Math.min(1, transitionProgress);
        newOpacities[index] = opacity;
        
        // Карточка движется снизу к центру пропорционально появлению слайда
        const moveFromBottom = windowHeight * (1 - transitionProgress);
        newTransforms[index] = `translateY(${moveFromBottom}px)`;
      } else if (index < currentSlideIndex) {
        // Предыдущие слайды - полностью прошли
        newOpacities[index] = 0;
        newTransforms[index] = `translateY(-${windowHeight}px)`;
      } else {
        // Будущие слайды - ждут снизу
        newOpacities[index] = 0;
        newTransforms[index] = `translateY(${windowHeight}px)`;
      }
    });

    this.stepOpacities.set(newOpacities);
    this.cardTransforms.set(newTransforms);
  }

  getStepOpacity(index: number): number {
    return this.stepOpacities()[index] || 0;
  }

  getCardTransform(index: number): string {
    return this.cardTransforms()[index] || 'translateY(0px)';
  }

  getStepClasses(index: number): string {
    const classes = ['step'];
    if (this.isFixed()) {
      classes.push('fixed');
    }
    return classes.join(' ');
  }

  getBackgroundImage(step: StepSliderConfig): string {
    if (this.isMobileScreen()) {
      return `url('${step.mobileImgUrl}')`;
    } else if (this.isTabletScreen()) {
      return `url('${step.tabletImgUrl}')`;
    } else {
      return `url('${step.desktopImgUrl}')`;
    }
  }

  openDetailDialog(): void {
    const currentIndex = this.currentSlideIndex();
    const currentStep = this.stepsConfig[currentIndex];
    
    this.selectedStep.set(currentStep);
    
    const modalRef = this.detailModal();
    if (modalRef?.nativeElement) {
      const modal = modalRef.nativeElement;
      modal.showModal();
      
      modal.classList.add('opening');
      
      // Убираем класс анимации после завершения
      setTimeout(() => {
        modal.classList.remove('opening');
      }, 300);
    }
  }

  closeModal(): void {
    const modalRef = this.detailModal();
    if (modalRef?.nativeElement) {
      const modal = modalRef.nativeElement;
      
      modal.classList.add('closing');
      
      setTimeout(() => {
        modal.close();
        modal.classList.remove('closing');
        this.selectedStep.set(null);
      }, 200);
    }
  }

  // Закрытие по нажатию Escape
  @HostListener('keydown.escape')
  onEscapeKey(): void {
    this.closeModal();
  }

  onSkipClick(): void {
    this.skipToNextSection.emit();
  }
}
