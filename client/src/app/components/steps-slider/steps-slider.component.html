<div class="steps-slide-scroller">
  @for(step of stepsConfig; track step.id; let index = $index) {
    <div 
      class="step"
      [class.fixed]="isFixed()"
      [attr.data-step-index]="index"
      [style.backgroundImage]="getBackgroundImage(step)"
      [style.opacity]="getStepOpacity(index)"
      [style.z-index]="stepsConfig.length - index"
      [style.pointer-events]="getStepOpacity(index) > 0.5 ? 'auto' : 'none'"
    >
      <div class="bluer"></div>
      <div class="step-inner relative">
        <h2>{{ 'lending-steps.slider.title' | transloco }}</h2>
        <div
          class="step-card"
          [style.transform]="getCardTransform(index)"
        >
          <div class="title">
            {{ step.title | transloco }}
          </div>
          <div class="subtitle">
            {{ step.subtitle | transloco }}
          </div>
          <div class="description">
            {{ step.description | transloco }}
          </div>
          <a class="link" (click)="openDetailDialog()">
            {{ 'lending-steps.link.more' | transloco }}
          </a>
        </div>
        <div class="skip-button" (click)="onSkipClick()">
          <img src="../../../assets/images/lending-steps/skip-button.webp" alt="skip arrow">
        </div>
      </div>
    </div>
  }
</div>

<!-- Модальное окно -->
<dialog #detailModal class="detail-modal stylized_">
  <div class="modal-content">
    <div class="modal-header">
      <h3>{{ selectedStep()?.title | transloco }}</h3>
      <button class="close-btn" (click)="closeModal()" aria-label="Закрыть">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M6 6L17.3137 17.3137" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M6 17L17.3137 5.68629" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
    </div>

    <div class="modal-body">
      @if (selectedStep()) {
        <div class="step-info">
          <div class="subtitle">
            {{ selectedStep()!.subtitle | transloco }}
          </div>

          <div class="description">
            {{ selectedStep()!.description | transloco }}
          </div>

          <div class="divider"></div>

          <!-- = -->
          <div class="question1-title">
            <img src="../../../assets/images/icons/lending-steps/question.svg" alt="question">
            {{ selectedStep()!.question1 | transloco }}
          </div>
          <div class="question1-answer">
            {{ selectedStep()!.answer1 | transloco }}
          </div>
          <!-- = -->
          <div class="question2-title">
            <img src="../../../assets/images/icons/lending-steps/yes.svg" alt="yes">
            {{ selectedStep()!.question2 | transloco }}
          </div>
          <div class="question2-answer">
            {{ selectedStep()!.answer2 | transloco }}
          </div>

          <!-- = -->

          <div class="steps-title">
            <img src="../../../assets/images/icons/lending-steps/steps.svg" alt="steps">
            {{ 'lending-steps.modal.practical_steps' | transloco }}
          </div>
          <div class="steps-subtitle">
            {{ selectedStep()!.stepsTitle | transloco }}
          </div>
          <ul class="steps-list">
            @for (stepItem of selectedStep()!.steps; track stepItem) {
              <li>{{ stepItem | transloco }}</li>
            }
          </ul>

          <!-- = -->

          <div class="actions-section">
              <a
                href="{{ selectedStep()!.immerseInPracticeUrl }}"
                class="action-link"
                target="_blank"
              >
              <img src="../../../assets/images/icons/lending-steps/book.svg" alt="book">

              {{ 'lending-steps.modal.immerse_in_practice' | transloco }}
            </a>

            <a
            href="{{ selectedStep()!.hearInstructionsUrl }}"
            class="action-link"
            target="_blank"
            >
              <img src="../../../assets/images/icons/lending-steps/hear.svg" alt="hear">
                {{ 'lending-steps.modal.hear_instructions' | transloco }}
              </a>
          </div>
        </div>
      }
    </div>
  </div>
  
  <!-- Backdrop для закрытия по клику вне модального окна -->
  <div class="modal-backdrop" (click)="closeModal()"></div>
</dialog>