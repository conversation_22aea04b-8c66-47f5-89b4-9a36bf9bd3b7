@import '../../../assets/styles/new-typography';
@import '../../../assets/styles/new-palette';

.steps-slide-scroller {
    position: relative;
    // Создаем высоту для скролла (16 слайдов * высота экрана)
    height: calc(100vh * 16);

    .step {
        height: 100vh;
        background-size: cover;
        background-position-x: left;
        background-position-y: top;
        background-repeat: no-repeat;
        width: 100%;
        position: sticky; // По умолчанию sticky
        top: 0;
        left: 0;
        right: 0;
        display: flex;
        // align-items: center;
        // gap: 52px;
        transition: opacity 0.1s ease-out;
        will-change: opacity;
        z-index: 1;
        
        // Применяем fixed позиционирование только когда нужно
        &.fixed {
            position: fixed;
        }
        .step-inner {
            max-width: 1500px;
            width: 100%;
            padding: 52px 45px 52px 52px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            gap: 52px;
            h2 {
                @include font-base;
                font-size: 60px;
                line-height: 80px;
                max-width: 590px;
                text-align: center;
                color: main(50);
                align-self: flex-end;
                margin: 0 auto 0 0;
                position: sticky;
            }
    
            .step-card {
                display: flex;
                flex-direction: column;
                justify-content: center;
                gap: 24px;
                width: 450px;
                min-width: 450px;
                height: 403px;
                margin: 0 0 0 auto;
                padding: 70px 24px 24px 24px;
                background: url('../../../assets/images/lending-steps/step-card.webp') no-repeat center center;
                background-size: contain;
                transition: transform 0.1s ease-out; // Плавное движение карточки
                will-change: transform;
    
                .title {
                    @include h4;
                    text-align: center;
                    color: main(500);
                    display: -webkit-box;
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
    
                .subtitle {
                    @include h4;
                    text-align: center;
                    color: main(600);
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
    
                .description {
                    @include body-1;
                    color: main(600);
                    display: -webkit-box;
                    -webkit-line-clamp: 4;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
    
                .link {
                    @include subtitle-2;
                    color: main(700);
                    text-decoration: none;
                    margin: 0 auto;
                    transition: color 0.2s ease-in-out;
                    cursor: pointer;
    
                    &:hover {
                        color: main(500);
                    }
                }
            }
            .skip-button {
                position: absolute;
                bottom: 20px;
                right: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 20px;
                height: 70px;
                cursor: pointer;
                @media (max-width: 1100px) {
                    top: 87px;
                    bottom: auto;
                }
                @media (max-width: 650px) {
                    top: 105px;
                    height: 40px;
                    img {
                        height: 40px;
                    }
                }
            }
        }

        .bluer {
            background: url('../../../assets/images/lending-steps/Bluer.webp') no-repeat top center;
            width: 100%;
            height: 553px;
            // max-height: 50%;
            position: absolute;
            bottom: 0;
            left: 0;
            z-index: 0;
            background-size: cover;
            max-height: 100%;

        }

    }
}

@media (max-width: 1100px) {
    .steps-slide-scroller {
        .step {
            .step-inner {
                padding: 52px 52px 85px;
                flex-direction: column;
                align-items: center;
                justify-content: space-between;
                h2 {
                    @include h2;
                    max-width: 100%;
                    margin: 0 auto;
                    position: sticky;
                    top: 100px;
                }
    
                .step-card {
                    transform: translateY(0) !important; // Сбрасываем движение для планшетов
                    margin: 0;
                }
            }

            .bluer {
                background: url('../../../assets/images/lending-steps/Bluer-tablet.webp') no-repeat bottom center;
                bottom: auto;
                height: 663px;
                top: 0;
                background-size: cover;
            }

        }
    }
}

@media (max-width: 920px) {
    .steps-slide-scroller {
        .step {
            background-position-x: center;
        }
    }
}

@media (max-width: 650px) {
    .steps-slide-scroller {
        .step {
            background-position-x: center;
            .step-inner {
                padding: 50px 16px;
                flex-direction: column;
                align-items: center;
                justify-content: space-between;
                h2 {
                    @include subtitle-1;
                    max-width: 320px;
                    margin: 0 auto 0 0;
                    text-align: start;
                    position: sticky;
                    top: 100px;
                    max-width: 300px;
                    // text-shadow: 3px -1px 2px #532E00;
                }
    
                .step-card {
                    width: 343px;
                    height: 260px;
                    min-width: 343px;
                    gap: 16px;
                    background: url('../../../assets/images/lending-steps/step-card-mobile.webp') no-repeat center center;
    
                    .title {
                        @include subtitle-4
                    }
    
                    .subtitle {
                        @include subtitle-4
                    }
    
                    .description {
                        @include button-5;
                    }
    
                    .link {
                        @include button-4;
                    }
                }
            }

            .bluer {
                background: url('../../../assets/images/lending-steps/Bluer-mobile.webp') no-repeat bottom center;
                background-size: cover;
                height: 362px
            }

        }
    }
}

@media (max-width: 500px) {
    .steps-slide-scroller {
        .step {
            .step-inner { 
                padding: 50px 16px 120px;
            }
        }
    }
}

// Стили для модального окна
.detail-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: transparent;
    border: none;
    padding: 0;
    margin: auto;
    z-index: 9999;
    overflow: hidden;

    &[open] {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    &::backdrop {
        background: #190E01E0;
        backdrop-filter: blur(10px);
    }

    .modal-backdrop {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: transparent;
        z-index: 1;
    }

    .modal-content {
        position: relative;
        background: main(50);
        border-radius: 20px;
        max-width: 700px;
        max-height: 90vh;
        height: 860px;
        margin: 0 auto;
        box-shadow: 0px 2px 5.6px 0px #A3A3A340;
        z-index: 2;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        padding: 10px;
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30px 30px 20px;

        h3 {
            @include subtitle-3;
            color: main(400);
            margin: 0;
        }

        .close-btn {
            background: none;
            border: none;
            cursor: pointer;
            padding: 0;
            border-radius: 0;
            transition: opacity 0.2s ease-in-out;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 1;
            position: sticky;
            transform: translate(75%, -75%);

            &:hover {
                opacity: 0.7;
            }
        }
    }

    .modal-body {
        overflow-y: auto;
        flex: 1;
        padding: 0 30px 30px;

        &::-webkit-scrollbar {
            width: 10px;
        }

        &::-webkit-scrollbar-track {
            background: #FFE6AE;
        }

        &::-webkit-scrollbar-thumb {
            background: #BA7A2C;
            border-radius: 5px;
        }

        &::-webkit-scrollbar-thumb:hover {
            background: var(--text-color);
        }

        &::-webkit-scrollbar:horizontal {
            height: 6px;
        }

        &::-webkit-scrollbar-thumb:horizontal {
            border-radius: 5px;
        }

        .step-info {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .subtitle {
            @include subtitle-2;
            color: main(600);
            padding-bottom: 8px;
        }

        .description {
            @include body-2;
            color: main(600);
        }

        .divider {
            width: 100%;
            height: 1px;
            background: main(300);
            margin: 8px 0;
        }

        .question1-title {
            @include body-2;
            color: main(600);
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .question1-answer {
            @include body-2;
            color: main(600);
            margin-bottom: 16px;
        }

        .question2-title {
            @include body-2;
            color: main(500);
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .question2-answer {
            @extend .question1-answer;
        }

        .steps-title {
            @include body-2;
            color: main(500);
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .steps-subtitle {
            @include body-2;
            color: main(600);
        }

        .steps-list {
            margin: 0;
            padding: 0 0 0 8px;
            list-style: none;

            li {
                @include body-2;
                color: main(600);
                padding: 10px 0;
                position: relative;
                padding-left: 18px;

                &:before {
                    content: '•';
                    color: main(500);
                    position: absolute;
                    left: 0;
                    top: 12px;
                    font-weight: bold;
                }
            }
        }


        .actions-section {
            display: flex;
            gap: 40px;
            flex-wrap: wrap;
            width: fit-content;
            margin: 16px auto 0;

            .action-link {
                @include body-2;
                color: main(600);
                text-decoration: none;
                text-align: center;
                transition: opacity 0.2s ease-in-out;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: 8px;

                &:hover {
                    opacity: 0.7;
                }
            }
        }

        // Анимации
        &.opening {
            animation: modalFadeIn 0.3s ease-out;

            .modal-content {
                animation: modalSlideIn 0.3s ease-out;
            }
        }

        &.closing {
            animation: modalFadeOut 0.2s ease-in;

            .modal-content {
                animation: modalSlideOut 0.2s ease-in;
            }
        }
    }
}

// Анимации
@keyframes modalFadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes modalFadeOut {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
    }
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px) scale(0.95);
        opacity: 0;
    }

    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

@keyframes modalSlideOut {
    from {
        transform: translateY(0) scale(1);
        opacity: 1;
    }

    to {
        transform: translateY(-50px) scale(0.95);
        opacity: 0;
    }
}

// Responsive для модального окна
@media (max-width: 650px) {
    .detail-modal {
        .modal-content {
            max-width: 700px;
            max-height: 95vh;
            height: 95vh;
            padding: 10px;
        }

        .modal-header {
            padding: 30px 20px 20px;
        }

        .modal-body {
            padding: 0 20px 20px;

            .subtitle {
                @include subtitle-3;
                padding-bottom: 0;
            }

            .description {
                @include button-4;
            }

            .divider {
                margin: 4px 0;
            }

            .question1-answer {
                @include body-3;
                margin-bottom: 8px;
            }

            .question2-answer {
                @extend .question1-answer;
            }

            .steps-subtitle {
                @include body-3;
            }

            .steps-list {
                padding: 0 0 0 4px;

                li {
                    @include body-3;
                    padding: 4px 0;
                    padding-left: 10px;

                    &:before {
                        content: '•';
                        color: main(500);
                        position: absolute;
                        left: 0;
                        top: 4px;
                        font-weight: bold;
                    }
                }
            }

            .actions-section {
                flex-direction: column;

                .action-linl {
                    @include body-3;
                }
            }
        }
    }
}