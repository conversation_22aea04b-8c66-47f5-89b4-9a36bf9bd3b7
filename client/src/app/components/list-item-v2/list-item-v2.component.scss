@import '../../../assets/styles/new-palette';
@import '../../../assets/styles/new-typography';


.card-container {
    max-width: 930px;
    margin: auto;
    color: #3d2b1f;
    width: 100%;
    display: flex;
    gap: 30px;

    @media (max-width: 768px) {
        max-width: 700px;
        gap: 20px;
    }

    @media (max-width: 640px) {
        flex-direction: column;
        max-width: 500px;
        gap: 10px;
    }

    .main-info-section {
        flex: 1;

        .card-header {
            display: flex;
            align-items: flex-start;
            gap: 30px;

            @media (max-width: 768px) {
                gap: 20px;
            }

            @media (max-width: 640px) {
                gap: 10px;
            }

            .img-block {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 6px;
                position: relative;

                @media (max-width: 640px) {
                    gap: 4px;
                }

                .subscription-label {
                    @include caption-2;
                    color: main(600);

                    @media (max-width: 640px) {
                        @include caption-3;
                    }
                }

                .item-img {
                    width: 90px;
                    height: 90px;
                    position: relative;

                    &.by-subscription {
                        img {
                            &.item-img {
                                opacity: 0.2;

                            }

                        }
                    }

                    @media (max-width: 640px) {
                        width: 72px;
                        height: 72px;
                    }

                    img {
                        position: absolute;
                        z-index: 1;

                        &.item-img {
                            width: 90px;
                            height: 90px;
                            object-fit: cover;
                            object-position: center;

                            @media (max-width: 640px) {
                                width: 72px;
                                height: 72px;
                            }
                        }

                    }

                    .img-mask {
                        background: url(../../../assets/images/main-v2/head-menu-mask.webp) no-repeat center;
                        background-size: contain;
                        width: 93px;
                        height: 92px;
                        top: -1px;
                        left: -2px;
                        position: absolute;
                        z-index: 2;

                        @media (max-width: 640px) {
                            width: 75px;
                            height: 74px;
                        }
                    }

                    .by-subscription-img {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        width: 40px;
                        height: 40px;
                        z-index: 3;

                        @media (max-width: 640px) {
                            width: 32px;
                            height: 32px;
                        }
                    }
                }
            }

            .meta-info {
                .title {
                    @include subtitle-1;
                    color: main(600);
                    margin-bottom: 24px;
                    cursor: pointer;
                    transition: color 0.2s ease-in-out;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;

                    &:hover {
                        color: main(400);
                    }

                    @media (max-width: 640px) {
                        @include subtitle-4;
                        margin-bottom: 0;
                    }
                }

                .meta {
                    display: flex;
                    @media (max-width: 640px) {
                        display: none;
                    }
                }
            }

        }
    }

    .description {
        @include body-1;
        color: main(600);
        margin-top: 24px;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;

        @media (max-width: 640px) {
            @include body-3;
            margin-top: 16px;
            -webkit-line-clamp: 4;
        }
    }

    .meta {
        display: none;

        @media (max-width: 640px) {
            display: flex;
            margin-top: 8px;
        }
    }

    .tags {
        margin-top: 32px;
        display: flex;
        flex-wrap: wrap;
        gap: 24px;

        .tag {
            @include caption-1;
            color: main(400);
            cursor: pointer;
            transition: color 0.2s ease;

            &:hover {
                color: main(600);
            }

            @media (max-width: 640px) {
                @include caption-4;
            }
        }

        @media (max-width: 640px) {
            margin-top: 16px;
            gap: 16px;
            @include caption-3;
        }
    }

    .actions {
        display: flex;
        gap: 14px;
        margin-top: 32px;
        align-items: center;

        @media (max-width: 640px) {
            margin-top: 16px;
        }

        .btn {
            display: flex;
            align-items: center;
            gap: 6px;
            background: main(200);
            border: solid 1px main(250);
            width: 100px;
            height: 34px;
            border-radius: 6px;
            cursor: pointer;
            color: main(600);
            @include caption-1;
            transition: opacity 0.2s ease-in-out;
            opacity: 1;
            justify-content: center;

            &:hover {
                opacity: 0.7;
            }

            @media (max-width: 640px) {
                @include caption-4;
                width: 64px;
                height: 24px;
                gap: 4px;
            }

            img {
                width: 18px;
                height: 18px;

                @media (max-width: 640px) {
                    width: 12px;
                    height: 12px;
                }
            }
        }
    }

    .actions-section {
        display: flex;
        flex-direction: column;
        gap: 28px;
        align-items: center;
        @include caption-1;
        color: main(400);
        min-width: fit-content;
        min-width: fit-content;
        flex: 0 1 0;
        padding: 13px 0;

        @media (max-width: 640px) {
            flex-direction: row;
            padding: 0;
            margin-top: 16px;
        }


        .menu {
            position: relative;

            @media (max-width: 640px) {
                order: 2;
                margin-left: auto;
            }

            .menu-toggle {
                cursor: pointer;
                transition: opacity 0.2s ease-in-out;
                opacity: 1;

                img {
                    min-width: 24px;
                }

                &:hover {
                    opacity: 0.7;
                }
            }

            .dropdown-menu {
                position: absolute;
                top: 100%;
                right: 0;
                background: main(100);
                border-radius: 8px;
                width: 120px;
                min-width: fit-content;
                z-index: 1000;
                overflow: hidden;
                margin-top: 8px;
                backdrop-filter: blur(20px);
                box-shadow: 0px 0px 4.4px 0px #C3BDB533;
                border: 1px solid main(250);
                padding: 12px 0;
                display: flex;
                flex-direction: column;
                gap: 8px;

                .dropdown-item {
                    padding: 12px;
                    cursor: pointer;
                    display: flex;
                    @include button-4;
                    color: main(700);
                    transition: color 0.2s ease-in-out;
                    gap: 8px;

                    &:hover {
                        color: main(500);
                    }

                    span {
                        white-space: nowrap;
                    }
                }
            }
        }

        .likes {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            transition: opacity 0.2s ease-in-out;
            opacity: 1;
            @include caption-1;

            @media (max-width: 640px) {
                @include caption-3;
                flex-direction: row;
                gap: 6px;
            }

            img {
                min-width: 24px;
                min-height: 24px;
                cursor: pointer;
            }

            &:hover {
                opacity: 0.7;
            }

            @media (max-width: 640px) {
                order: 3;
                min-width: 38px;
            }
        }

        .play-toggle {
            cursor: pointer;
            transition: opacity 0.2s ease-in-out;
            opacity: 1;

            img {
                min-width: 24px;
            }

            &:hover {
                opacity: 0.7;
            }

            @media (max-width: 640px) {
                order: 1;
            }
        }
    }
}


.meta {
    @include body-2;
    line-height: 23px;
    color: main(500);
    display: flex;
    flex-wrap: wrap;
    align-self: center;
    gap: 8px;

    @media (max-width: 768px) {
        @include body-3;
    }

    @media (max-width: 640px) {
        gap: 10px;
        @include caption-3;
    }

    .dot {
        color: main(250);
    }

    .meta-item {
        display: flex;
        gap: 6px;

        @media (max-width: 768px) {
            align-items: center;
        }

        img {
            width: 20px;
            height: 20px;

            @media (max-width: 768px) {
                width: 18px;
                height: 18px;
            }

            @media (max-width: 640px) {
                display: none !important;
            }
        }
    }
}