<div class="card-container">
    <div class="main-info-section">
        <div class="card-header">
            <div class="img-block">
                <div class="item-img" [ngClass]="{'by-subscription': formatedData()?.paid}">
                    <img
                        class="item-img"
                        [src]="displayImageUrl()"
                        (error)="onImageError()"
                        alt="item image"
                    />
                    <div class="img-mask"></div>
                    @if(formatedData()?.paid) {
                    <img class="by-subscription-img" src="../../../assets/images/item-list-v2/crown.svg"
                        alt="by subscription" />
                    }
                </div>
                @if(formatedData()?.paid) {
                <div class="subscription-label">По подписке</div>
                }
            </div>

            <div class="meta-info">
                <h2 class="title" (click)="navToItemDetails.emit()">{{ formatedData()?.title }}</h2>
                <ng-container *ngTemplateOutlet="metaInfo"></ng-container>
            </div>


        </div>
        <ng-container *ngTemplateOutlet="metaInfo"></ng-container>
        <p class="description">{{ formatedData()?.description }}</p>

        @if(formatedData()?.tags?.length) {
        <div class="tags">
            <span *ngFor="let tag of formatedData().tags" class="tag" (click)="onTagClick.emit(tag); $event.stopPropagation()">{{ tag?.name }}</span>
        </div>
        }

        <div class="actions">
            @if (showAudioOptions()) {
                <button class="btn" (click)="onAudioClick.emit()">
                    <img src="../../../assets/images/item-list-v2/Audio.svg" alt="audio" />
                    Аудио
                </button>
            }
            @if (showVideoButton()) {
                <button class="btn" (click)="onVideoClick.emit()">
                    <img src="../../../assets/images/item-list-v2/Video.svg" alt="video" />
                    Видео
                </button>
            }
            @if (showTextButton()) {
                <button class="btn" (click)="onBookClick.emit()">
                    <img src="../../../assets/images/item-list-v2/Book_Open.svg" alt="text" />
                    Текст
                </button>
            }
        </div>
    </div>

    <div class="actions-section">
        <div class="menu" (clickOutside)="closeMenu()" appClickOutside>
            <div class="menu-toggle" (click)="toggleMenu()">
                <img src="../../../assets/images/item-list-v2/menu.svg" alt="menu" />
            </div>
            @if (isMenuOpen()) {
            <div class="dropdown-menu">
                <div class="dropdown-item" (click)="onShareClick.emit();$event.stopPropagation(); closeMenu()">
                    <ng-container *ngTemplateOutlet="shareIcon"></ng-container>
                    <span>Поделиться</span>
                </div>
                <div class="dropdown-item" (click)="onAddToFavoritesClick.emit();$event.stopPropagation(); closeMenu()">
                    <ng-container *ngTemplateOutlet="menuFavoriteIcon"></ng-container>
                    <span> {{formatedData().inFavourites ? 'Убрать из избранного' : 'Добавить в избранное'}}</span>
                </div>
                @if (showAudioOptions()) {
                    <div class="dropdown-item" (click)="onAddToQueueClick.emit();$event.stopPropagation(); closeMenu()">
                        <ng-container *ngTemplateOutlet="addToQueueIcon"></ng-container>
                        <span>Добавить в очередь</span>
                    </div>
                }
            </div>
            }
        </div>
        <div class="likes">
            <img (click)="onLikeClick.emit()" [src]="likeIcon()" alt="like" />
            <span>{{ likesCount() }}</span>
        </div>
        @if (itemType() === ListItemTypes.AUDIO_LECTURES) {
            <div (click)="onPlayClick.emit()" class="play-toggle">
                <img src="../../../assets/images/item-list-v2/play.svg" alt="play" />
            </div>
        }
    </div>
</div>

<ng-template #metaInfo>
    <div class="meta">
        <span class="meta-item">
            <img src="../../../assets/images/item-list-v2/User.svg" alt="author" />
            {{ formatedData()?.author }}
        </span>
        <span class="dot">•</span>
        <span class="meta-item">
            <img src="../../../assets/images/item-list-v2/Calendar.svg" alt="date" />
            {{ formatedData()?.date }}
        </span>
        <span class="dot">•</span>
        <span class="meta-item">
            <img src="../../../assets/images/item-list-v2/Time.svg" alt="duration" />
            {{ formatedData()?.duration || 0 }}
        </span>
        <span class="dot">•</span>
        <span class="meta-item">
            <img src="../../../assets/images/item-list-v2/Eye.svg" alt="views" />
            {{ formatedData()?.views }} просм.
        </span>
    </div>
</ng-template>
<ng-template #shareIcon>
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M13.5 6C14.7426 6 15.75 4.99264 15.75 3.75C15.75 2.50736 14.7426 1.5 13.5 1.5C12.2574 1.5 11.25 2.50736 11.25 3.75C11.25 4.99264 12.2574 6 13.5 6Z"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        <path
            d="M4.5 11.25C5.74264 11.25 6.75 10.2426 6.75 9C6.75 7.75736 5.74264 6.75 4.5 6.75C3.25736 6.75 2.25 7.75736 2.25 9C2.25 10.2426 3.25736 11.25 4.5 11.25Z"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        <path
            d="M13.5 16.5C14.7426 16.5 15.75 15.4926 15.75 14.25C15.75 13.0074 14.7426 12 13.5 12C12.2574 12 11.25 13.0074 11.25 14.25C11.25 15.4926 12.2574 16.5 13.5 16.5Z"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        <path d="M6.44141 10.1325L11.5639 13.1175" stroke="currentColor" stroke-width="2" stroke-linecap="round"
            stroke-linejoin="round" />
        <path d="M11.5564 4.88251L6.44141 7.86751" stroke="currentColor" stroke-width="2" stroke-linecap="round"
            stroke-linejoin="round" />
    </svg>
</ng-template>

<ng-template #menuFavoriteIcon>
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M14.25 15.75L9 12.75L3.75 15.75V3.75C3.75 3.35218 3.90804 2.97064 4.18934 2.68934C4.47064 2.40804 4.85218 2.25 5.25 2.25H12.75C13.1478 2.25 13.5294 2.40804 13.8107 2.68934C14.092 2.97064 14.25 3.35218 14.25 3.75V15.75Z"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
    </svg>

</ng-template>

<ng-template #addToQueueIcon>
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 3.75H2.25" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        <path d="M8.25 9H2.25" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        <path d="M12 14.25H2.25" stroke="currentColor" stroke-width="2" stroke-linecap="round"
            stroke-linejoin="round" />
        <path d="M13.5 6.75V11.25" stroke="currentColor" stroke-width="2" stroke-linecap="round"
            stroke-linejoin="round" />
        <path d="M15.75 9H11.25" stroke="currentColor" stroke-width="2" stroke-linecap="round"
            stroke-linejoin="round" />
    </svg>
</ng-template>