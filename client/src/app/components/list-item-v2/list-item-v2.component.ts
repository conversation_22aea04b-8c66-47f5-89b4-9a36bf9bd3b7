import { ClickOutsideDirective } from '@/directives/clickOutside'
import { environment } from "@/env/environment"
import { ReadingTimePipe } from '@/pipes/reading-time.pipe'
import { CommonModule } from '@angular/common'
import { Component, computed, input, output, signal } from '@angular/core'
import moment from "moment/moment"

export enum ListItemTypes {
  AUDIO_LECTURES = 'audio_lectures',
  BOOK = 'book',
  CATEGORY = 'category'
}

export interface ListItemData {
  id: number | string;
  external_id: string;
  status: string;
  videoStatus: string;
  youtube: string;
  link: string;
  type: keyof typeof ListItemTypes;
  title: string;
  author: string;
  reader: string;
  comment: string;
  description: string;
  date: string;
  duration: string;
  views: number;
  likes_count: number;
  imgUrl: string;
  seo_title: string;
  seo_description: string;
  text: string;
  text_link: string;
  preview: string | null;
  paid: boolean;
  lecture_link: string | null;
  likes: number;
  position: { id: number, time: string }[];
  listened: boolean;
  tags: { id: number, external_id: string, name: string }[];
  liked: boolean;
  listened_count: number;
  inFavourites: boolean;
  audio: any;
  audioFiles: any;
}

@Component({
  selector: 'app-list-item-v2',
  standalone: true,
  imports: [CommonModule, ClickOutsideDirective, ReadingTimePipe],
  templateUrl: './list-item-v2.component.html',
  styleUrl: './list-item-v2.component.scss'
})
export class ListItemV2Component {
  private readingTimePipe = new ReadingTimePipe();

  readonly itemData = input.required<any>();
  readonly itemType = input.required<ListItemTypes>();
  readonly onLikeClick = output<void>();
  readonly onPlayClick = output<void>();
  readonly onAddToQueueClick = output<void>();
  readonly onAddToFavoritesClick = output<void>();
  readonly onShareClick = output<void>();
  readonly onAudioClick = output<void>();
  readonly onVideoClick = output<void>();
  readonly onBookClick = output<void>();
  readonly navToItemDetails = output<void>();
  readonly onTagClick = output<any>();

  readonly isMenuOpen = signal<boolean>(false);
  readonly imageError = signal<boolean>(false);

  readonly ListItemTypes = ListItemTypes;


  readonly defaultImg = computed(() => {
    switch (this.itemType()) {
      case ListItemTypes.AUDIO_LECTURES:
        return '../../../assets/images/clouds.webp';
      case ListItemTypes.BOOK:
        return '../../../assets/images/default-book.jpg';
      case ListItemTypes.CATEGORY:
        return '../../../assets/images/clouds.webp';
      default:
        return '../../../assets/images/clouds.webp';
    }
  });

  readonly formatedData = computed(() => {
    const data = this.itemData();
    console.log(data, 'data');

    switch (this.itemType()) {
      case ListItemTypes.AUDIO_LECTURES:
        return {
          ...data,
          imgUrl: data.preview || this.defaultImg(),
        };
      case ListItemTypes.BOOK:
        return this.formatedBookData();
      case ListItemTypes.CATEGORY:
        return this.formatedCategoryData();
      default:
        return data;
    }
  });

  readonly displayImageUrl = computed(() => {
    if (this.imageError()) {
      return this.defaultImg();
    }
    return this.formatedData()?.imgUrl || this.defaultImg();
  });

  readonly likeIcon = computed(() => {
    const isLiked = this.formatedData()?.liked;
    return isLiked ? '../../../assets/images/item-list-v2/liked.svg' : '../../../assets/images/item-list-v2/like.svg';
  });

  readonly showAudioOptions = computed(() => {
    const data = this.itemData();
    switch (this.itemType()) {
      case ListItemTypes.AUDIO_LECTURES:
        return true;
      case ListItemTypes.BOOK:
        return data?.audio?.length > 0;
      case ListItemTypes.CATEGORY:
        const audioCount = (data?.audio?.length || 0) + (data?.audioFiles?.length || 0);
        return audioCount > 0;
      default:
        return true;
    }
  });

  readonly showVideoButton = computed(() => {
    const data = this.itemData();
    if (this.itemType() === ListItemTypes.AUDIO_LECTURES) {
      return !!data?.youtube;
    }
    return false;
  });

  readonly showTextButton = computed(() => {
    const data = this.itemData();
    if (this.itemType() === ListItemTypes.AUDIO_LECTURES) {
      return !!data?.text_link;
    }
    if (this.itemType() === ListItemTypes.BOOK) {
      return false;
    }
    if (this.itemType() === ListItemTypes.CATEGORY) {
      return false;
    }
    return true;
  });

  readonly likesCount = computed(() => {
    const data = this.formatedData();
    const count = data?.likes || data?.likes_count || 0;
    return count < 0 ? 0 : count;
  });

  toggleMenu() {
    this.isMenuOpen.set(!this.isMenuOpen());
  }

  closeMenu() {
    this.isMenuOpen.set(false);
  }

  onImageError() {
    this.imageError.set(true);
  }

  formatedBookData(): ListItemData {
    const data = this.itemData();

    return {
      ...data,
      description: data.annotation ? data.annotation.substring(0, 630) : '',
      date: moment(data.created_at).format('DD.MM.YYYY'),
      imgUrl: data.image || this.defaultImg(),
    }
  }

  formatedCategoryData(): ListItemData {
    const data = this.itemData();

    return {
      ...data,
      description: this.setCategoryDescription(),
      date: moment(data.created_at).format('DD.MM.YYYY'),
      duration: data.content?.length ? this.readingTimePipe.transform(data.content) : '',
      imgUrl: data.preview?.name ? environment.serverUrl + '/upload/' + data.preview.name : this.defaultImg(),
    }
  }

  setCategoryDescription() {
    const content = this.itemData()?.content;
    if (!content || content.length === 0) return '';

    const textContent = this.stripHtmlTags(content);
    return textContent.substring(0, 630);
  }

  private stripHtmlTags(html: string): string {
    if (!html) return '';

    if (typeof document !== 'undefined') {
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = html;
      return tempDiv.textContent || tempDiv.innerText || '';
    } else {
      return html.replace(/<[^>]*>/g, '').trim();
    }
  }

}
