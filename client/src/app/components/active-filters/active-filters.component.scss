.active-filters-wrap {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  gap: 10px;
  margin: 20px 0 10px 0;
}

.filter-item {
  border-radius: 25px;
  padding: 12px 19px;
  display: flex;
  align-items: center;
  white-space: nowrap;
  background: var(--background-color);
  border: 1px solid var(--book_about);
  font-size: 20px;
  line-height: 24px;
  color: var(--font-color1);
  cursor: default;
}

.filter-item-wrapper {
  border: 1px solid var(--book_about);
  border-radius: 25px;
  margin-right: 10px;
  margin-bottom: 10px;
}

.filter-remove {
  width: 16px;
  height: 16px;
  margin-left: 10px;
  cursor: pointer;
  background: url('assets/images/icons/x_a.svg') no-repeat center;
  background-size: contain;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.filter-remove:hover {
  opacity: 1;
}

@media (max-width: 1024px) {
  .filter-item {
    padding: 7px 19px;
    font-size: 18px;
  }
}

@media (max-width: 768px) {
  .active-filters-wrap {
    flex-wrap: unset;
    overflow-x: auto;
    gap: 8px;
  }

  .filter-item {
    padding: 6px 15px;
    font-size: 15px;
  }

  .filter-item-wrapper {
    margin-right: 8px;
    margin-bottom: 8px;
  }

  .filter-remove {
    width: 12px;
    height: 12px;
    margin-left: 8px;
  }
}

@media (max-width: 480px) {
  .active-filters-wrap {
    width: 87.2%;
  }
}
