{"menuLinksArrays": {"tradition": [{"imgUrl": "https://sanatanadharma.world//upload/content/1761389975________________.webp", "nameCode": "header.dropdown.tradition.sanatana_dharma", "description": "Сана<PERSON><PERSON><PERSON> Дхарма", "link": "categories/60/sanatana-dharma", "descriptionCode": "header.dropdown.tradition.sanatana_dharma.description"}, {"imgUrl": "https://sanatanadharma.world//upload/content/1761469294________________2.webp", "nameCode": "header.dropdown.tradition.lineage", "description": "Линия передачи", "link": "categories/60/liniya-peredachi", "descriptionCode": "header.dropdown.tradition.lineage.description"}, {"imgUrl": "/assets/images/main-v2/default-pictures/Sculpture_of_<PERSON><PERSON><PERSON>_in_Berlin_Museum.jpg", "nameCode": "header.dropdown.tradition.guru", "description": "<PERSON><PERSON><PERSON><PERSON>", "link": "categories/60/guru", "descriptionCode": "header.dropdown.tradition.guru.description"}, {"imgUrl": "https://sanatanadharma.world//upload/content/1761474252_creed.webp", "nameCode": "header.dropdown.tradition.creed", "description": "Символ веры", "link": "categories/60/simvol-very", "descriptionCode": "header.dropdown.tradition.creed.description"}, {"imgUrl": "https://sanatanadharma.world//upload/content/1761475072_vosd.webp", "nameCode": "header.dropdown.tradition.vosd", "descriptionCode": "header.dropdown.tradition.vosd.description", "description": "Всемирная община Санатана Дхармы", "link": "categories/60/vsemirnaya-obshina-sanatana-dharmy"}], "education": [{"imgUrl": "https://sanatanadharma.world//upload/content/1760531982_omdram_symbol_om_-ar_9151_-v_7_36de7df4-d75d-4ab8-bc11-f4391673732f_3.webp", "nameCode": "header.dropdown.education.courses", "description": "Курсы", "link": "ru/categories/62", "descriptionCode": "header.dropdown.education.courses.description"}, {"imgUrl": "https://sanatanadharma.world/upload/content/preview_1760533704.webp", "nameCode": "header.dropdown.education.methods_tree", "description": "Дерево методов Учения", "link": "categories/63/derevo-metodov-ucheniya", "descriptionCode": "header.dropdown.education.methods_tree.description"}, {"imgUrl": "https://sanatanadharma.world/upload/content/1760533215_gemini_generated_image_r5qg68r5qg68r5qg.webp", "nameCode": "header.dropdown.education.institute", "description": "Институт Васиштхи", "link": "categories/63/institut-vasishthi", "descriptionCode": "header.dropdown.education.institute.description"}, {"imgUrl": "https://sanatanadharma.world/upload/content/preview_1760533704.webp", "nameCode": "header.dropdown.education.system", "description": "Система обучения", "link": "categories/63/sistema-obucheniya", "descriptionCode": "header.dropdown.education.system.description"}, {"imgUrl": "/assets/images/default.jpg", "nameCode": "header.dropdown.education.become_student", "descriptionCode": "header.dropdown.education.become_student.description", "description": "Как стать учеником?", "link": ""}], "media": [{"imgUrl": "/assets/images/default.jpg", "nameCode": "header.dropdown.media.lectures", "descriptionCode": "header.dropdown.media.lectures.description", "description": "Лекции", "link": ""}, {"imgUrl": "/assets/images/default.jpg", "nameCode": "header.dropdown.media.audiobooks", "descriptionCode": "header.dropdown.media.audiobooks.description", "description": "Аудиокниги", "link": ""}, {"imgUrl": "/assets/images/default.jpg", "nameCode": "header.dropdown.media.video", "descriptionCode": "header.dropdown.media.video.description", "description": "Видео", "link": ""}, {"imgUrl": "/assets/images/default.jpg", "nameCode": "header.dropdown.media.photo", "descriptionCode": "header.dropdown.media.photo.description", "description": "Фото", "link": ""}], "library": [{"imgUrl": "/assets/images/main-v2/default-pictures/Sculpture_of_<PERSON><PERSON><PERSON>_in_Berlin_Museum.jpg", "nameCode": "header.dropdown.library.scriptures", "description": "Священные тексты", "link": "", "descriptionCode": "header.dropdown.library.scriptures.description"}, {"imgUrl": "/assets/images/main-v2/default-pictures/Sculpture_of_<PERSON><PERSON><PERSON>_in_Berlin_Museum.jpg", "nameCode": "header.dropdown.library.guru_books", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>уру", "link": "", "descriptionCode": "header.dropdown.library.guru_books.description"}, {"imgUrl": "/assets/images/main-v2/default-pictures/Sculpture_of_<PERSON><PERSON><PERSON>_in_Berlin_Museum.jpg", "nameCode": "header.dropdown.library.upadeshi", "description": "Упадеши", "link": "", "descriptionCode": "header.dropdown.library.upadeshi.description"}, {"imgUrl": "/assets/images/main-v2/default-pictures/Sculpture_of_<PERSON><PERSON><PERSON>_in_Berlin_Museum.jpg", "nameCode": "header.dropdown.library.advaita-vedanta", "description": "Адвайта-веданта", "link": "", "descriptionCode": "header.dropdown.library.advaita-vedanta.description"}, {"imgUrl": "/assets/images/main-v2/default-pictures/Sculpture_of_<PERSON><PERSON><PERSON>_in_Berlin_Museum.jpg", "nameCode": "header.dropdown.library.kashmir-shaivism", "description": "Кашмирский шиваизм", "link": "", "descriptionCode": "header.dropdown.library.kashmir-shaivism.description"}, {"imgUrl": "/assets/images/main-v2/default-pictures/Sculpture_of_<PERSON><PERSON><PERSON>_in_Berlin_Museum.jpg", "nameCode": "header.dropdown.library.articles", "description": "Статьи", "link": "", "descriptionCode": "header.dropdown.library.articles.description"}], "events": [{"imgUrl": "/assets/images/main-v2/default-pictures/Sculpture_of_<PERSON><PERSON><PERSON>_in_Berlin_Museum.jpg", "nameCode": "header.dropdown.events.calendar", "description": "Календарь событий", "link": "", "descriptionCode": "header.dropdown.events.calendar.description"}, {"imgUrl": "/assets/images/main-v2/default-pictures/Sculpture_of_<PERSON><PERSON><PERSON>_in_Berlin_Museum.jpg", "nameCode": "header.dropdown.events.retreats_online", "description": "он-лайн ритриты", "link": "", "descriptionCode": "header.dropdown.events.retreats_online.description"}, {"imgUrl": "/assets/images/main-v2/default-pictures/Sculpture_of_<PERSON><PERSON><PERSON>_in_Berlin_Museum.jpg", "nameCode": "header.dropdown.events.pilgrimage", "description": "Паломничества", "link": "", "descriptionCode": "header.dropdown.events.pilgrimage.description"}, {"imgUrl": "/assets/images/default.jpg", "nameCode": "header.dropdown.events.seminars", "descriptionCode": "header.dropdown.events.seminars.description", "description": "Семинары", "link": ""}, {"imgUrl": "/assets/images/default.jpg", "nameCode": "header.dropdown.events.holydays", "descriptionCode": "header.dropdown.events.holydays.description", "description": "Праздники", "link": ""}]}}