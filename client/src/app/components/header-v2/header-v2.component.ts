import { ClickOutsideDirective } from "@/directives/clickOutside"
import { environment } from "@/env/environment"
import { AuthService } from '@/services/auth.service'
import { BurgerMenuService } from '@/services/burger-menu.service'
import { ContentService } from "@/services/content.service"
import { HeaderStateService } from "@/services/header-state.service"
import { LibraryService } from '@/services/library.service'
import { ProfileService } from "@/services/profile.service"
import { ShareDataService } from '@/services/share-data.service'
import { ToasterService } from '@/services/toaster.service'
import { CommonModule, DOCUMENT, isPlatformBrowser, NgOptimizedImage } from '@angular/common'
import { Component, computed, effect, ElementRef, EventEmitter, HostListener, Inject, inject, OnDestroy, OnInit, Output, PLATFORM_ID, Renderer2, signal, viewChild } from '@angular/core'
import { ActivatedRoute, NavigationEnd, Router, RouterLink } from "@angular/router"
import { TranslocoPipe, TranslocoService } from "@jsverse/transloco"
import { filter, Subject, takeUntil } from 'rxjs'
import headerBurgerMenuData from './config/header-burger-menu.json'
import headerDropdownMenuData from './config/header-dropdown-menu.json'

export enum MenuLinkMenuKeys {
  tradition = 'tradition',
  education = 'education',
  practice = 'practice',
  media = 'media',
  library = 'library',
  events = 'events',
}

@Component({
  selector: 'app-header-v2',
  standalone: true,
  imports: [RouterLink, CommonModule, NgOptimizedImage, ClickOutsideDirective, TranslocoPipe],
  templateUrl: './header-v2.component.html',
  styleUrl: './header-v2.component.scss'
})
export class HeaderV2Component implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  readonly menuLinksArrays: Partial<Record<MenuLinkMenuKeys, any[]>> = headerDropdownMenuData.menuLinksArrays;
  readonly menuKeys = Object.keys(headerDropdownMenuData.menuLinksArrays) as MenuLinkMenuKeys[];
  readonly isUserMenuOpen = signal<boolean>(false);
  readonly activeLinkMenu = signal<MenuLinkMenuKeys | null>(null);
  readonly isMobileScreen = signal<boolean>(false);
  readonly isLanguageMenuOpen = signal<boolean>(false);
  readonly isThemeMenuOpen = signal<boolean>(false);
  readonly curentWindowInnerWidth = signal<number>(0);
  private readonly burgerMenuContent = viewChild<ElementRef<HTMLElement>>('burgerMenuContent');

  private readonly burgerMenuService = inject(BurgerMenuService);
  readonly isBurgerMenuOpen = this.burgerMenuService.isBurgerMenuOpen;

  selectedLinkMenu = signal<any[]>([]);
  readonly MenuLinkMenuKeys = MenuLinkMenuKeys;
  readonly burgrerMenu = signal(structuredClone(headerBurgerMenuData));
  private isScrollingProgrammatically = false;

  // Helper method to get link name safely
  getLinkName(link: any): string {
    return link.nameCode ? this.translocoService.translate(link.nameCode) : link.name;
  }

  // Helper method to get column name safely
  getColName(col: any): string {
    return col.colNameCode ? this.translocoService.translate(col.colNameCode) : col.colName;
  }

  // searchShow: boolean = false;
  @Output() dailyMenuOpen = new EventEmitter<any>();
  @Output() sideOpen = new EventEmitter<any>();
  @Output() sideClose = new EventEmitter<any>();
  // @Input() isSidebarOpen = false;
  route = inject(ActivatedRoute)
  router = inject(Router);
  contentService = inject(ContentService);
  libraryService = inject(LibraryService);
  translocoService = inject(TranslocoService);
  profileService = inject(ProfileService);
  authService = inject(AuthService);
  headerStateService = inject(HeaderStateService);
  shareDataService = inject(ShareDataService);
  private document = inject(DOCUMENT);
  private renderer = inject(Renderer2);
  currentLanguage = signal<string>('ru');
  environment = environment;
  

  readonly isScrolled = computed(() => {
    return this.headerStateService.isScrolledApp();
  });
  
  readonly isShowMobileMainPlayer = computed(() => {
    const mobilePlayerBrackePoint = 500;
    const currentWidth = this.curentWindowInnerWidth();
    const isFullscreen = this.headerStateService.isMobileMainPlayerFullscreen();

    let hasTracks = false;
    if (isPlatformBrowser(this.platformId)) {
      const playerState = this.shareDataService.loadPlayerState();
      hasTracks = !!(playerState?.queue && playerState.queue.length > 0);
    }

    if (currentWidth === 0) {
      return false;
    }

    return currentWidth <= mobilePlayerBrackePoint && isFullscreen && hasTracks;
  });

  readonly defaultBg = computed(() => {
    const scrolled = this.isScrolled();
    // const isTransparentPage = this.headerStateService.isPageWithInitTransparentHeader();
    const showMobilePlayer = this.isShowMobileMainPlayer();

    if (scrolled) {
      return false;
    }

    // if (!isTransparentPage) {
    //   return true;
    // }

    if (showMobilePlayer) {
      return true;
    }

    return false;
  });


  @HostListener('window:resize')
  checkScreenSize(): void {
     if (isPlatformBrowser(this.platformId)) {
      const curentWindow = window.innerWidth;
      const wasMobile = this.isMobileScreen();
      const isNowMobile = curentWindow <= 680;
      this.curentWindowInnerWidth.set(curentWindow);

      this.isMobileScreen.set(isNowMobile);
      
      // Закрываем меню при любом переходе между мобильным и десктопным экраном
      if (wasMobile !== isNowMobile && this.isBurgerMenuOpen()) {
        this.closeBurgerMenu();
      }

      if (curentWindow <=1100) {
        this.closeLinkMemu();
      }
    }
  }

  constructor(
    @Inject(PLATFORM_ID) private platformId: any,
    private toasterService: ToasterService
  ) {
    effect(() => {
      if (this.authService.token() && !this.profileService.name()) {
        this.profileService.getProfile().subscribe();
      } 
    });
    this.checkScreenSize();
   }

  ngOnInit() {
    this.checkScreenSize();

    // Initialize current language from URL
    const urlLang = this.router.url.split('/')[1];
    console.log('Initial URL:', this.router.url);
    console.log('Initial URL lang:', urlLang);

    if (urlLang && ['ru', 'en', 'de', 'ua', 'it'].includes(urlLang)) {
      this.currentLanguage.set(urlLang);
      this.translocoService.setActiveLang(urlLang);
    } else {
      // Fallback to default language if no valid language in URL
      this.currentLanguage.set('ru');
      this.translocoService.setActiveLang('ru');
    }

    // Listen to route changes to update current language
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd),
      takeUntil(this.destroy$)
    ).subscribe(() => {
      const urlLang = this.router.url.split('/')[1];
      console.log('Route changed, URL:', this.router.url);
      console.log('Route changed, URL lang:', urlLang);

      if (urlLang && ['ru', 'en', 'de', 'ua', 'it'].includes(urlLang)) {
        this.currentLanguage.set(urlLang);
        this.translocoService.setActiveLang(urlLang);
      }
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // toggleSearchPanel() {
  //   this.searchShow = !this.searchShow;
  // }

  toggleLanguageMenu(event: Event) {
    event.stopPropagation();
    this.isLanguageMenuOpen.set(!this.isLanguageMenuOpen());
    this.closeThemeMenu();
  }

  closeLanguageMenu() {
    this.isLanguageMenuOpen.set(false);
  }

  toggleThemeMenu(event: Event) {
    event.stopPropagation();
    this.isThemeMenuOpen.set(!this.isThemeMenuOpen());
    this.closeLanguageMenu();
  }

  closeThemeMenu() {
    this.isThemeMenuOpen.set(false);
  }

  private setHtmlLang(lang: string): void {
    const htmlElement = this.document.documentElement;
    this.renderer.setAttribute(htmlElement, 'lang', lang);
  }

  changeLanguage(lang: string) {
    const currentUrl = this.router.url;
    console.log('Current URL:', currentUrl);
    console.log('Current language:', this.currentLanguage());
    console.log('New language:', lang);

    // More robust URL replacement
    let newUrl: string;
    const urlParts = currentUrl.split('/');

    if (urlParts.length > 1 && ['ru', 'en', 'de', 'ua'].includes(urlParts[1])) {
      // Replace existing language
      urlParts[1] = lang;
      newUrl = urlParts.join('/');
    } else {
      // Add language to URL
      newUrl = `/${lang}${currentUrl}`;
    }

    console.log('New URL:', newUrl);

    this.currentLanguage.set(lang);
    this.setHtmlLang(lang);
    this.translocoService.setActiveLang(lang);
    this.router.navigate([newUrl]);
    this.closeLanguageMenu();
  }

  getCurrentLanguageLabel(): string {
    const currentLang = this.currentLanguage();
    switch (currentLang) {
      case 'ru': return 'Рус';
      case 'en': return 'Eng';
      case 'de': return 'Deu';
      case 'ua': return 'Укр';
      case 'it': return 'Ita';
      default: return 'Eng';
    }
  }

  changeTheme(theme: string) {
    // TODO: Implement theme change logic
    console.log('Theme changed to:', theme);
    this.closeThemeMenu();
  }

  // async changeLanguage(e: any) {
  //   const currentUrl = this.router.url
  //   const newUrl = currentUrl.replace(`/${this.translocoService.getActiveLang()}/`, `/${e.target.value}/`)
  //   this.router.navigate([newUrl])
  // }

  toggleSidebar() {
    this.sideOpen.emit(true);
  }

  navigateToPhoto() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/photo`]);
    this.sideOpen.emit(false);
    this.closeBurgerMenu();
  }

  navigateToAudio() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/audiogallery/audiolektsii`]);
    this.sideOpen.emit(false);
    this.closeBurgerMenu();
  }

  navigateToForum() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/forum`]);
    this.sideOpen.emit(false);
    this.closeBurgerMenu();
  }


  navigateToLibrary() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/library`]);
    this.sideOpen.emit(false);
    this.closeBurgerMenu();
  }

  navigateToMain() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/`]);
    this.sideOpen.emit(false);
    this.closeBurgerMenu();
  }

  navigateToCatigory() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/categories`]);
    this.sideOpen.emit(false);
    this.closeBurgerMenu();
  }

  toggleUserMenu(event: Event) {
    event.stopPropagation();
    this.isUserMenuOpen.set(!this.isUserMenuOpen());
  }

  closeUserMenu() {
    this.isUserMenuOpen.set(false);
  }

  navigateToProfile() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/profile`]);
    this.closeUserMenu();
    this.sideClose.emit(true);
    this.closeBurgerMenu();
  }

  navigateToFavorites() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/profile/favorites`]);
    this.closeUserMenu();
    this.sideClose.emit(true);
    this.closeBurgerMenu();
  }

  navigateToPlaylists() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/profile/playlists`]);
    this.closeUserMenu();
    this.sideClose.emit(true);
    this.closeBurgerMenu();
  }

  navigateToMyData() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/profile/my-data`]);
    this.closeUserMenu();
    this.sideClose.emit(true);
    this.closeBurgerMenu();
  }

  navigateToAnketa() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/anketa`]);
    this.closeUserMenu();
    this.sideClose.emit(true);
    this.closeBurgerMenu();
  }

  navigateToSubscriptions() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/profile/subscriptions`]);
    this.closeUserMenu();
    this.sideClose.emit(true);
    this.closeBurgerMenu();
  }

  navigateToDonation() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/donation/bank-transfer`]);
    this.closeUserMenu();
    this.sideClose.emit(true);
    this.closeBurgerMenu();
  }

  navigateToAIChat() {
    const lang = this.translocoService.getActiveLang();
    this.router.navigate(['/', lang, 'ai-chat']);
    this.closeUserMenu();
    this.sideClose.emit(true);
    this.closeBurgerMenu();
  }
  

  logout() {
    this.authService.logout();
    this.closeUserMenu();
    this.sideClose.emit(true);
    this.closeBurgerMenu();
  }

  getSubscriptionsLabel(): string {
    const key = environment.hideStripe ? 'header.user.purchases' : 'header.user.subscriptions';
    return this.translocoService.translate(key);
  }



  toggleLinkMemu(key: MenuLinkMenuKeys) {
    if (this.activeLinkMenu() === key) {
      this.closeLinkMemu();
    } else {
      this.showLinkMemu(key);
    }
  }

  showLinkMemu(key: MenuLinkMenuKeys) {
    if (!key) return;
    this.activeLinkMenu.set(key)
    this.selectedLinkMenu.set(this.menuLinksArrays[key] || []);
  }

  closeLinkMemu() {
    this.activeLinkMenu.set(null)
    this.selectedLinkMenu.set([]);
  }

  toggleBurgerMenu() {
    this.burgerMenuService.toggleBurgerMenu();
    this.burgrerMenu.set(structuredClone(headerBurgerMenuData));
    this.closeLinkMemu();
  }

  navigateToHome() {
    this.closeLinkMemu();
    this.navigateToMain();
    this.closeBurgerMenu();
  }

  // navigateToForum() {
  //   const lang = this.translocoService.getActiveLang();
  //   this.router.navigate([`/${lang}/anketa`]);
  //   this.closeUserMenu();
  //   this.sideClose.emit(true);
  // }

  selectBurgerMenuCol(section: any) {
    this.burgrerMenu().forEach((section: any) => {
      section.activeSection = false;
    });
    section.activeSection = !section.activeSection;

    if (!this.isMobileScreen()) {
      setTimeout(() => {
        this.scrollToActiveSection();
      }, 0);
    }
  }

  private scrollToActiveSection() {
    const containerRef = this.burgerMenuContent();
    if (!containerRef) return;

    const container = containerRef.nativeElement;
    const activeSection = container.querySelector('section.active') as HTMLElement;

    if (activeSection && container) {
      const containerRect = container.getBoundingClientRect();
      const sectionRect = activeSection.getBoundingClientRect();
      const scrollTop = container.scrollTop + (sectionRect.top - containerRect.top);

      container.scrollTo({
        top: scrollTop,
        behavior: 'smooth'
      });
    }
  }

  onBurgerMenuScroll() {
    if (this.isScrollingProgrammatically || this.isMobileScreen()) return;

    const containerRef = this.burgerMenuContent();
    if (!containerRef) return;

    const container = containerRef.nativeElement;
    const sections = container.querySelectorAll('section');

    if (!sections.length) return;

    const containerRect = container.getBoundingClientRect();
    const scrollOffset = 100; // Offset from top to determine active section

    let activeSection: HTMLElement | null = null;
    let minDistance = Infinity;

    sections.forEach((section: HTMLElement) => {
      const sectionRect = section.getBoundingClientRect();
      const distance = Math.abs(sectionRect.top - containerRect.top - scrollOffset);

      if (distance < minDistance && sectionRect.top <= containerRect.top + scrollOffset) {
        minDistance = distance;
        activeSection = section;
      }
    });

    if (activeSection) {
      const sectionIndex = Array.from(sections).indexOf(activeSection);
      const menuSections = this.burgrerMenu();

      menuSections.forEach((section: any, index: number) => {
        section.activeSection = index === sectionIndex;
      });
    }
  }


  closeBurgerMenu() {
    this.burgerMenuService.closeBurgerMenu();
  }

  navigateToBurgerMenuLink(url: string) {
    this.closeBurgerMenu();
    this.closeLinkMemu();
    if (!url) return;

    this.navigateToUrl(url);
    this.closeUserMenu();
  }

  navigateToDropdownMenuLink(url: string) {
    this.closeLinkMemu();
    if (!url) return;

    this.navigateToUrl(url);
  }

  private navigateToUrl(url: string) {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];

    console.log('Navigating to URL:', url);
    console.log('Base route:', baseRoute);

    // Проверяем, есть ли query параметры в URL
    if (url.includes('?')) {
      const [path, queryString] = url.split('?');
      const queryParams: any = {};

      // Парсим query параметры
      queryString.split('&').forEach(param => {
        const [key, value] = param.split('=');
        queryParams[key] = decodeURIComponent(value);
      });

      console.log('Path:', path);
      console.log('Query params:', queryParams);

      // Определяем полный путь
      const fullPath = path.startsWith('/') ? path : `/${baseRoute}/${path}`;

      // Навигация с query параметрами
      this.router.navigate([fullPath], { queryParams });
    } else {
      // Обычная навигация без query параметров
      const fullPath = url.startsWith('/') ? url : `/${baseRoute}/${url}`;
      console.log('Simple navigation to:', fullPath);
      this.router.navigate([fullPath]);
    }
  }

  toggleBurgerMenuSection(selectedSection: any) {
    if (!this.isMobileScreen()) return;
    this.burgrerMenu().forEach((section: any) => {
      if (selectedSection.sectionNameCode !== section.sectionNameCode) {
        section.activeSection = false;
        section.linkCols.forEach((col: any) => {
          col.activeCol = false;
        });
      }
    });
    selectedSection.activeSection = !selectedSection.activeSection;
    this.closeUserMenu();
  }

  toggleBurgerMenuSectionCol(section: any, col: any) {
    if (!this.isMobileScreen()) return;

    if (!section && section.linkCols?.length) return;
    section.linkCols.forEach((linksCol: any) => {
      if (linksCol.colName !== col.colName) {
        linksCol.activeCol = false;
      }
    });
    col.activeCol = !col.activeCol;
    this.closeUserMenu();
  }

  navToSocialMedia() {
    // TODO: add logic for navigation social media
  }

  showSidebar() {
    this.dailyMenuOpen.emit(true);
  }
}
