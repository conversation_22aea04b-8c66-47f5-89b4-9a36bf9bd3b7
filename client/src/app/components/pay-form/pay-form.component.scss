@use"../../pages/donation/donation.component.scss";

.name-input-wrapper {
  margin-bottom: 30px;
  margin-top: 30px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.name-label {
  font-family: Prata;
  font-weight: 400;
  font-size: 18px;
  line-height: 24px;
  color: var(--font-color);
  text-align: center;
}

.name-input {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  padding: 12px 16px;
  font-family: Prata;
  font-weight: 400;
  font-size: 16px;
  line-height: 22px;
  color: #99601A;
  border: 1px solid #EDC17F;
  border-radius: 10px;
  background-color: #FEF1CF;
  text-align: center;

  &:focus-visible {
    outline: 1px solid #e7b467;
  }

  &::placeholder {
    font-family: Prata;
    font-weight: 400;
    font-size: 14px;
    color: #99601A;
    opacity: 0.7;
  }
}

.custom-amount-input {
  box-sizing: border-box;
  min-width: 210px;
  width: 210px;
  height: 56px;
  font-family: Prata;
  font-weight: 400;
  font-size: 16px;
  line-height: 22px;
  color: #99601A;
  border: 1px solid #EDC17F;
  border-radius: 10px;
  background-color: #FEF1CF;
  margin-left: 10px;
  margin-top: 0;
  margin-bottom: 0;
  padding: 0 12px;
  text-align: center;
  vertical-align: middle;
  align-self: center;

  &:focus,
  &:focus-visible {
    outline: none;
    border-color: #e7b467;
  }

  &::placeholder {
    font-family: Prata;
    font-weight: 400;
    font-size: 14px;
    color: #99601A;
    opacity: 0.7;
  }

  // Убираем стрелочки вверх/вниз для input type="number"
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  // Для Firefox
  -moz-appearance: textfield;
}

.custom-radio-other {
  display: flex;
  align-items: center;
  gap: 0;
}

.custom-amount-wrapper {
  display: flex;
  align-items: center;
  min-width: 220px; // 210px + 10px margin-left
  width: 220px;
  height: 56px; // Фиксированная высота, чтобы не было смещения
  min-height: 56px;
}

// Responsive design for payment form
@media (max-width: 768px) {
  .tot_wrap {
    font-size: 18px;
    line-height: 24px;
    margin: 50px 0 30px 0;
  }

  textarea {
    width: 90%;
    max-width: 600px;
  }

  .syst_wrap {
    flex-direction: column;
    gap: 20px;
  }

  .custom-radio-group {
    width: auto !important;
    max-width: 90%;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: flex-start !important;
    margin: 0 auto;
  }
}

@media (max-width: 600px) {
  .tot_wrap {
    font-size: 16px;
    line-height: 22px;
    margin: 40px 0 25px 0;
  }

  textarea {
    width: 95%;
    height: 120px;
    font-size: 15px;
    line-height: 20px;
    padding: 15px 10px;
  }

  .custom-radio-group {
    width: auto !important;
    max-width: 95%;
    gap: 12px;
    margin: 0 auto;
  }

  .custom-radio-label {
    font-size: 16px !important;
    line-height: 20px !important;
  }

  .custom-amount-wrapper {
    min-width: 208px; // 200px + 8px margin-left
    width: 208px;
    height: 48px;
    min-height: 48px;
  }

  .custom-amount-input {
    min-width: 200px;
    width: 200px;
    height: 48px;
    font-size: 14px;
    margin-left: 8px;
    padding: 0 10px;
  }

  .radio-value.rdr {
    min-width: 200px;
    width: 200px;
    height: 48px;
    font-size: 13px;
    margin-left: 8px;
    padding: 0 8px;
    line-height: 17px;
    white-space: normal;
    word-wrap: break-word;
  }

  .curr_sw_wrap {
    margin-left: 0;
    margin-bottom: 15px;
    display: flex;
    justify-content: center;
  }

  .currcy_switch {
    right: auto;
    left: 50%;
    transform: translateX(-50%);
  }
}

@media (max-width: 450px) {
  .tot_wrap {
    font-size: 15px;
    line-height: 20px;
    margin: 35px 0 20px 0;
  }

  textarea {
    width: 100%;
    height: 110px;
    font-size: 14px;
    line-height: 18px;
    padding: 12px 8px;
  }

  .custom-radio-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    width: auto !important;
    max-width: fit-content;
    margin: 0 auto;
  }

  .custom-radio-label {
    font-size: 15px !important;
    line-height: 18px !important;
  }

  .custom-radio-button {
    zoom: 0.9;
    margin-right: 8px !important;
  }

  .custom-amount-wrapper {
    min-width: 186px; // 180px + 6px margin-left
    width: 186px;
    height: 44px;
    min-height: 44px;
  }

  .custom-amount-input {
    min-width: 180px;
    width: 180px;
    height: 44px;
    font-size: 13px;
    margin-left: 6px;
    padding: 0 8px;
  }

  .radio-value.rdr {
    min-width: 180px;
    width: 180px;
    height: 44px;
    font-size: 12px;
    margin-left: 6px;
    padding: 0 6px;
    line-height: 16px;
    white-space: normal;
    word-wrap: break-word;
  }

  .curr_sw_wrap {
    margin-bottom: 20px;
  }

  .curr_s_btn {
    font-size: 15px;
  }

  .c-selection {
    font-size: 14px;
    padding: 10px 20px 0 10px;
    line-height: 36px;
  }
}

@media (max-width: 500px) {
  // Переопределяем стили кнопки из donation.component.scss
  .payment-form .form-control button,
  .btn_pcj_ {
    height: 40px !important;
    font-size: 18px !important;
    width: 221px !important;
  }
}

@media (max-width: 450px) {
  .name-input-wrapper {
    margin-bottom: 20px;
    padding: 0 15px;
  }

  .name-label {
    font-size: 16px;
    line-height: 22px;
  }

  .name-input {
    font-size: 14px;
    padding: 10px 12px;
  }
}

@media (max-width: 370px) {
  .tot_wrap {
    font-size: 14px;
    line-height: 18px;
    margin: 30px 0 18px 0;
  }

  textarea {
    font-size: 13px;
    line-height: 17px;
    height: 100px;
  }

  .custom-radio-label {
    font-size: 14px !important;
    line-height: 16px !important;
  }

  .custom-amount-wrapper {
    min-width: 170px; // 160px + 10px margin-left
    width: 170px;
    height: 40px;
    min-height: 40px;
  }

  .custom-amount-input {
    min-width: 160px;
    width: 160px;
    height: 40px;
    font-size: 12px;
    padding: 0 6px;
  }

  .radio-value.rdr {
    min-width: 160px;
    width: 160px;
    height: 40px;
    font-size: 11px;
    padding: 0 4px;
    line-height: 14px;
    white-space: normal;
    word-wrap: break-word;
  }

  .curr_s_btn {
    font-size: 14px;

    img {
      margin-left: 8px;
    }
  }

  .c-selection {
    font-size: 13px;
    padding: 8px 16px 0 8px;
    line-height: 32px;
  }

  .currcy_switch {
    width: 90px;
  }
}