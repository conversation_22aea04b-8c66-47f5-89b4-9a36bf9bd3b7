<div class="payment-form" [formGroup]="paymentForm">
  <div class="form-control">
    <div class="tot_wrap">{{ 'donation.amount_label' | transloco }}</div>
    <div class="syst_wrap">
      @if (!environment.hideStripe) {
        <div class="curr_sw_wrap" [class.opened]="iswitchClicked">
          <div (click)="iswitchClicked = !iswitchClicked" class="curr_s_btn">
            <p>
              {{currentCurency}}
            </p>
            <img src="assets/images/Arrow_sw.svg" alt="arrow">
          </div>
          <div class="currcy_switch">
            <button type="button" [class.selected_c]="currentCurrency() == '₽ RUB'" class="c-selection"
              (click)="changeCurrency('₽ RUB'); iswitchClicked = false">₽ RUB</button>
            <button type="button" [class.selected_c]="currentCurrency() == '€ EUR'" class="c-selection"
              (click)="changeCurrency('€ EUR'); iswitchClicked = false">€ EUR</button>
          </div>
        </div>
      }
      <div class="custom-radio-group">
        <div class="custom-radio">
          <input type="radio" [value]="donationAmounts[0]" [checked]="paymentForm.get('sum')?.value === donationAmounts[0]"
                 (change)="selectAmount(donationAmounts[0])" id="amount1" class="custom-radio-input">
          <label for="amount1" class="custom-radio-label">
            <span class="custom-radio-button"></span>
            <span class="radio-value"> {{donationAmounts[0]}}&nbsp;{{currencySymbol}}</span>
          </label>
        </div>
        <div class="custom-radio">
          <input type="radio" [value]="donationAmounts[1]" [checked]="paymentForm.get('sum')?.value === donationAmounts[1]"
                 (change)="selectAmount(donationAmounts[1])" id="amount2" class="custom-radio-input">
          <label for="amount2" class="custom-radio-label">
            <span class="custom-radio-button"></span>
            <span class="radio-value"> {{donationAmounts[1]}}&nbsp;{{currencySymbol}}</span>
          </label>
        </div>
        <div class="custom-radio">
          <input type="radio" [value]="donationAmounts[2]" [checked]="paymentForm.get('sum')?.value === donationAmounts[2]"
                 (change)="selectAmount(donationAmounts[2])" id="amount3" class="custom-radio-input">
          <label for="amount3" class="custom-radio-label">
            <span class="custom-radio-button"></span>
            <span class="radio-value">{{donationAmounts[2]}}&nbsp;{{currencySymbol}}</span>
          </label>
        </div>
        <div class="custom-radio custom-radio-other">
          <input type="radio" [checked]="isCustomAmountSelected" id="other" class="custom-radio-input">
          <label for="other" class="custom-radio-label" (click)="selectCustomAmount(); $event.preventDefault()">
            <span class="custom-radio-button"></span>
          </label>
          <div class="custom-amount-wrapper">
            <span class="radio-value rdr" *ngIf="!isCustomAmountSelected">{{
              ('donation.other_amount' | transloco) + ' ' + ('donation.min_amount_placeholder' | transloco) + ' '
              + minAmount + ' ' + currencySymbol
              }}</span>
            <input *ngIf="isCustomAmountSelected" type="number" class="custom-amount-input"
                   [(ngModel)]="customAmount" [ngModelOptions]="{standalone: true}"
                   (ngModelChange)="onCustomAmountChange($event)"
                   (blur)="onCustomAmountBlur()"
                   [placeholder]="('donation.min_amount_placeholder' | transloco) + ' ' + minAmount + ' ' + currencySymbol"
                   [min]="minAmount"
                   (click)="$event.stopPropagation()"
                   (keydown.enter)="$event.preventDefault()">
          </div>
        </div>
      </div>
    </div>
    <div *ngIf="commentText" class="name-input-wrapper">
      <!-- <label class="name-label">{{ 'event.name_input_label' | transloco }}</label> -->
      <input
        type="text"
        class="name-input"
        [(ngModel)]="userName"
        [ngModelOptions]="{standalone: true}"
        [placeholder]="'event.name_input_placeholder' | transloco">
    </div>
    <div *ngIf="!commentText">
      <div class="tot_wrap">{{ 'donation.comment_label' | transloco }}<span>*</span></div>
      <textarea formControlName="comment" rows="5" id="txtarea"
        [placeholder]="'donation.comment_placeholder' | transloco"></textarea>
    </div>
    <button type="submit" class="btn_pcj_">{{ 'donation.donate' | transloco }}</button>
  </div>
</div>