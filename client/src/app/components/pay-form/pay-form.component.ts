import { environment } from '@/env/environment'
import { CommonModule } from '@angular/common'
import { Component, Input, OnInit, OnChanges, SimpleChanges, signal, effect } from '@angular/core'
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms'
import { TranslocoModule } from '@jsverse/transloco'

@Component({
  selector: 'app-pay-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, FormsModule, TranslocoModule],
  templateUrl: './pay-form.component.html',
  styleUrls: ['./pay-form.component.scss']
})
export class PayFormComponent implements OnInit, OnChanges {
  @Input() paymentForm!: FormGroup;
  @Input() commentText?: string;

  environment = environment;
  iswitchClicked: boolean = false;
  currentCurency: string = '₽ RUB';

  currencySymbol: string = '₽';
  donationAmounts = [500, 1500, 2500];
  customAmount: number | null = null;
  isCustomAmountSelected: boolean = false;
  minAmount: number = 100;

  userName = signal('');

  constructor() {
    effect(() => {
      const name = this.userName().trim();
      const baseComment = this.commentText || '';
      const fullComment = name ? `${baseComment} - ${name}` : baseComment;
      if (this.paymentForm) {
        this.paymentForm.patchValue({ comment: fullComment });
      }
    });
  }

  ngOnInit(): void {
    this.setCommentIfProvided();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['commentText'] && !changes['commentText'].firstChange) {
      this.setCommentIfProvided();
    }
  }

  private setCommentIfProvided(): void {
    if (this.commentText && this.paymentForm) {
      const name = this.userName().trim();
      const fullComment = name ? `${this.commentText} - ${name}` : this.commentText;
      this.paymentForm.patchValue({ comment: fullComment });
    }
  }

  changeCurrency(currency: string) {
    this.currentCurency = currency;

    if (currency === '€ EUR') {
      this.currencySymbol = '€';
      this.donationAmounts = [20, 60, 100];
      this.minAmount = 10;
      this.paymentForm.patchValue({ type: 'stripe' });
    } else {
      this.currencySymbol = '₽';
      this.donationAmounts = [500, 1500, 2500];
      this.minAmount = 100;
      this.paymentForm.patchValue({ type: 'yookassa' });
    }

    this.customAmount = null;
    this.isCustomAmountSelected = false;
    this.paymentForm.patchValue({ sum: null });
  }

  currentCurrency() {
    return this.currentCurency;
  }

  selectAmount(amount: number) {
    this.paymentForm.patchValue({ sum: amount });
    this.customAmount = null;
    this.isCustomAmountSelected = false;
  }

  selectCustomAmount() {
    this.isCustomAmountSelected = true;
    // Не устанавливаем значение по умолчанию, оставляем null
    this.customAmount = null;
    this.paymentForm.patchValue({ sum: null });
  }

  onCustomAmountChange(value: number | null) {
    this.customAmount = value;
    this.paymentForm.patchValue({ sum: value });
  }

  onCustomAmountBlur() {
    // При потере фокуса проверяем и корректируем значение
    if (this.customAmount !== null && this.customAmount < this.minAmount) {
      this.customAmount = this.minAmount;
      this.paymentForm.patchValue({ sum: this.minAmount });
    }
  }
}
