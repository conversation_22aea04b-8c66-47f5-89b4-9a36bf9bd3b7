import { navigateToUrl } from '@/utils/navigation.util'
import { JsonPipe } from '@angular/common'
import { Component, inject, input } from '@angular/core'
import { Router } from '@angular/router'

@Component({
  selector: 'app-chronological-card',
  imports: [JsonPipe],
  templateUrl: './chronological-card.component.html',
  styleUrl: './chronological-card.component.scss',
  standalone: true
})
export class ChronologicalCardComponent {
  readonly value = input<any>();
  readonly router = inject(Router);
  navigateToUrl = navigateToUrl;
}
