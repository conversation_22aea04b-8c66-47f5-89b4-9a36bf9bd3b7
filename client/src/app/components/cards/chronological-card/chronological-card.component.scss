@import '../../../../assets/styles/new-palette';
@import '../../../../assets/styles/new-typography';

.chronological-card {
    display: flex;
    flex-direction: column;
    gap: 24px;
    padding: 1px;
    background-color: main(50);
    @media (max-width: 1110px) {
        gap: 16px;
    }
    @media (max-width: 768px) {

    }
    &:hover {
        .hover-mask {
            opacity: 1;
            z-index: 2;
        }
        .secondary-btn {
            opacity: 1;
        }
    }
    .background-mask {
        z-index: 3;
    }

    .card-img {
        border-radius: 6px;
        max-height: 288px;
        height: 288px;
        object-fit: cover;
        @media (max-width: 1110px) {
            max-height: 230px;
            height: 230px;
        }

        @media (max-width: 430px) {
                margin-left: 1px;
                max-height: 146px;
                height: 146px;
                width: calc(100% - 3px);
        }
    }

    .card-date {
        color: main(600);
        margin: 8px 0;
        @include caption-1;
        line-height: 100%;
        letter-spacing: 0;
        vertical-align: middle;
        @media (max-width: 1110px) {
            @include caption-4;
            margin: 4px 0;
        }
        @media (max-width: 650px) {
            margin: 0;
        }
    }
    
    .card-title {
        @include subtitle-1;
        color: main(700);
        @media (max-width: 1110px) {
            @include body-2;
        }
        @media (max-width: 650px) {
            @include caption-1;
        }
    }

    .card-description {
        color: main(500);
        @include body-1;
        @media (max-width: 1110px) {
            @include body-3;
        }
    }

    .hover-mask {
        width: 100%;
        height: 100%;
        position: absolute;
        border-radius: 6px;
        top: 0;
        left: 0;
        background: rgba(255, 255, 255, 0.021);
        opacity: 0;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        max-height: 289px;
        @media (max-width: 1110px) {
            max-height: 231px;
        }
        @media (max-width: 430px) {
            max-height: 147px;
            width: calc(100% - 3px);
            top: 1px;
            left: 2px;
        }
    }
    .secondary-btn {
        top: 54.2%;
        left: 50%;
        position: absolute;
        transform: translateX(-50%);
        opacity: 0;
        background: url(../../../../assets/images/main-v2/secondary-button_md.webp) no-repeat center;
        background-size: cover;
        cursor: pointer;
        width: 138px;
        height: 33px;
        display: flex;
        align-items: center;
        justify-content: center;
        @include button-2;
        color: main(600);
        transition: all 0.2s ease-in-out;
        &:hover {
            background: url(../../../../assets/images/main-v2/secondary-button_md-hover.webp) no-repeat center;
            background-size: cover;
            // width: 138px;
            // height: 34px;
        }
        @media (max-width: 1110px) {
            width: 101px;
            height: 24px;
            @include button-5;
            &:hover {
                width: 103px;
                height: 25px;
            }
        }
        @media (max-width: 430px) {
            width: 77px;
            height: 18px;
            font-size: 9px;
            line-height: 12px;
            &:hover {
                width: 77px;
                height: 19px;
            }

        }
    }
}