@if (value(); as value) {
    <div class="accent-card relative">
        <div class="card-bg">
            <img src="../../../../assets/images/main-v2/accent-card-mask-head.webp" alt="background-mask"
                class="background-mask-img w-full object-cover" />
            <img class="card-img" [src]="value.imageUrl" [alt]="'test'">
            <div class="hover-mask"></div>
            <div class="secondary-btn" (click)="navigateToUrl(value?.link || '', router)">Подробнее</div>
        </div>
        <div class="accent-card-title-section">
            @if (value.date && value.level) {
                <div class="flex justify-between items-center date-level-box">
                    @if (value.level) {
                        <div class="card-level">
                            {{value.level}}
                        </div>
    
                    }
                    @if (value.date) {
                        <div class="card-date">
                            {{value.date}}
                        </div>
                    }
                </div>
            }
            @if (value.name) {
                <div class="card-title">
                    {{value.name}}
                </div>
            }
        </div>
        @if (value.description && isActive()) {
            <div class="card-description">
                {{value.description}}
            </div>
        }
    </div>
}