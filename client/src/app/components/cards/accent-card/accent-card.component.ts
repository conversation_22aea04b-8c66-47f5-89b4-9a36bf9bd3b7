import { navigateToUrl } from '@/utils/navigation.util'
import { Component, inject, input } from '@angular/core'
import { Router } from '@angular/router'

@Component({
  selector: 'app-accent-card',
  imports: [],
  templateUrl: './accent-card.component.html',
  styleUrl: './accent-card.component.scss',
  standalone: true
})
export class AccentCardComponent {
  readonly value = input<any>();
  readonly isActive = input<boolean>();
  readonly router = inject(Router);
  navigateToUrl = navigateToUrl;
}
