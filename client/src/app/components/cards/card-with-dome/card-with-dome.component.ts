import { navigateToUrl } from '@/utils/navigation.util'
import { Component, inject, input } from '@angular/core'
import { Router } from '@angular/router'

@Component({
  selector: 'app-card-with-domes',
  imports: [],
  templateUrl: './card-with-dome.component.html',
  styleUrl: './card-with-dome.component.scss',
  standalone: true
})
export class CardWithDomeComponent {
  readonly value = input<any>();
  readonly router = inject(Router);
  navigateToUrl = navigateToUrl;
}
