.narr_wrapper {
    max-width: 930px;
    margin: 0 auto
}

.lg_header {
    font-family: Prata;
    font-weight: 400;
    font-size: 36px;
    line-height: 48px;
    text-align: center;
    color: #532E00;
}

.accordion {
    width: 100%;
    margin-top: 20px;
    margin-bottom: 80px;
}

.accordion-item {
    border-bottom: 1px solid #EDC17F;
    padding-bottom: 30px;
}

.accordion-header {
    font-family: Prata;
    font-weight: 400;
    font-size: 24px;
    line-height: 32px;
    color: #99601A;
    display: flex;
    justify-content: space-between;
    cursor: pointer;
    padding-left: 20px;
    padding-top: 60px;

    .accordion-icon.mns {
        display: none;
    }
}

.accordion-icon {
    font-size: 50px;
    color: #351F04;
    transition: transform 0.3s ease;
}

.accordion-item.active {
    .accordion-content {
        max-height: 800px;
        opacity: 1;
        padding-top: 25px;
        padding-bottom: 10px;
    }

    .accordion-icon {
        display: none;

        &.mns {
            display: block;
        }
    }
}

.accordion-content {
    font-family: Prata;
    font-weight: 400;
    font-size: 18px;
    line-height: 24px;
    // Улучшенные переходы
    transition: max-height 0.2s cubic-bezier(0.4, 0, 0.2, 1), 
                opacity 0.15s ease-in-out,
                padding 0.15s ease-in-out;
    color: #261604;
    max-height: 0;
    max-width: 94%;
    padding-left: 20px;
    padding-top: 0;
    padding-bottom: 0;
    overflow: hidden;
    opacity: 0;
}