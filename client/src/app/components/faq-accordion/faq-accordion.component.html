<div class="narr_wrapper">
  @if (title()) {
    <div class="lg_header">
      {{ title() | transloco }}
    </div>
  }
  <div class="accordion">
    @for (faqItem of faqItems(); track $index) {
      <div [ngClass]="{'active': isFaqOpen($index)}" class="accordion-item">
        <div class="accordion-header" (click)="toggleFaq($index)">
          {{ faqItem.questionKey | transloco }}
          <span class="accordion-icon">+</span>
          <span class="accordion-icon mns">−</span>
        </div>
        <div class="accordion-content">
          {{ faqItem.answerKey | transloco }}
        </div>
      </div>
    }
  </div>
</div>