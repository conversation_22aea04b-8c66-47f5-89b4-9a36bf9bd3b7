import { Component, input, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslocoModule } from '@jsverse/transloco';

export interface FaqItem {
  questionKey: string;
  answerKey: string;
}

@Component({
  selector: 'app-faq-accordion',
  standalone: true,
  imports: [CommonModule, TranslocoModule],
  templateUrl: './faq-accordion.component.html',
  styleUrl: './faq-accordion.component.scss'
})
export class FaqAccordionComponent {
  faqItems = input.required<FaqItem[]>();
  title = input<string>('');
  
  openedFaqIndex = signal<number | null>(null);

  toggleFaq(index: number) {
    this.openedFaqIndex.set(
      this.openedFaqIndex() === index ? null : index
    );
  }

  isFaqOpen(index: number): boolean {
    return this.openedFaqIndex() === index;
  }
}
