import { Component, EventEmitter, Inject, inject, Output, PLATFORM_ID } from '@angular/core';
import { Router } from "@angular/router";
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { TranslocoService, TranslocoPipe } from "@jsverse/transloco";
import { ToasterService } from '@/services/toaster.service';

@Component({
  selector: 'app-footer-v2',
  standalone: true,
  imports: [ CommonModule, TranslocoPipe],
  templateUrl: './footer-v2.component.html',
  styleUrl: './footer-v2.component.scss'
})
export class FooterV2Component {






  @Output() sideOpen = new EventEmitter<any>();
  router = inject(Router);
  translocoService = inject(TranslocoService);

  constructor(
    @Inject(PLATFORM_ID) private platformId: any,
    private toasterService: ToasterService
  ) { }

  ngOnInit() {
  }

  navigateToLibrary() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/library`]);
    this.sideOpen.emit(false);
  }

  navigateToPhoto() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/photo`]);
    this.sideOpen.emit(false);
  }

  navigateToAudio() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/audiogallery/audiolektsii`]);
    this.sideOpen.emit(false);
  }

  navigateToForum() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/forum`]);
    this.sideOpen.emit(false);
  }

  scrollToTop() {
    if (isPlatformBrowser(this.platformId)) {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }

  copyEmailToClipboard() {
    if (isPlatformBrowser(this.platformId)) {
      navigator.clipboard.writeText('<EMAIL>');
    }
    this.toasterService.showToast(this.translocoService.translate('toast.email_copied'), 'success', 'bottom-middle', 3000);
  }

  navTo(link: string) {
    if (!link) return;
  }
}
  