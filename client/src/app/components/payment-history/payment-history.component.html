
<h2 class="p_history">{{ 'payment_history.p_history' | transloco }}</h2>
<div class="tabl_wrp">
  <table class="custom-table">
    <thead>
      <tr>
        <th>{{ 'payment_history.status' | transloco }}</th>
        <th>{{ 'payment_history.date' | transloco }}</th>
        <th>{{ 'payment_history.payment_method' | transloco }}</th>
        <th>{{ 'payment_history.details' | transloco }}</th>
        <th>{{ 'payment_history.amount' | transloco }}</th>
      </tr>
    </thead>
    <tbody>
      @if (loading && paymentHistory.length === 0) {
        <tr>
          <td colspan="5" class="text-center">{{ 'payment_history.loading' | transloco }}</td>
        </tr>
      }
      @if (!loading && paymentHistory.length === 0) {
        <tr>
          <td colspan="5" class="text-center">{{ 'payment_history.empty' | transloco }}</td>
        </tr>
      }
      @for (payment of paymentHistory; track payment.id) {
        <tr>
          <td>
            <span class="badge" [ngClass]="getStatusBadgeClass(payment.status)">
              {{ getStatusText(payment.status) }}
            </span>
          </td>
          <td>
            {{ formatDate(payment.date) }}<br>
            <small>{{ formatTime(payment.date) }}</small>
          </td>
          <td class="card-icon_tx">
            <img [src]="getPaymentMethodIcon(payment.paymentMethod)" alt="VISA" class="card-icon">
            &nbsp;&nbsp;{{ payment.paymentMethod }}
          </td>
          <td class="td_txt">{{ payment.details }}</td>
          <td>{{ payment.amount }}&nbsp;{{getCurrencySymbol(payment.currency)}}</td>
        </tr>
      }
    </tbody>
  </table>
</div>
@if (currentPage < totalPages) {
  <div class="load_more_wrap">
    <div class="lod_m" (click)="loadMore()" [class.loading]="loading">
      <img src="assets/images/icons/Left Icon.svg" alt="arr">
      {{ loading ? ('payment_history.loading' | transloco) : ('search.load_more' | transloco) }}
    </div>
  </div>
}
