@import '../../../assets/styles/new-palette';
@import '../../../assets/styles/new-typography';


.page-title-container {
    max-width: 1440px;
    margin: 0 auto;
    width: 100%;
    height: 358px;
    min-height: fit-content;
    display: flex;
    flex-direction: column;
    gap: 22px;
    justify-content: flex-start;
    align-items: center;
    position: relative;
    padding: 108px 17px 32px;
    .title-img {
        position: absolute;
        top: 0px;
        left: 50%;
        transform: translateX(-50%);
        width: 722px;
        height: 358px;
        background: url('../../../assets/images/main-v2/page-ornament.webp') no-repeat top center;
        background-size: cover;
    }
    .page-title {
        @include h1-alt;
        color: main(600);
        position: sticky;
        z-index: 2;
        max-width: 930px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 10px;
        text-align: center;
    }
    .title-decor-line {
        width: 356px;
        height: 21px;
        background: url('../../../assets/images/main-v2/title-decor-line.webp') no-repeat center;
        background-size: cover;
        position: sticky;
        z-index: 2;
    }
}

@media (max-width: 1100px) {
    .page-title-container {
        height: 312px;
        breadcrumb {
            position: absolute;
            margin-top: 9px;
        }
        .title-img {
            width: 576px;
            height: 295px;
            background: url('../../../assets/images/main-v2/page-ornament-tablet.webp') no-repeat top center;
            background-size: cover;
        }
        .page-title {
            max-width: calc(100% - 70px);
        }
    }
}

@media (max-width: 650px) {
    .page-title-container {
        padding: 95px 14px 8px;
        height: 240px;
        gap: 16px;
        .title-img {
            top: 80px;
            width: 296px;
            height: 152px;
            background: url('../../../assets/images/main-v2/page-ornament-mobile.webp') no-repeat top center;
            background-size: cover;
        }
        .page-title {
            @include h3-alt;
            text-align: center;
            margin-bottom: 0px;
        }
        .title-decor-line {
            width: 261px;
            height: 17px;
            background: url('../../../assets/images/main-v2/title-decor-line-mobile.webp') no-repeat center;
            background-size: cover;
        }
    }
}

