import { Component, inject } from '@angular/core';
import { CommonModule } from "@angular/common";
import { PageTitleService } from '@/services/page-title.service';
import { TranslocoPipe } from "@jsverse/transloco"
import { BreadcrumbComponent } from '../breadcrumb/breadcrumb.component';

@Component({
  selector: 'app-page-title',
  standalone: true,
  imports: [CommonModule, BreadcrumbComponent, TranslocoPipe],
  templateUrl: './page-title.component.html',
  styleUrl: './page-title.component.scss'
})
export class PageTitleComponent {
  readonly pageTitleService = inject(PageTitleService);

  readonly title = this.pageTitleService.pageTitle;
}
