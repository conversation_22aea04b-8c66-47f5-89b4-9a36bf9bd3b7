import { environment } from '@/env/environment'
import { Router } from '@angular/router'

export function navigateToUrl(url: string | null | undefined, router: Router): boolean {
  if (!url) {
    return false;
  }

  try {
    const parsedUrl = new URL(url);
    const baseUrl = new URL(environment.baseUrl);
    
    if (parsedUrl.hostname === baseUrl.hostname) {
      router.navigate([parsedUrl.pathname]);
    } else {
      location.href = url;
    }
  } catch {
    location.href = url;
  }

  return true;
}

