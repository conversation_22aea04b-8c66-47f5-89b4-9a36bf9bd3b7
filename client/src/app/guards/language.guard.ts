import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';

const VALID_LANGUAGES = ['ru', 'en', 'de', 'ua', 'it'];

export const languageGuard: CanActivateFn = (route, state) => {
  const router = inject(Router);
  
  // Extract language from route params
  const lang = route.paramMap.get('lang');
  
  // If language is not valid, redirect to /ru with the rest of the path
  if (!lang || !VALID_LANGUAGES.includes(lang)) {
    const urlSegments = state.url.split('/').filter(segment => segment);
    
    // Remove the invalid language segment and prepend 'ru'
    const pathWithoutLang = urlSegments.slice(1).join('/');
    const newUrl = pathWithoutLang ? `/ru/${pathWithoutLang}` : '/ru';
    
    router.navigate([newUrl]);
    return false;
  }
  
  return true;
};

