import { inject, PLATFORM_ID } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { CookieService } from 'ngx-cookie-service';
import { isPlatformServer } from '@angular/common';
import { SsrCookieService } from 'ngx-cookie-service-ssr';
import { TranslocoService } from '@jsverse/transloco';

export const eventGuard: CanActivateFn = (route, state) => {
  const platformId = inject(PLATFORM_ID);
  const cookieService = isPlatformServer(platformId)
    ? inject(SsrCookieService)
    : inject(CookieService);
  const router = inject(Router);
  const translocoService = inject(TranslocoService);

  // Check if current route is the event page
  // Extract language from URL
  const urlSegments = state.url.split('/').filter(segment => segment);
  const isMainEventPage = urlSegments.length === 1; // Only language segment, e.g., /ru
  const isEventPage = urlSegments.length === 2 && (urlSegments[1] === 'event' || urlSegments[1] === 'lending-steps'); // e.g., /ru/event

  // Skip guard for event pages (both main and /event route)
  if (isMainEventPage || isEventPage) {
    return true;
  }

  // Get the cookie value
  const omCookie = cookieService.get('om');

  // If cookie doesn't exist or value is not 'dram', redirect to event page
  if (omCookie !== 'dram') {
    const lang = translocoService.getActiveLang() || 'ru';
    router.navigate([`/${lang}/event`]);
    return false;
  }

  return true;
};

