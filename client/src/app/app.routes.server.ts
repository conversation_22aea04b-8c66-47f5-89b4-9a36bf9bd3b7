import { RenderMode, ServerRoute } from '@angular/ssr'

const SUPPORTED_LANGUAGES = [
  { lang: 'ru' },
  { lang: 'en' },
  { lang: 'de' },
  { lang: 'ua' },
  { lang: 'it' }
];

export const serverRoutes: ServerRoute[] = [
  // Root redirect - prerender for better performance
  {
    path: '',
    renderMode: RenderMode.Prerender
  },

  // Language-specific main pages - prerender for all supported languages
  {
    path: ':lang',
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return SUPPORTED_LANGUAGES;
    }
  },

  // Static pages - prerender for better performance and SEO
  {
    path: ':lang/photo',
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return SUPPORTED_LANGUAGES;
    }
  },

  {
    path: ':lang/audiogallery/audiolektsii',
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return SUPPORTED_LANGUAGES;
    }
  },

  {
    path: ':lang/audiogallery/videolektsii',
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return SUPPORTED_LANGUAGES;
    }
  },

  {
    path: ':lang/library',
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return SUPPORTED_LANGUAGES;
    }
  },

  {
    path: ':lang/categories',
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return SUPPORTED_LANGUAGES;
    }
  },

  {
    path: ':lang/sitemap',
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return SUPPORTED_LANGUAGES;
    }
  },

  {
    path: ':lang/forum',
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return SUPPORTED_LANGUAGES;
    }
  },

  {
    path: ':lang/news',
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return SUPPORTED_LANGUAGES;
    }
  },

  {
    path: ':lang/audiofiles',
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return SUPPORTED_LANGUAGES;
    }
  },

  {
    path: ':lang/search',
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return SUPPORTED_LANGUAGES;
    }
  },

  // User authentication pages - client-side rendering for better UX
  {
    path: ':lang/signin',
    renderMode: RenderMode.Client
  },
  {
    path: ':lang/signup',
    renderMode: RenderMode.Client
  },
  {
    path: ':lang/forgot',
    renderMode: RenderMode.Client
  },

  // User profile pages - client-side rendering (user-specific content)
  {
    path: ':lang/profile/**',
    renderMode: RenderMode.Client
  },

  // AI Chat pages - client-side rendering (interactive chat functionality)
  {
    path: ':lang/ai-chat',
    renderMode: RenderMode.Client
  },
  {
    path: ':lang/ai-chat/**',
    renderMode: RenderMode.Client
  },

  {
    path: ':lang/anketa',
    renderMode: RenderMode.Server
  },


   {
    path: ':lang/new-main',
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return SUPPORTED_LANGUAGES;
    }
  },

   {
    path: ':lang/donation',
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return SUPPORTED_LANGUAGES;
    }
  },

   {
    path: ':lang/notifications',
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return SUPPORTED_LANGUAGES;
    }
  },
   {
    path: ':lang/photo/**',
    renderMode: RenderMode.Client
  },
   {
    path: ':lang/audiogallery/audiolektsii/**',
    renderMode: RenderMode.Client
  },
  {
    path: ':lang/library/**',
    renderMode: RenderMode.Client
  },
  {
    path: ':lang/categories/**',
    renderMode: RenderMode.Client
  },
  {
    path: ':lang/forum/**',
    renderMode: RenderMode.Client
  },
  {
    path: ':lang/news/**',
    renderMode: RenderMode.Client
  },
  {
    path: ':lang/audiofiles/**',
    renderMode: RenderMode.Client
  },
  {
    path: ':lang/search/**',
    renderMode: RenderMode.Client
  },
  {
    path: ':lang/mypage/**',
    renderMode: RenderMode.Client
  },
  {
    path: ':lang/donation/bank-transfer',
    renderMode: RenderMode.Client
  },
  {
    path: ':lang/donation/online',
    renderMode: RenderMode.Client
  },
  {
    path: ':lang/:category/:page',
    renderMode: RenderMode.Client
  },
  {
    path: '**',
    renderMode: RenderMode.Client
  }
];
