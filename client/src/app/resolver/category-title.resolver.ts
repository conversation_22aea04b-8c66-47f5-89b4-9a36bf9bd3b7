import { Injectable, inject } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ContentService } from '@/services/content.service';
import { TranslocoService } from '@jsverse/transloco';

@Injectable({
  providedIn: 'root'
})
export class CategoryTitleResolver implements Resolve<string> {
  contentService = inject(ContentService)
  translocoService = inject(TranslocoService)

  resolve(route: ActivatedRouteSnapshot): Observable<string> {
    const categoryId = route.paramMap.get('id');
    const defaultTitle = this.translocoService.translate('categories.title') || 'Categories';

    if (!categoryId) return of(defaultTitle);

    return this.contentService.getCategories().pipe(
      map((categories: any) => {
        const category = categories.find((c: any) => c.id == categoryId);
        return category?.title || defaultTitle;
      }),
      catchError(() => of(defaultTitle))
    );
  }
}
