import { ForumService } from "@/services/forum.service"
import { Injectable, inject } from '@angular/core'
import { ActivatedRouteSnapshot, Resolve } from '@angular/router'
import { TranslocoService } from '@jsverse/transloco'
import { of } from 'rxjs'
import { catchError, map } from 'rxjs/operators'

export interface ForumTopicBreadcrumb {
  categoryName: string;
  categoryId: number;
  topicName: string;
}

@Injectable({
  providedIn: 'root'
})
export class ForumTopicTitleResolver implements Resolve<string> {
  forumService = inject(ForumService)
  translocoService = inject(TranslocoService)

  resolve(route: ActivatedRouteSnapshot) {
    const topicId = route.paramMap.get('id');

    if (!topicId) return of(this.translocoService.translate('breadcrumb.topic'));

    return this.forumService.getTopic(topicId).pipe(
      map((topic: any) => {
        if (topic?.category) {
          return {
            categoryName: topic.category.name,
            categoryId: topic.category.id,
            topicName: topic.name || this.translocoService.translate('breadcrumb.topic')
          };
        }
        return topic?.name || this.translocoService.translate('breadcrumb.topic');
      }),
      catchError(() => of(this.translocoService.translate('breadcrumb.topic')))
    );
  }
}
