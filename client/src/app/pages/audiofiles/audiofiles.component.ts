import {Component, HostListener, inject, OnInit, PLATFORM_ID, DestroyRef, ViewChild, ElementRef, effect} from '@angular/core';
import { ShareDataService } from '@/services/share-data.service';
import {CommonModule, isPlatformBrowser, NgOptimizedImage} from '@angular/common';
import {PlaylistService} from "@/services/playlist.service";
import {ProfileService} from "@/services/profile.service";
import {ActivatedRoute, Router, RouterLink} from '@angular/router';
import {AudioService} from "@/services/audio.service";
import {TranslocoModule, TranslocoService} from "@jsverse/transloco";
import {NgSelectModule} from "@ng-select/ng-select";
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Track } from '@/interfaces/track';
import { ToasterService } from '@/services/toaster.service';
import { AuthService } from '@/services/auth.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { debounceTime, distinctUntilChanged, Subject, switchMap, takeUntil, tap } from 'rxjs';
import { BreadcrumbComponent } from '@/components/breadcrumb/breadcrumb.component';
import { IsInPlaylistPipe } from '@/pipes/isInPlaylist.pipe';
import { CustomDropdownComponent } from '@/components/custom-dropdown/custom-dropdown.component';
import {AudioFilesService} from "@/services/audiofiles.service";
import moment from "moment/moment";
import {PlaylistDialogComponent} from "@/pages/audio-gallery/playlist-dialog/playlist-dialog.component";
import { FavoritesIconComponent } from "@/components/svg-components/favorites-icon/favorites-icon.component";
import { PageTitleService } from '@/services/page-title.service';

@Component({
  selector: 'app-audio-gallery',
  standalone: true,
  imports: [CommonModule,
    FormsModule,
    NgSelectModule,
    NgOptimizedImage,
    // BreadcrumbComponent,
    CustomDropdownComponent,
    ReactiveFormsModule, IsInPlaylistPipe, PlaylistDialogComponent, FavoritesIconComponent, TranslocoModule],
  templateUrl: './audiofiles.component.html',
  styleUrls: ['./audiofiles.component.scss']
})
export class AudiofilesComponent implements OnInit {
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
  searchSubject = new Subject<any>();
  toasterService = inject(ToasterService);
  shareDataService = inject(ShareDataService);
  translocoService = inject(TranslocoService);
  profileService = inject(ProfileService);
  audioFilesService = inject(AudioFilesService);
  readonly pageTitleService = inject(PageTitleService);
  route = inject(ActivatedRoute);
  platformId = inject(PLATFORM_ID)
  authService = inject(AuthService);
  router = inject(Router);
  fb = inject(FormBuilder)
  private readonly destroyRef = inject(DestroyRef);
  filter: FormGroup = this.fb.group({
    search: '',
    category: '',
    description:'',
    sortOrder: 'date',
    tag: [],
    page: 1
  })
  filters: any = {
    types: [],
    singers: [],
    tags: [],
    sortOrder: 'date',
    page: 1,
  };
  // typesArray: any;
  sortOptions = [
    { label: 'Алфавиту', value: 'title', id: 0 },
    { label: 'Дате', value: 'date', id: 1 },
    { label: 'Популярности', value: 'views', id: 2 },
    { label: 'Лайкам', value: 'likes', id: 3 },
    { label: 'Длительности', value: 'duration', id: 4 },
  ];
  sortDirection: 'Asc' | 'Desc' = 'Asc';
  currentSortField: string = 'title';
  selectedSortLabel = 'Алфавиту';
  authorsArray: any[] = [];
  selectedTrackId: number = -1;
  playlists: any[] = [];
  itemsPerPage: number = 10;
  totalPages: number = 1;
  dropdownOpen = false;
  dropdownSortOpen = false;

  isOpened: Record<number, boolean> = {};
  data: any = [];
  selectedTags: any[] = [];
  selectedSingers: any[] = [];
  selectedTypes: any[] = [];

  items: any = []
  types: any = [];
  singers: any = [];
  tags: any = [];

  showPlaylist = false;

  ngOnInit() {
    this.pageTitleService.setPageTitle('Aудио');
    const sortValue = this.filters.sortOrder;
    const match = sortValue.match(/^(.*?)(Asc|Desc)?$/);
    this.currentSortField = match?.[1] || 'title';
    this.sortDirection = (match?.[2] as 'Asc' | 'Desc') || 'Asc';
    this.selectedSortLabel = this.sortOptions.find(option => option.value === this.currentSortField)?.label || '';

    this.audioFilesService.getTypes().subscribe((res: any) => this.types = res);
    this.audioFilesService.getSingers().subscribe((res: any) => this.singers = res);
    this.audioFilesService.getTags().subscribe((res: any) => this.tags = res);

    this.audioFilesService
      .findAll(this.filters)
      .subscribe(
        (res: any) => this.items = [...this.items, ...res.items]
      );

    this.searchSubject.pipe(
      debounceTime(1000),
      distinctUntilChanged(),
      takeUntilDestroyed(this.destroyRef),
    ).subscribe((e) => {
      this.items = [];
      this.filters.title = e.target.value;
      this.findAll()
    });

    //this.getPlaylists();
  }

  findAll() {
    this.audioFilesService
      .findAll(this.filters)
      .subscribe(
        (res: any) => {
          this.items = [...this.items, ...res.items];
          this.totalPages = res.pagination.totalPages;
        }
      );
  }

  openModal() {
    this.modal.nativeElement.showModal();
  }

  toggleDropdownSort() {
    this.dropdownSortOpen = !this.dropdownSortOpen;
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  applyFilter(loadMore: boolean = false) {
    this.data = [];

    this.filters = {
      ...this.filters,
      ...this.filter.value
    };
  }


  selectSort(field: string) {
    this.items = [];
    if (this.currentSortField === field) {
      this.sortDirection = this.sortDirection === 'Asc' ? 'Desc' : 'Asc';
    } else {
      this.currentSortField = field;
      this.sortDirection = 'Asc';
    }
    this.filters.sortOrder = this.currentSortField + this.sortDirection;
    this.selectedSortLabel = this.sortOptions.find(option => option.value === field)?.label || '';
    this.findAll()
  }

  nextPage() {
    if(this.filters.page < this.totalPages) {
      this.filters.page++;
      this.findAll();
    }
  }

  resetFilters() {
    for(let key in this.filters) this.filters[key] = '';
    this.selectedTags = [];
    this.selectedSingers = [];
    this.selectedTypes = [];
    this.filters.page = 1;
  }

  changeTypeFilter(types: any) {
    const copy = types && types.length > 0
      ? types.map((tag: any) => tag.id.toString())
      : [];
    this.items = [];
    this.filters.types = copy;
    this.filters.page = 1;
    this.findAll();
  }

  changeTagsFilter(tags: any) {
    const copy = tags && tags.length > 0
      ? tags.map((tag: any) => tag.id.toString())
      : [];
    this.items = [];
    this.filters.tags = copy;
    this.filters.page = 1;
    this.findAll();
  }

  changeSingersFilter(singers: any) {
    const copy = singers && singers.length > 0
      ? singers.map((tag: any) => tag.id.toString())
      : [];
    this.items = [];
    this.filters.singers = copy;
    this.filters.page = 1;
    this.findAll();
  }

  get formatDate() {
    return (date: any) => moment(date).format('YYYY.MM.DD HH:mm');
  }

  like(item: any) {
    if (!this.authService.token()) {
      this.shareDataService.showInfoModal('error');
      return
    }
    this.audioFilesService.like(item.id).subscribe({
      next: () => {
        item.liked = !item.liked;
        if(!item.liked) {
          item.likes--
        } else {
          item.likes++;
        }
        this.toasterService.showToast('Аудио добавлено в понравившееся!', 'success', 'bottom-middle', 3000);
      },
      error: (err: any) => {
        this.toasterService.showToast(err.message, 'error', 'bottom-middle');
      }
    })
  }

  showPlaylistDialog(id: any) {
    if (!this.authService.token()) {
      this.shareDataService.showInfoModal('error');
      return
    }
    this.showPlaylist = true;

    this.selectedTrackId = id;
  }

  playlistClosed(event: any) {
    this.showPlaylist = event;
  }

  playlistSelected(playlists: any) {
    this.showPlaylist = false;
    this.playlists = playlists;
  }


  getPlaylists() {
    if(this.authService.isAuth) {
      this.profileService.getPlaylist().subscribe((res: any) => this.playlists = res);
    }
  }

  protected readonly Math = Math;
}
