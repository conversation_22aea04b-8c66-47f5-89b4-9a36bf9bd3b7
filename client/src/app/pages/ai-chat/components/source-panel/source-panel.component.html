<div class="source-panel" [class.panel-open]="isOpen()">
  @if (hasSource() && source()) {
    <!-- Panel Header -->
    <div class="panel-header">
      <div class="header-content">
        <h3 class="source-title">{{ source()!.title }}</h3>
        @if (source()!.url) {
          <a
            class="source-url"
            [href]="source()!.url"
            target="_blank"
            rel="noopener noreferrer"
          >
            <span class="url-icon">🔗</span>
            <span class="url-text">{{ 'ai_chat.open_source' | transloco }}</span>
          </a>
        }
      </div>
      <!-- <button 
        class="close-btn"
        (click)="onClose()"
        title="Закрыть панель"
      >
        <span class="close-icon">✕</span>
      </button> -->
    </div>

    <!-- Panel Content -->
    <div class="panel-content">
      <div class="source-content">
        @if (isLoading()) {
          <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">{{ 'ai_chat.loading_content' | transloco }}</div>
          </div>
        } @else if (highlightedContent()) {
          <div
            class="content-text"
            [innerHTML]="formatContent(highlightedContent())"
          ></div>
        } @else {
          <div
            class="content-text"
            [innerHTML]="formatContent(source()!.content)"
          ></div>
        }
      </div>
    </div>

    <!-- Panel Footer -->
    <div class="panel-footer">
      <div class="footer-info">
        <button
        class="action-btn copy-btn mr-[4px]"
        (click)="copy()"
        [title]="'ai_chat.copy' | transloco"
        >
        @if (isCopied()) {
          <span class="action-icon">✓</span>
        } @else {
          <img src="assets/images/icons/copy.svg" alt="i">
        }
        </button>
      </div>
    </div>
  } @else {
    <!-- Empty State -->
    <div class="empty-panel">
      <div class="empty-content">
        <div class="empty-icon">📄</div>
        <div class="empty-title">{{ 'ai_chat.source_not_selected' | transloco }}</div>
        <div class="empty-description">
          {{ 'ai_chat.source_not_selected_description' | transloco }}
        </div>
      </div>
    </div>
  }

  <!-- Mobile Backdrop -->
  <div class="mobile-backdrop" (click)="onBackdropClick($event)"></div>
</div>
