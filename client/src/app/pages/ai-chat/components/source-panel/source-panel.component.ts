import { CommonModule } from '@angular/common';
import { AfterViewChecked, ChangeDetectionStrategy, Component, computed, effect, input, OnChanges, OnInit, output, signal } from '@angular/core';
import { TranslocoModule } from '@jsverse/transloco';
import { ChatSource } from '../../ai-chat.component';

@Component({
  selector: 'app-source-panel',
  standalone: true,
  imports: [CommonModule, TranslocoModule],
  templateUrl: './source-panel.component.html',
  styleUrl: './source-panel.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SourcePanelComponent implements OnInit, OnChanges, AfterViewChecked {
  // Inputs
  source = input<ChatSource | null>(null);
  isOpen = input<boolean>(false);

  // Outputs
  close = output<void>();

  // State
  highlightedContent = signal<string>('');
  isCopied = signal<boolean>(false);
  private hasScrolled = signal<boolean>(false);

  // Computed
  hasSource = computed(() => this.source() !== null);
  isLoading = computed(() => {
    const source = this.source();
    return source && source.content === 'Загружаем контент...';
  });

  constructor() {
    // Auto-scroll to highlighted text when modal opens or content changes
    effect(() => {
      const isOpen = this.isOpen();
      const source = this.source();

      // Reset scroll flag when modal opens or source changes
      if (isOpen && source) {
        this.hasScrolled.set(false);
      }

      // Update highlighted content when source changes
      this.updateHighlightedContent();
    });
  }

  ngOnInit() {
    // Initial update
    this.updateHighlightedContent();
  }

  ngOnChanges() {
    // Effect will handle updates, but keep this for compatibility
  }

  ngAfterViewChecked() {
    // Try to scroll after view is checked
    const isOpen = this.isOpen();
    const content = this.highlightedContent();
    const sourceContent = this.source()?.content || '';
    const isLoadingState = this.isLoading();
    const alreadyScrolled = this.hasScrolled();

    // Check for both 'highlight' and 'search-highlight' classes
    const hasHighlight = content.includes('class="highlight"') ||
                        content.includes('class="search-highlight"') ||
                        sourceContent.includes('class="search-highlight"');

    if (isOpen && hasHighlight && !isLoadingState && !alreadyScrolled) {
      const success = this.scrollToHighlightSync();
      if (success) {
        this.hasScrolled.set(true);
      }
    }
  }

  onClose() {
    this.close.emit();
  }

  onBackdropClick(event: Event) {
    if (event.target === event.currentTarget) {
      this.onClose();
    }
  }

  private updateHighlightedContent() {
    const currentSource = this.source();
    if (!currentSource) {
      this.highlightedContent.set('');
      return;
    }

    let content = currentSource.content;

    // Highlight the quoted text if available
    if (currentSource.highlightedText) {
      const highlightedText = currentSource.highlightedText;
      const regex = new RegExp(`(${this.escapeRegExp(highlightedText)})`, 'gi');
      content = content.replace(regex, '<mark class="highlight">$1</mark>');
    }

    this.highlightedContent.set(content);
  }

  private escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  scrollToHighlight() {
    setTimeout(() => this.scrollToHighlightSync(), 100);
  }

  scrollToHighlightSync(): boolean {
    // Try multiple selectors to find the highlight element
    const selectors = [
      '.source-content .search-highlight',
      '.source-content mark.highlight',
      '.source-content .highlight',
      '.search-highlight',
      'mark.highlight',
      '.highlight'
    ];

    for (const selector of selectors) {
      const highlightElement = document.querySelector(selector);
      if (highlightElement) {
        highlightElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
        return true;
      }
    }

    return false;
  }

  formatContent(content: string): string {
    return content
      // Convert line breaks to HTML
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br>')
      // Wrap in paragraphs
      .replace(/^/, '<p>')
      .replace(/$/, '</p>')
      // Clean up empty paragraphs
      .replace(/<p><\/p>/g, '')
      .replace(/<p><br><\/p>/g, '');
  }

  copy() {
    const content = this.source()!.content;
    if (!content.trim()) return;
    const plainText = this.source()!.content.replace(/<[^>]+>/g, '');
    navigator.clipboard.writeText(plainText).then(() => {
      this.isCopied.set(true);
      setTimeout(() => {
        this.isCopied.set(false);
      }, 2000);
    }).catch(err => {
      console.error('Failed to copy text: ', err);
    });
  }
}
