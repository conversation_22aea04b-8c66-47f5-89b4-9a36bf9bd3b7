import { CommonModule, NgOptimizedImage } from '@angular/common'
import { ChangeDetectionStrategy, Component, computed, ElementRef, HostListener, inject, input, NgZone, output, signal, ViewChild } from '@angular/core'
import { FormsModule } from '@angular/forms'
import { TranslocoModule, TranslocoService } from '@jsverse/transloco'
import { Chat } from '../../ai-chat.component'

@Component({
  selector: 'app-chat-sidebar',
  standalone: true,
  imports: [CommonModule, FormsModule, NgOptimizedImage, TranslocoModule],
  templateUrl: './chat-sidebar.component.html',
  styleUrl: './chat-sidebar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ChatSidebarComponent {
  translocoService = inject(TranslocoService);

  // Inputs
  chats = input<Chat[]>([]);
  currentChatId = input<string | null>(null);

  // Outputs
  newChat = output<void>();
  chatSelect = output<string>();
  chatRename = output<{ chatId: string; newTitle: string }>();
  chatDelete = output<string>();
  chatPin = output<string>();

  // State
  editingChatId = signal<string | null>(null);
  editingTitle = signal<string>('');
  openMenuChatId = signal<string | null>(null);
  message: string = "";

  @ViewChild('chatActionsMenu') chatActionsMenu!: ElementRef<HTMLElement>;
  @ViewChild('modalRename') modalRename!: ElementRef<HTMLDialogElement>;
  @ViewChild('confirmDialog') confirmDialog!: ElementRef<HTMLDialogElement>;
  
  // Computed
  sortedChats = computed(() => {
    const allChats = this.chats();
    const pinned = allChats.filter(chat => chat.isPinned);
    const unpinned = allChats.filter(chat => !chat.isPinned);
    
    // Sort pinned chats by updatedAt (most recent first)
    pinned.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
    
    // Sort unpinned chats by updatedAt (most recent first)
    unpinned.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
    
    return [...pinned, ...unpinned];
  });

  hasChats = computed(() => this.chats().length > 0);

  constructor(private zone: NgZone) {
    this.zone.runOutsideAngular(() => {
        document.addEventListener('contextmenu', (e: MouseEvent) => {
            e.preventDefault();
        });
    });
}

  onNewChatClick() {
    this.newChat.emit();
  }

  onChatClick(chatId: string) {
    if (this.editingChatId() !== chatId) {
      console.log('Emitting chat select:', chatId);
      this.chatSelect.emit(chatId);
    }
  }

  onStartRename(chat: Chat, event: Event) {
    event.stopPropagation();
    this.editingChatId.set(chat.id);
    this.editingTitle.set(chat.title);
    this.closeMenu();
    this.openModal();
  }

  onSaveRename(chatId: string | null) {
    if(!chatId) {
      return;
    }
    const newTitle = this.editingTitle().trim();
    if (newTitle && newTitle !== this.chats().find(c => c.id === chatId)?.title) {
      this.chatRename.emit({ chatId, newTitle });
    }
    this.editingChatId.set(null);
    this.editingTitle.set('');
  }

  onCancelRename() {
    this.editingChatId.set(null);
    this.editingTitle.set('');
  }

  openConfirmationDialog(message: string): Promise<boolean> {
    this.message = message;
    this.confirmDialog.nativeElement.showModal();
    return new Promise((resolve) => {
      const okButton = this.confirmDialog.nativeElement.querySelector('.ok-button');
      const cancelButton = this.confirmDialog.nativeElement.querySelector('.cancel-button');
      const closeDialog = (confirmed: boolean) => {
        this.closeModal(this.confirmDialog.nativeElement);
        resolve(confirmed);
      };
      okButton?.addEventListener('click', () => closeDialog(true), { once: true });
      cancelButton?.addEventListener('click', () => closeDialog(false), { once: true });
    });
  }

  onDeleteChat(chatId: string, event: Event) {
    this.openConfirmationDialog('Удалить чат?').then((confirmed) => {
      if (confirmed) {
        this.chatDelete.emit(chatId);
      }
    });
    event.stopPropagation();
  }

  onPinChat(chatId: string, event: Event) {
    event.stopPropagation();
    this.closeMenu();
    this.chatPin.emit(chatId);
  }

  onKeydown(event: KeyboardEvent, chatId: string) {
    if (event.key === 'Enter') {
      this.onSaveRename(chatId);
    } else if (event.key === 'Escape') {
      this.onCancelRename();
    }
  }

  formatDate(date: Date): string {
    const now = new Date();
    const diffInHours = (now.getTime() - new Date(date).getTime()) / (1000 * 60 * 60);
    const lang = this.translocoService.getActiveLang();

    // Map language codes to locale strings
    const localeMap: Record<string, string> = {
      'ru': 'ru-RU',
      'en': 'en-US',
      'de': 'de-DE',
      'ua': 'uk-UA',
      'it': 'it-IT'
    };

    const locale = localeMap[lang] || 'ru-RU';

    if (diffInHours < 24) {
      return new Date(date).toLocaleTimeString(locale, {
        hour: '2-digit',
        minute: '2-digit'
      });
    } else if (diffInHours < 24 * 7) {
      return new Date(date).toLocaleDateString(locale, {
        weekday: 'short'
      });
    } else {
      return new Date(date).toLocaleDateString(locale, {
        day: '2-digit',
        month: '2-digit'
      });
    }
  }

  getChatTitle(chat: Chat): string {
    if (chat.title && chat.title.trim()) {
      return chat.title;
    }

    // Generate title from first user message
    const firstUserMessage = chat.messages.find(m => m.role === 'user');
    if (firstUserMessage) {
      const title = firstUserMessage.content.slice(0, 30);
      return title.length < firstUserMessage.content.length ? title + '...' : title;
    }

    return this.translocoService.translate('ai_chat.new_chat');
  }

  openChatContextMenu(chatId: string, event: Event) {
    event.stopPropagation();

    if(this.openMenuChatId() === chatId) {
      this.closeMenu();
      return;
    }

    this.openMenuChatId.set(chatId);
  }

  closeMenu() {
    this.openMenuChatId.set(null);
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    const target = event.target as HTMLElement;
    if (!target.closest('.chats-action-menu') && !target.closest('.three-dots-btn')) {
      this.closeMenu();
    }
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  openModal() {
    this.modalRename.nativeElement.showModal();
  }
}
