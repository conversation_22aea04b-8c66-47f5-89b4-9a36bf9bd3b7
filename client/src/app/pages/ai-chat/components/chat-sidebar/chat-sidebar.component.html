<dialog class="stylized_wide h_auto" #modalRename>
  <div (click)="closeModal(modalRename)" class="x_bt"></div>
  <div class="flex flex-col cont_mod">
    <p class="pr_20 text-center mb-[40px]">{{'ai_chat.rename_chat' | transloco}}</p>
      <input
        type="text"
        class="chat-title-input"
        [(ngModel)]="editingTitle"

        
        (click)="$event.stopPropagation()"
        #titleInput
      />
      <!-- (blur)="onSaveRename(chat.id)" -->
      <!-- (keydown)="onKeydown($event, chat.id)" -->

    <div class="filter-buttons-container flex justify-between mt-4">
      <button class="save-btn" (click)="closeModal(modalRename)" >
        <img class="btn-backdrop-img" src="assets/images/primary-cancel-button-bg.png" width="218" height="54" alt="bg">
        <div class="save-btn-label" >Отмена</div>
      </button>
      <button class="save-btn" (click)="onSaveRename(editingChatId());closeModal(modalRename)">
        <img class="btn-backdrop-img" src="assets/images/primary-assept-button-bg.png" width="218" height="54" alt="bg">
        <div class="save-btn-label">Изменить</div>
      </button>
    </div>
  </div>
</dialog>
<div class="chat-sidebar">
  <!-- Header with New Chat Button -->
  <div class="sidebar-header flex flex-col">
    <h2 class="chat-title main text-center">{{'ai_chat.title' | transloco}}</h2>
    <button class="new-chat-btn" (click)="onNewChatClick()">
      <!-- <span class="new-chat-icon">+</span> -->
      <span class="new-chat-text">{{'ai_chat.new_chat' | transloco}}</span>
    </button>
  </div>
  @if (hasChats()) {
    <div class="flex text-xs text-main-800 chat-list-header">
      {{'ai_chat.chats' | transloco}}
    </div>
  }

  <!-- Chat List -->
  <div class="chat-list-container">
    @if (hasChats()) {
      <div class="chat-list">
        @for (chat of sortedChats(); track chat.id) {
          <div 
            class="chat-item relative justify-between"
            [class.active]="currentChatId() === chat.id"
            [class.pinned]="chat.isPinned"
          >
          <div class="flex" (click)="onChatClick(chat.id)">
            <!-- Pin indicator -->
            @if (chat.isPinned) {
              <div class="pin-indicator">
                <!-- <span class="pin-icon">📌</span> -->
                <img src="assets/images/icons/pin.png" alt="i" class="w-[20px] h-[20px]">
              </div>
            }

            <!-- Chat content -->
            <div class="chat-content">
                <div class="chat-info">
                  <div class="chat-title">{{ getChatTitle(chat) }}</div>
                  <!-- <div class="chat-date">{{ formatDate(chat.updatedAt) }}</div> -->
                </div>
            </div>
          </div>
          <img src="assets/images/three-dots-dark.svg" alt="i" class="w-6 h-6 three-dots-btn" (click)="openChatContextMenu(chat.id, $event)">

            <!-- Action buttons (rightclick menu) -->

            <div class="chats-action-menu" [class.showing]="openMenuChatId() === chat.id">
              @if (!chat.isPinned) {
                <div class="chats-action-menu-item" (click)="onPinChat(chat.id, $event)">
                  <img src="assets/images/icons/pin.png" alt="i" class="w-[20px] h-[20px] mr-[8px]">
                  {{'ai_chat.pin' | transloco}}
                </div>
              }
              @else {
                <div class="chats-action-menu-item" (click)="onPinChat(chat.id, $event)">
                  <img src="assets/images/icons/pin.png" alt="i" class="w-[20px] h-[20px] mr-[8px]">
                  {{'ai_chat.unpin' | transloco}}
                </div>
              }

              <div class="chats-action-menu-item" (click)="onStartRename(chat, $event)">
                <img src="assets/images/icons/pen.png" alt="i" class="w-[20px] h-[20px] mr-[8px]">
                {{'ai_chat.edit' | transloco}}
              </div>
              <div class="chats-action-menu-item delete" (click)="onDeleteChat(chat.id, $event)">
                <img src="assets/images/icons/trash.png" alt="i" class="w-[20px] h-[20px] mr-[8px]">
                {{'ai_chat.delete' | transloco}}
              </div>
            </div>
          </div>
        }
      </div>
    } @else {
      <!-- Empty state -->
      <div class="empty-state">
        <div class="empty-icon">💬</div>
        <div class="empty-title">{{'ai_chat.no_chats' | transloco}}</div>
        <div class="empty-description text-main-700">
          {{'ai_chat.empty_description' | transloco}}
        </div>
      </div>
    }
  </div>
</div>
<dialog class="stylized_wide" #confirmDialog>
  <div class="dialog-message">
    {{ message }}
  </div>
  <div class="mt-[60px] flex gap-[16px] text-center justify-center dialog-footer">
    <button class="save-btn confirm-btn cancel-button" type="submit">
      <img class="btn-backdrop-img" src="assets/images/primary-cancel-button-bg.png" width="218" height="54" alt="bg">
      <div class="save-btn-label" >Отмена</div>
    </button>
    <button class="save-btn confirm-btn ok-button" type="submit">
      <img class="btn-backdrop-img" src="assets/images/primary-assept-button-bg.png" width="218" height="54" alt="bg">
      <div class="save-btn-label">Да</div>
    </button>
    <!-- <button type="submit" class="confirm-btn ok-button">
      <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
      <div class="confirm-btn-label">Да</div>
    </button>
    <button type="submit" class="confirm-btn cancel-button">
      <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
      <div class="confirm-btn-label">Отмена</div>
    </button> -->
    <!-- <button class="ok-button">Да</button>
    <button class="cancel-button ml-4">Отмена</button> -->
  </div>
</dialog>