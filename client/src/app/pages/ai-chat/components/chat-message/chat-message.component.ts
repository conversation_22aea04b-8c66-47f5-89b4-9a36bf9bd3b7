import { environment } from '@/env/environment'
import { AiChatService } from '@/services/ai-chat.service'
import { ProfileService } from '@/services/profile.service'
import { CommonModule } from '@angular/common'
import { AfterViewInit, ChangeDetectionStrategy, Component, effect, ElementRef, inject, input, OnDestroy, output, signal, ViewEncapsulation } from '@angular/core'
import { TranslocoModule } from '@jsverse/transloco'
import { ChatMessage, ChatSource } from '../../ai-chat.component'

@Component({
  selector: 'app-chat-message',
  standalone: true,
  imports: [CommonModule, TranslocoModule],
  templateUrl: './chat-message.component.html',
  styleUrl: './chat-message.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None
})
export class ChatMessageComponent implements OnDestroy, AfterViewInit {
  // Inputs
  message = input.required<ChatMessage>();
  isLoading = input<boolean>(false);

  // Outputs
  sourceClick = output<ChatSource>();
  typewritingComplete = output<string>();

  // State
  isCopied = signal<boolean>(false);
  displayedContent = signal<string>('');
  isContentVisible = signal<boolean>(false);
  loadingLinks = signal<Set<string>>(new Set());
  private typewriterInterval?: number;
  private elementRef = inject(ElementRef);
  private aiChatService = inject(AiChatService);
  profileService = inject(ProfileService);
  environment = environment;
  private boundClickHandler = this.handleContainerClick.bind(this);

  constructor() {
    effect(() => {
      const message = this.message();
      if (message.isTypewriting && message.fullContent) {
        this.showContentWithGeminiEffect(message.fullContent);
      } else {
        this.displayedContent.set(message.content);
        this.isContentVisible.set(true);
        setTimeout(() => this.setupLinkClickHandlers(), 0);
      }
    });
  }

  ngAfterViewInit() {
    this.setupLinkClickHandlers();
  }

  ngOnDestroy() {
    if (this.typewriterInterval) {
      clearInterval(this.typewriterInterval);
    }
    this.removeLinkClickHandlers();
  }

  private setupLinkClickHandlers() {
    this.removeLinkClickHandlers();

    this.elementRef.nativeElement.addEventListener('click', this.boundClickHandler);
  }

  private removeLinkClickHandlers() {
    this.elementRef.nativeElement.removeEventListener('click', this.boundClickHandler);
  }

  private handleContainerClick(event: Event) {
    const target = event.target as HTMLElement;
    const anchor = target.closest('a') as HTMLAnchorElement | null;

    if (!anchor) return;

    const href = anchor.getAttribute('href');
    if (!href) return;

    try {
      const url = new URL(href, window.location.href);
      if (url.searchParams.has('q')) {
        event.preventDefault();
        const title = anchor.textContent?.trim() || href;
        this.loadAndShowUrlContent(href, title);
      }
    } catch {
    }
  }



  private loadAndShowUrlContent(url: string, title: string) {
    this.loadingLinks.update(links => new Set([...links, url]));

    try {
      const urlObj = new URL(url, window.location.href);
      const targetText = urlObj.searchParams.get('q');
      if (urlObj.searchParams.has('q')) {
        urlObj.searchParams.delete('q');
      }
      const fetchUrl = urlObj.toString();

      const loadingSource: ChatSource = {
        id: url,
        title: title,
        content: 'Загружаем контент...',
        url: url,
        highlightedText: targetText || undefined
      };

      this.onSourceClick(loadingSource);

      this.aiChatService.fetchUrlContent(fetchUrl).subscribe({
        next: (urlContent) => {
          this.loadingLinks.update(links => {
            const newLinks = new Set(links);
            newLinks.delete(url);
            return newLinks;
          });

          const source: ChatSource = {
            id: url,
            title: title,
            content: urlContent.content,
            url: url,
            highlightedText: targetText || undefined
          };

          this.onSourceClick(source);
        },
        error: () => {
          this.loadingLinks.update(links => {
            const newLinks = new Set(links);
            newLinks.delete(url);
            return newLinks;
          });
        }
      });
    } catch (error) {
      console.error('Invalid URL:', error);
    }
  }



  private showContentWithGeminiEffect(fullText: string) {
    if (this.typewriterInterval) {
      clearInterval(this.typewriterInterval);
    }

    let currentIndex = 0;
    this.displayedContent.set('');
    this.isContentVisible.set(true);

    const typewriterLength = Math.min(fullText.length, Math.max(300, fullText.indexOf(' ', 200) + 1));

    this.typewriterInterval = window.setInterval(() => {
      if (currentIndex < typewriterLength) {
        this.displayedContent.update(current => current + fullText[currentIndex]);
        currentIndex++;
      } else {
        if (this.typewriterInterval) {
          clearInterval(this.typewriterInterval);
          this.typewriterInterval = undefined;
        }

        setTimeout(() => {
          this.isContentVisible.set(false);

          setTimeout(() => {
            this.displayedContent.set(fullText);

            setTimeout(() => {
              this.isContentVisible.set(true);
              this.typewritingComplete.emit(this.message().id);
              setTimeout(() => this.setupLinkClickHandlers(), 600);
            }, 50);
          }, 600);
        }, 800);
      }
    }, 30);
  }

  onCopyMessage() {
    const message = this.message();
    const content = message.fullContent || message.content;
    if (!content.trim()) return;

    const plainText = this.stripHtmlTags(content);

    navigator.clipboard.writeText(plainText).then(() => {
      this.isCopied.set(true);
      setTimeout(() => {
        this.isCopied.set(false);
      }, 2000);
    }).catch(err => {
      console.error('Failed to copy text: ', err);
    });
  }

  private stripHtmlTags(html: string): string {
    const tmp = document.createElement('div');
    tmp.innerHTML = html;
    return tmp.textContent || tmp.innerText || '';
  }

  onSourceClick(source: ChatSource) {
    this.sourceClick.emit(source);
  }

  formatTime(date: Date): string {
    return new Date(date).toLocaleString('ru-RU', {
      hour: '2-digit',
      minute: '2-digit',
      day: 'numeric',
      month: 'long'
    });
  }

  // Parse message content to handle markdown and citations
  parseContent(content: string): { type: 'text' | 'citation' | 'source'; content: string; source?: ChatSource }[] {
    if (!content) return [];

    const parts: { type: 'text' | 'citation' | 'source'; content: string; source?: ChatSource }[] = [];
    const sources = this.message().sources || [];
    
    // Simple parsing for citations [1], [2], etc.
    const citationRegex = /\[(\d+)\]/g;
    let lastIndex = 0;
    let match;

    while ((match = citationRegex.exec(content)) !== null) {
      // Add text before citation
      if (match.index > lastIndex) {
        const textContent = content.slice(lastIndex, match.index);
        if (textContent) {
          parts.push({ type: 'text', content: textContent });
        }
      }

      // Add citation
      const citationNumber = parseInt(match[1]) - 1;
      const source = sources[citationNumber];
      if (source) {
        parts.push({ 
          type: 'citation', 
          content: match[0],
          source: source
        });
      } else {
        parts.push({ type: 'text', content: match[0] });
      }

      lastIndex = match.index + match[0].length;
    }

    // Add remaining text
    if (lastIndex < content.length) {
      const remainingContent = content.slice(lastIndex);
      if (remainingContent) {
        parts.push({ type: 'text', content: remainingContent });
      }
    }

    return parts.length > 0 ? parts : [{ type: 'text', content: content }];
  }
}
