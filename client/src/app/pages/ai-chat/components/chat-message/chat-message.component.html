<div class="message-wrapper" [class.user-message]="message().role === 'user'" [class.assistant-message]="message().role === 'assistant'">
  
  <!-- User Message -->
  @if (message().role === 'user') {
    <div class="message user-msg">
      <div class="message-content">
        <div class="message-text" [innerHTML]="message().content"></div>
        <!-- <div class="message-time">{{ formatTime(message().timestamp) }}</div> -->
      </div>
      <div class="message-avatar user-avatar">
        @if (profileService.avatar()) {
          <img [src]="environment.serverUrl + '/upload/' + profileService.avatar()" alt="User avatar" class="avatar-image">
        } @else {
          <span class="avatar-icon">👤</span>
        }
      </div>
    </div>
  }

  <!-- Assistant Message -->
  @if (message().role === 'assistant') {
    <div class="message assistant-msg">
      @if (!isLoading()) { 
        <div class="message-avatar assistant-avatar">
          <img class="w-[34px] h-[34px]" src="assets/images/avatar.png" alt="i">
        </div>
      }

      <div class="message-content">
        @if (message().isTyping && isLoading()) {
          <!-- Typing Indicator -->
          <div class="typing-indicator">
            <div class="loader loader--border" role="status" aria-live="polite" aria-label="Loading">
              <span class="visually-hidden">{{ 'ai_chat.loading' | transloco }}</span>
            </div>
            <span class="typing-text">{{ 'ai_chat.researching_question' | transloco }}</span>
          </div>
        } @else {
          <!-- Message Content -->
          @if (message().isStreaming && !displayedContent()) {
            <div class="flex items-center gap-2 gathering-sources-text text-sm">
              <span>{{ 'ai_chat.gathering_sources' | transloco }}</span>
              <span class="typing-cursor">|</span>
            </div>
          } @else {
            <div class="message-text smooth-transition"
                 [style.opacity]="isContentVisible() ? 1 : 0">
              @for (part of parseContent(displayedContent()); track $index) {
                @if (part.type === 'text') {
                  <span [innerHTML]="part.content"></span>
                } @else if (part.type === 'citation' && part.source) {
                  <button
                    class="citation-link"
                    (click)="onSourceClick(part.source!)"
                    [title]="part.source!.title"
                  >
                    {{ part.content }}
                  </button>
                }
              }
            </div>
          }

          <!-- @if (!message().isTypewriting) {
            <div class="om-shanti">{{ 'ai_chat.om_shanti' | transloco }} 🙏</div>
          } -->

          <!-- Message Actions -->
          @if (!message().isTypewriting) {
            <div class="message-actions justify-between">
              <div class="flex items-center gap-2">
                <button
                class="action-btn copy-btn mr-[4px]"
                (click)="onCopyMessage()"

                [title]="isCopied() ? ('ai_chat.copied' | transloco) : ('ai_chat.copy' | transloco)"
                >
                @if (isCopied()) {
                  <span class="action-icon">✓</span>
                } @else {
                  <img src="assets/images/icons/copy.svg" alt="i">
                }
                </button>

                @if (message().isStreaming) {
                  <div class="flex items-center gap-1 text-xs preparing-answer-text">
                    <div class="loader loader--border-small" role="status" aria-live="polite">
                      <span class="visually-hidden">{{ 'ai_chat.loading' | transloco }}</span>
                    </div>
                    <span>{{ 'ai_chat.preparing_answer' | transloco }}</span>
                  </div>
                }
              </div>

              <div class="message-time">{{ formatTime(message().timestamp) }}</div>
            </div>
          }
        }
      </div>
    </div>
  }
</div>
