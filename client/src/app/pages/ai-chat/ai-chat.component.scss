@use "../../styles/core.scss" as core;

:host {
  position: fixed;
  top: 132px; // Header height
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
}

.ai-chat-container {
  display: flex;
  height: 100%;
  width: 100%;
  background: var(--grey-back);
  position: relative;
  overflow: hidden;
  font-family: IBM_Plex_Sans, sans-serif;
  border-top: 1px solid var(--border_light);
  margin-top: 3px;
}

// Left Sidebar
app-chat-sidebar {
  flex: 0 0 430px;
  // width: 300px;
  // min-width: 300px;
  background: var(--side_back);
  border-right: 1px solid var(--border_light);
  transition: transform 0.3s ease;
  z-index: 100;
  box-shadow: 2px 0 8px rgba(83, 46, 0, 0.1);

  &.sidebar-hidden {
    transform: translateX(-100%);
  }
}

// Main Chat Area
.chat-main-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  min-width: 0;
  background: var(--main-background-color);
}

.sidebar-toggle {
  display: none;
  position: absolute;
  top: 16px;
  left: 16px;
  z-index: 110;
  background: #FEF1CF;
  border: 2px solid var(--border);
  border-radius: 12px;
  padding: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(83, 46, 0, 0.15);

  &:hover {
    // background: var(--selection);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(83, 46, 0, 0.2);
  }

  .burger-line {
    display: block;
    width: 18px;
    height: 2px;
    background: var(--font-color1);
    margin: 3px 0;
    transition: 0.3s ease;
    border-radius: 1px;
  }

  &.sidebar-open {
    .burger-line:nth-child(1) {
      transform: rotate(-45deg) translate(-4px, 5px);
      margin-bottom: 5px;
    }
    .burger-line:nth-child(2) {
      opacity: 0;
    }
    .burger-line:nth-child(3) {
      transform: rotate(45deg) translate(-4px, -5px);
    }
  }
}

// Right Panel
app-source-panel {
  width: 0;
  min-width: 0;
  background: transparent;
  // border-left: none;
  // transition: all 0.3s ease;
  // overflow: hidden;
  // box-shadow: -2px 0 8px rgba(83, 46, 0, 0.1);

  &.panel-open {
    // width: 380px;
    // min-width: 380px;

    // Adjust input area when source panel is open
    ~ .chat-main-wrapper .input-area {
      right: 380px;
    }
  }
}

// Mobile Overlay
.mobile-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 90;
}

.chat-source-modal {
  border-radius: 20px;
  padding: 30px 40px;
  background-color: #FFF6E0;
  .x_bt {
    top: 23px;
    right: 23px;
  }
}

dialog:-internal-dialog-in-top-layer::backdrop {
  backdrop-filter: blur(10px);
  background: #190E0180;
}

// Mobile Styles
@media (max-width: 1024px) {
  app-chat-sidebar {
    position: fixed;
    top: 80px; // Header height
    left: 0;
    bottom: 0; // Go to bottom of screen
    z-index: 100;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);

    &.sidebar-hidden {
      transform: translateX(-100%);
    }
  }

  .sidebar-toggle {
    display: block;

    &.mobile-only {
      display: block;
    }
  }

  .mobile-overlay {
    display: block;
  }

  app-source-panel {

    &.panel-open {
      width: 100%;
      min-width: 100%;
    }
  }
}

@media (max-width: 768px) {

  .chat-source-modal {
    border-radius: 20px;
    padding: 24px 16px;
    min-width: 90vw;
    background-color: #FFF6E0;
    .x_bt {
      top: 12px;
      right: 12px;
    }
  }

  app-chat-sidebar {
    width: 280px;
    min-width: 280px;
  }

  app-source-panel.panel-open {
    width: 100%;
    min-width: 100%;
  }
  :host {
    top:80px;
  }
}

@media (max-width: 480px) {
  app-chat-sidebar {
    width: 100%;
    min-width: 100%;
  }

  .sidebar-toggle {
    top: 15px;
    left: 15px;
  }
}
