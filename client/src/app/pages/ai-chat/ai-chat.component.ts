import { AuthService } from '@/services/auth.service'
import { PageTitleService } from '@/services/page-title.service'
import { ProfileService } from '@/services/profile.service'
import { ToasterService } from '@/services/toaster.service'
import { CommonModule } from '@angular/common'
import { ChangeDetectionStrategy, Component, computed, ElementRef, inject, OnDestroy, OnInit, signal, ViewChild } from '@angular/core'
import { FormsModule } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { TranslocoService } from '@jsverse/transloco'
import { Subscription } from 'rxjs'
import { AiChatService } from '../../services/ai-chat.service'
import { ChatMainComponent } from './components/chat-main/chat-main.component'
import { ChatSidebarComponent } from './components/chat-sidebar/chat-sidebar.component'
import { SourcePanelComponent } from './components/source-panel/source-panel.component'

export interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  sources?: ChatSource[];
  isTyping?: boolean;
  isStreaming?: boolean;
  isTypewriting?: boolean;
  fullContent?: string;
}

export interface ChatSource {
  id: string;
  title: string;
  content: string;
  url?: string;
  highlightedText?: string;
}

export interface Chat {
  id: string;
  title: string;
  createdAt: Date;
  updatedAt: Date;
  isPinned: boolean;
  messages: ChatMessage[];
}

@Component({
  selector: 'app-ai-chat',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ChatSidebarComponent,
    ChatMainComponent,
    SourcePanelComponent
  ],
  templateUrl: './ai-chat.component.html',
  styleUrl: './ai-chat.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AiChatComponent implements OnInit, OnDestroy {
  private aiChatService = inject(AiChatService);
  private toasterService = inject(ToasterService);
  private translocoService = inject(TranslocoService);
  private profileService = inject(ProfileService);
  readonly pageTitleService = inject(PageTitleService);
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
    authService = inject(AuthService);

  chats = signal<Chat[]>([]);
  currentChatId = signal<string | null>(null);
  selectedSource = signal<ChatSource | null>(null);
  isSourcePanelOpen = signal<boolean>(false);
  isSidebarOpen = signal<boolean>(true);
  isLoading = signal<boolean>(false);
  private hasTriedAutoCreate = false;
  private profileSubscription?: Subscription;

  currentChat = computed(() => {
    const chatId = this.currentChatId();
    const chats = this.chats();
    const foundChat = chats.find(chat => chat.id === chatId) || null;
    return foundChat;
  });

  ngOnInit() {
    this.pageTitleService.clearPageTitle();
    this.route.params.subscribe(params => {
      const chatIdFromUrl = params['chatId'];
      if (chatIdFromUrl) {
        this.currentChatId.set(chatIdFromUrl);
      }
    });

    this.loadChats();

    this.profileSubscription = this.profileService.data$.subscribe((profile) => {
      if (profile) {
        this.aiChatService.loadChats();
        this.hasTriedAutoCreate = false;
      }
    });

    document.getElementById('footer-main')?.classList.add('hidden');
    document.getElementsByTagName('app-daily-sidebar')[0]?.classList.add('hidden');
    document.getElementsByTagName('app-daily-sidebar')[1]?.classList.add('hidden');
  }

  ngOnDestroy() {
    this.profileSubscription?.unsubscribe();
  }

  loadChats() {
    this.aiChatService.getChats().subscribe(chats => {
      if (chats === null) {
        return;
      }

      this.chats.set(chats);

      if (chats.length === 0 && !this.hasTriedAutoCreate) {
        this.hasTriedAutoCreate = true;
        this.onNewChat();
      } else if (!this.currentChatId()) {
        const firstChatId = chats[0].id;
        this.currentChatId.set(firstChatId);
        this.updateUrl(firstChatId);
      }
    });
  }

  onNewChat() {
    this.aiChatService.createNewChat().subscribe({
      next: (newChat) => {
        if (!newChat.messages) {
          newChat.messages = [];
        }

        this.aiChatService.addChatToList(newChat);
        this.currentChatId.set(newChat.id);
        this.updateUrl(newChat.id);
      },
      error: (e) => {
        this.toasterService.showToast(e.error.message, 'error', 'bottom-middle');
      }
    });
  }

  onChatSelect(chatId: string) {
    this.currentChatId.set(chatId);
    this.updateUrl(chatId);

    const selectedChat = this.chats().find(c => c.id === chatId);
    if (selectedChat?.messages && selectedChat.messages.length > 0) {
      return;
    }

    this.aiChatService.getChatMessages(chatId).subscribe({
      next: (messages) => {
        this.chats.update(chats =>
          chats.map(chat =>
            chat.id === chatId ? { ...chat, messages: messages || [] } : chat
          )
        );
      },
      error: () => {
        console.error('Error loading chat messages');
        this.chats.update(chats =>
          chats.map(chat =>
            chat.id === chatId ? { ...chat, messages: [] } : chat
          )
        );
      }
    });
  }

  private updateUrl(chatId: string) {
    // Get language from URL path, same approach as in app.component.ts
    const urlLang = this.router.url.split('/')[1];
    const lang = (urlLang && ['ru', 'en', 'de', 'ua'].includes(urlLang)) ? urlLang : this.translocoService.getActiveLang();
    this.router.navigate([lang, 'ai-chat', chatId], { replaceUrl: true });
  }

  onChatRename(chatId: string, newTitle: string) {
    this.aiChatService.updateChat(chatId, { title: newTitle }).subscribe((updatedChat) => {
      this.aiChatService.updateChatInList(updatedChat);
    });
  }

  onChatDelete(chatId: string) {
    this.aiChatService.deleteChat(chatId).subscribe(() => {
      this.aiChatService.removeChatFromList(chatId);
      if (this.currentChatId() === chatId) {
        const remainingChats = this.chats();
        if (remainingChats.length > 0) {
          this.onChatSelect(remainingChats[0].id);
        } else {
          this.currentChatId.set(null);
        }
      }
    });
  }

  onChatPin(chatId: string) {
    const chat = this.chats().find(c => c.id === chatId);
    if (!chat) return;

    this.aiChatService.updateChat(chatId, { isPinned: !chat.isPinned }).subscribe((updatedChat) => {
      this.aiChatService.updateChatInList(updatedChat);
    });
  }

  onSendMessage(content: string) {
    const currentChat = this.currentChat();
    if (!currentChat) return;

    const userMessage: ChatMessage = {
      id: this.generateId(),
      content: content,
      role: 'user',
      timestamp: new Date()
    };

    const typingMessage: ChatMessage = {
      id: this.generateId(),
      content: '',
      role: 'assistant',
      timestamp: new Date(),
      isTyping: true
    };

    const currentMessages = [...(currentChat.messages || []), userMessage, typingMessage];
    this.updateChatMessages(currentChat.id, currentMessages);
    this.isLoading.set(true);

    this.aiChatService.sendMessage(currentChat.id, content).subscribe({
      next: (response: any) => {
        const updatedChat = this.currentChat();
        if (updatedChat) {
          const messagesWithoutTyping = (updatedChat.messages || []).filter(m => !m.isTyping);

          const fullContent = response.aiResponse?.message || response.assistantMessage?.content || '';
          const streamingMessageId = this.generateId();

          const streamingMessage: ChatMessage = {
            id: streamingMessageId,
            content: '',
            role: 'assistant',
            timestamp: new Date(),
            sources: response.aiResponse?.sources || response.sources || [],
            isStreaming: true
          };

          this.updateChatMessages(currentChat.id, [...messagesWithoutTyping, streamingMessage]);

          setTimeout(() => {
            const currentChat = this.currentChat();
            if (currentChat) {
              const messagesWithoutStreaming = (currentChat.messages || []).filter(m => m.id !== streamingMessageId);

              const aiMessage: ChatMessage = {
                id: this.generateId(),
                content: '',
                role: 'assistant',
                timestamp: new Date(),
                sources: response.aiResponse?.sources || response.sources || [],
                isTypewriting: true,
                fullContent: fullContent
              };

              this.updateChatMessages(currentChat.id, [...messagesWithoutStreaming, aiMessage]);
            }
          }, 5000);
        }
        this.isLoading.set(false);
      },
      error: () => {
        console.error('Error sending message');
        const updatedChat = this.currentChat();
        if (updatedChat) {
          const messagesWithoutTyping = (updatedChat.messages || []).filter(m => !m.isTyping);
          this.updateChatMessages(currentChat.id, messagesWithoutTyping);
        }
        this.isLoading.set(false);
      }
    });
  }

  onSourceClick(source: ChatSource) {
    this.selectedSource.set(source);
    this.isSourcePanelOpen.set(true);
    this.modal.nativeElement.showModal();
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  onSourcePanelClose() {
    this.isSourcePanelOpen.set(false);
    this.selectedSource.set(null);
  }

  onTypewritingComplete(messageId: string) {
    const currentChat = this.currentChat();
    if (!currentChat) return;

    this.chats.update(chats =>
      chats.map(chat => {
        if (chat.id === currentChat.id) {
          const updatedMessages = (chat.messages || []).map(message => {
            if (message.id === messageId && message.isTypewriting) {
              return {
                ...message,
                content: message.fullContent || message.content,
                isTypewriting: false,
                fullContent: undefined
              };
            }
            return message;
          });
          return { ...chat, messages: updatedMessages };
        }
        return chat;
      })
    );
  }

  onToggleSidebar() {
    this.isSidebarOpen.update(open => !open);
  }

  private updateChatMessages(chatId: string, messages: ChatMessage[]) {
    this.chats.update(chats => 
      chats.map(chat => 
        chat.id === chatId ? { ...chat, messages, updatedAt: new Date() } : chat
      )
    );
  }



  private generateId(): string {
    return Math.random().toString(36).substring(2, 11);
  }
}
