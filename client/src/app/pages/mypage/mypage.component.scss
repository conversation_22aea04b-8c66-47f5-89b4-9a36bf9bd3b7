@import '../../../assets/styles/new-palette';
@import '../../../assets/styles/new-typography';

.mypage {
  // margin-top: 35px;
  // @media (max-width: 1100px) {
  //   margin-top: 0;
  // }
  // @media (max-width: 570px) {
  //   margin-top: -8px;
  // }
  // @media (max-width: 500px) {
  //   margin-top: -28px;
  // }
  .container {
    &:has(.html_wrap) {
      padding: 0 15px;
    }
  }
}

.mypage-header {
  position: relative;
  padding: 20px;
  padding-top: 90px;
}

.mypage-header img {
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  object-fit: cover;
  position: absolute;
  z-index: 1;
}

.mypage-header:before {
  content: '';
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,.5);
  top: 0;
  left: 0;
  position: absolute;
  z-index: 2;
}

.mypage-header__text {
  z-index: 5;
  position: relative;
  color: white;
}

.mypage-title {
  font-weight: 400;
  font-size: 32px;
  text-align: center;
}

.mypage-description {
  margin-top: 40px;
}

.mypage-buttons {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

.mypage-content {
  padding: 20px;
  color: white;
}

.social {
  margin: 20px 0;
}

.content_wrap_ {
  display: flex;
  cursor: pointer;
  margin-bottom: 10px;
}

.content_wrap_ .social_par {
  font-family: Prata;
  font-weight: 400;
  font-size: 18px;
  line-height: 24px;
  color: white;
  margin-left: 10px;
}

.content_ {
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  margin-right: 10px;
}

.content_._telegram {
  background: var(--cover_telegram);
  width: 28px;
  height: 24px;
}

.content_._instagram {
  background: var(--cover_instagram);
  width: 24px;
  height: 24px;
}

.content_._phone {
  background: var(--cover_phone);
  width: 27px;
  height: 19px;
}

.content_._email {
  background: var(--cover_email);
  width: 24px;
  height: 23px;
}

.mypage-image {
  margin: 40px 0;
  text-align: center;
  padding: 0 20px;
}

.mypage-image img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
}

.container {
  margin: 0 auto;
}

.mypage-carousel {
  max-width: 1440px;
  width: 100%;
  margin: 0 auto;
  padding: 110px 0;
  overflow-x: hidden;

  .carousel-header {
    max-width: 1200px;
    margin: 0 auto 100px auto;
    color: main(600);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;
    @media (max-width: 1100px) {
      margin: 0 auto 40px auto;
    }
    @media (max-width: 650px) {
      margin: 0 auto 33px auto;
      justify-content: flex-start;
    }

    .carousel-title {
      @include h2;
      line-height: 100%;
      svg {
        display: none;
      }

      @media (max-width: 1100px) {
        @include h4;
      }

      @media (max-width: 650px) {
        display: flex;
        align-items: center;
        gap: 16px;
        width: 100%;
        justify-content: flex-start;
        @include button-2;
        svg {
          display: block;
        }
      }
    }
    
    a {
      @include button-1;
      color: main(700);
      transition: all 0.2s ease-in-out;
      @media (max-width: 1100px) {
        @include button-4;
      }

      @media (max-width: 650px) {
        display: none;
      }

      &:hover {
        color: main(500);
      }

      &:active {
        color: main(600);
      }
    }
  }

  .chronological-section,
  .start-learning-section,
  .projects-and-ministry-section {
    margin-bottom: 110px;
    @media (max-width: 1100px) {
      margin-bottom: 60px;
    }
    @media (max-width: 650px) {
      margin-bottom: 40px;
    }
  }

  .projects-tabs {
    max-width: 1200px;
    margin: 0 auto 60px auto;
    padding: 0 15px;
    overflow-x: auto;
    
    .projects-tabs-wrapper {
      display: flex;
      gap: 16px;
      min-width: fit-content;
      
      .tab-item {
        @include button-1;
        color: main(600);
        padding: 12px 24px;
        border: 1px solid main(200);
        border-radius: 8px;
        transition: all 0.2s ease-in-out;
        white-space: nowrap;
        
        &:hover {
          background: main(50);
          border-color: main(300);
        }
        
        &.active {
          background: main(100);
          border-color: main(400);
          color: main(700);
        }
      }
    }
  }
}

