@import '../../../assets/styles/new-typography';
@import '../../../assets/styles/new-palette';

.poem-banner {
    height: 100vh;
    min-height: clamp(600px, 100vh, 1024px);
    background-image: url('../../../assets/images/lending-steps/Lending\ -\ Web.webp');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    width: 100%;
    padding: clamp(85px, 15vh, 170px) 16px clamp(25px, 5vh, 50px);
    position: relative;
    overflow-x: hidden;

    @media (max-width: 650px) {
        min-height: fit-content;
        padding: clamp(85px, 12vh, 120px) 16px clamp(20px, 4vh, 40px);
    }

    h1 {
        @include gentium-book-plus-font;
        font-size: clamp(32px, 8vw, 96px);
        line-height: 100%;
        text-align: center;
        color: main(50);
        opacity: 1;
        transition: opacity 1s ease-in-out;

        &.fade-out {
            opacity: 0;
        }

        @media (max-width: 650px) {
            padding-top: 55px;
            font-size: clamp(28px, 7vw, 52px);
            line-height: 120%;
        }
    }

    .poem-section {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        background: rgb(69 38 0 / 32%);
        backdrop-filter: blur(8px);
        opacity: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: clamp(85px, 10vh, 108px) 25px 25px;
        transition: opacity 1s ease-in-out;
        z-index: -1;

        @media (max-width: 650px) {
            height: inherit;
            // align-items: flex-start;
            padding: clamp(85px, 8vh, 80px) 25px 25px;
        }

        @media (max-width: 500px) {
            // height: inherit;
            // align-items: flex-start;
            padding: 90px 25px 130px;
        }

        &.show {
            opacity: 1;
            z-index: 2;
        }

        .poem-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: clamp(20px, 3.2vh, 40px);

            .poem-column {
                display: flex;
                flex-direction: column;
                gap: clamp(8px, 1.5vh, 16px);
                // width: clamp(200px, 25vw, 340px);
                width: 100%;
                opacity: 0;
                transition: opacity 1s ease-in-out;

                @media (max-width: 650px) {
                    // width: clamp(180px, 35vw, 240px);
                }

                @media (max-width: 460px) {
                    // width: clamp(160px, 40vw, 200px);
                }

                &.show {
                    opacity: 1;
                }

                p {
                    @include font-base;
                    font-size: clamp(14px, 2.8vh, 30px);
                    line-height: clamp(18px, 3.5vh, 38px);
                    color: main(50);
                    opacity: 1;
                    margin: 0;

                    @media (max-width: 650px) {
                        font-size: clamp(12px, 2vh, 16px);
                        line-height: clamp(14px, 2.2vh, 16px);
                    }

                    @media (max-width: 460px) {
                        font-size: 12px;
                        line-height: 14px;
                    }

                    &:nth-child(2) {
                        padding-left: clamp(20px, 3vh, 40px);

                        @media (max-width: 650px) {
                            padding-left: clamp(15px, 2.5vh, 30px);
                        }
                    }

                    &:nth-child(3) {
                        padding-left: clamp(40px, 6vh, 80px);

                        @media (max-width: 650px) {
                            padding-left: clamp(30px, 5vh, 60px);
                        }
                    }

                    &:nth-child(4) {
                        padding-left: clamp(65px, 9vh, 130px);

                        @media (max-width: 650px) {
                            padding-left: clamp(50px, 8vh, 100px);
                        }
                    }
                }
            }
        }
    }

    .skip-section {
        position: absolute;
        bottom: 0;
        width: 100%;
        display: flex;
        right: 0;
        z-index: 5;

        .skip-inner {
            width: 100%;
            max-width: 1480px;
            margin: 0 auto;
            display: flex;
            justify-content: flex-end;
            padding: 20px 25px;

            .skip-button {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 20px;
                height: 70px;
                cursor: pointer;
                @media (max-width: 650px) {
                    height: 40px;
                    img {
                        height: 40px;
                    }
                }
            }
        }

        @media (max-width: 650px) {
            bottom: auto;
            top: 85px;
        }
    }
}

.faq-accordion-section {
    margin: clamp(60px, 12vh, 120px) 0;
}



//  TODO: Remove old styles after confirming new styles work as intended
// .poem-banner {
//     height: 100vh;
//     min-height: 1024px;
//     background-image: url('../../../assets/images/lending-steps/Lending\ -\ Web.webp');
//     background-size: cover;
//     background-position: center;
//     background-repeat: no-repeat;
//     width: 100%;
//     padding: 170px 16px 50px;
//     position: relative;
//     overflow-x: hidden;

//     @media (max-width: 650px) {
//         min-height: fit-content;
//     }
//     // @media (max-width: 500px) {
//     //     min-height: 812px;
//     // }

//     h1 {
//         @include gentium-book-plus-font;
//         font-size: 96px;
//         line-height: 100%;
//         text-align: center;
//         color: main(50);
//         opacity: 1;
//         transition: opacity 1s ease-in-out;

//         &.fade-out {
//             opacity: 0;
//         }

//         @media (max-width: 650px) {
//             font-size: 52px;
//             line-height: 120%;
//         }
//     }

//     .poem-section {
//         width: 100%;
//         height: 100%;
//         position: absolute;
//         top: 0;
//         left: 0;
//         background: rgb(69 38 0 / 32%);
//         backdrop-filter: blur(8px);
//         opacity: 0;
//         display: flex;
//         align-items: center;
//         justify-content: center;
//         padding: 108px 25px 25px;
//         transition: opacity 1s ease-in-out;

//         @media (max-width: 1100px) {
//             padding: 80px 25px 25px;
//         }
//         @media (max-width: 650px) {
//             height: inherit;
//         }
//         @media (max-width: 500px) {
//             padding-bottom: 80px;
//         }
//         @media (max-width: 500px) and (max-height: 750px) {
//             align-items: flex-start;
//         }

//         &.show {
//             opacity: 1;
//         }

//         .poem-content {
//             display: flex;
//             flex-direction: column;
//             align-items: center;
//             gap: 40px;

//             @media (max-width: 1100px) {
//                 gap: 30px;
//             }

//             @media (max-width: 500px) and (max-height: 750px) {
//                 gap: 20px;
//             }

//             .poem-column {
//                 display: flex;
//                 flex-direction: column;
//                 gap: 16px;
//                 width: 340px;
//                 opacity: 0;
//                 transition: opacity 1s ease-in-out;

//                 @media (max-width: 650px) {
//                     width: 240px;
//                 }
//                 @media (max-width: 460px) {
//                     width: 200px;
//                 }

//                 @media (max-width: 500px) and (max-height: 750px) {
//                     gap: 11px;
//                 }

//                 &.show {
//                     opacity: 1;
//                 }

//                 p {
//                     @include font-base;
//                     font-size: 30px;
//                     line-height: 38px;
//                     color: main(50);
//                     opacity: 1;
//                     margin: 0;

//                     @media (max-width: 1100px) {
//                         font-size: 28px;
//                         line-height: 36px;
//                     }
//                     @media (max-width: 650px) {
//                         font-size: 20px;
//                         line-height: 27px;
//                     }
//                     @media (max-width: 460px) {
//                         font-size: 16px;
//                         line-height: 22px;
//                     }

//                     @media (max-width: 500px) and (max-height: 700px) {
//                         font-size: 14px;
//                         line-height: 18px;
//                     }

//                     &:nth-child(2) {
//                         padding-left: 40px;

//                         @media (max-width: 650px) {
//                             padding-left: 30px;
//                         }
//                     }

//                     &:nth-child(3) {
//                         padding-left: 80px;

//                         @media (max-width: 650px) {
//                             padding-left: 60px;
//                         }
//                     }

//                     &:nth-child(4) {
//                         padding-left: 130px;

//                         @media (max-width: 650px) {
//                             padding-left: 100px;
//                         }
//                     }
//                 }
//             }
//         }
//     }
// }

// .faq-accordion-section {
//     margin: 120px 0;
// }