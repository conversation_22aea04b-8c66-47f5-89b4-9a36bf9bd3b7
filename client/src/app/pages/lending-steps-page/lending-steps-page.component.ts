import { Component, inject, OnInit, OnDestroy, signal, HostListener, PLATFORM_ID, viewChild, ElementRef } from '@angular/core';
import { timer, interval, take, switchMap, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { isPlatformBrowser } from '@angular/common';
import { TranslocoPipe } from '@jsverse/transloco';
import { PageTitleService } from '@/services/page-title.service';
import { HeaderStateService } from '@/services/header-state.service';
import { StepsSliderComponent } from '@/components/steps-slider/steps-slider.component';
import { FaqAccordionComponent, FaqItem } from '@/components/faq-accordion/faq-accordion.component';

@Component({
  selector: 'app-lending-steps-page',
  imports: [StepsSliderComponent, FaqAccordionComponent, TranslocoPipe],
  templateUrl: './lending-steps-page.component.html',
  standalone: true,
  styleUrl: './lending-steps-page.component.scss'
})
export class LendingStepsPageComponent implements OnInit, OnDestroy {
  readonly pageTitleService = inject(PageTitleService);
  readonly headerStateService = inject(HeaderStateService);
  private readonly platformId = inject(PLATFORM_ID);

  readonly showTitle = signal(true);
  readonly showPoemSection = signal(false);
  readonly showPoemColumns = signal<number[]>([]);
  readonly scrollBlocked = signal(true);

  readonly stepsSlider = viewChild<ElementRef<any>>('stepsSlider');
  readonly faqSection = viewChild<ElementRef<any>>('faqSection');

  private destroyRef = takeUntilDestroyed();
  private animationTriggered = false;
  private poemAnimationStarted = false;
  private animationCompleted = false;
  private readonly scrollThreshold = 100;

  faqItems: FaqItem[] = [
    {
      questionKey: 'Как долго занимает прохождение всех 16 стадий?',
      answerKey: 'Это зависит от вашей предыдущей кармы, чистоты самайи, решимости и усердия в практике. Для кого-то этот путь может занять годы, для других — несколько жизней. Важна не скорость, а качество и глубина прохождения каждой стадии. Нельзя перепрыгнуть через этап, не освоив его полностью, так же как нельзя построить второй этаж дома без прочного первого.'
    },
    {
      questionKey: 'Что делать, если я "застрял" на какой-то стадии?',
      answerKey: 'Это зависит от вашей предыдущей кармы, чистоты самайи, решимости и усердия в практике. Для кого-то этот путь может занять годы, для других — несколько жизней. Важна не скорость, а качество и глубина прохождения каждой стадии. Нельзя перепрыгнуть через этап, не освоив его полностью, так же как нельзя построить второй этаж дома без прочного первого.'
    },
    {
      questionKey: 'Можно ли пройти этот путь без Учителя?',
      answerKey: 'Это зависит от вашей предыдущей кармы, чистоты самайи, решимости и усердия в практике. Для кого-то этот путь может занять годы, для других — несколько жизней. Важна не скорость, а качество и глубина прохождения каждой стадии. Нельзя перепрыгнуть через этап, не освоив его полностью, так же как нельзя построить второй этаж дома без прочного первого.'
    },
    {
      questionKey: 'Не является ли цель стать "Богом-Творцом" проявлением эго?',
      answerKey: 'Это зависит от вашей предыдущей кармы, чистоты самайи, решимости и усердия в практике. Для кого-то этот путь может занять годы, для других — несколько жизней. Важна не скорость, а качество и глубина прохождения каждой стадии. Нельзя перепрыгнуть через этап, не освоив его полностью, так же как нельзя построить второй этаж дома без прочного первого.'
    },
    {
      questionKey: 'Нужно ли мне прилагать усилия в практике или просто "быть в потоке"?',
      answerKey: 'Это зависит от вашей предыдущей кармы, чистоты самайи, решимости и усердия в практике. Для кого-то этот путь может занять годы, для других — несколько жизней. Важна не скорость, а качество и глубина прохождения каждой стадии. Нельзя перепрыгнуть через этап, не освоив его полностью, так же как нельзя построить второй этаж дома без прочного первого.'
    },
    {
      questionKey: 'Что именно означает "создать свою мандалу"? Это реальный мир или просто воображение?',
      answerKey: 'Это зависит от вашей предыдущей кармы, чистоты самайи, решимости и усердия в практике. Для кого-то этот путь может занять годы, для других — несколько жизней. Важна не скорость, а качество и глубина прохождения каждой стадии. Нельзя перепрыгнуть через этап, не освоив его полностью, так же как нельзя построить второй этаж дома без прочного первого.'
    },
  ];

  ngOnInit(): void {
    this.pageTitleService.clearPageTitle();
    this.headerStateService.isScrolledApp.set(false);

    // Блокируем скролл при инициализации
    this.blockScroll();
  }

  ngOnDestroy(): void {
    // Разблокируем скролл при уходе с компонента
    this.unblockScroll();

    if (isPlatformBrowser(this.platformId)) {
      const isScrolled = window.scrollY > 20;
      this.headerStateService.isScrolledApp.set(isScrolled);
    }
  }

  @HostListener('window:scroll', ['$event'])
  onWindowScroll(event: Event): void {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    // Если скролл заблокирован, предотвращаем скролл
    if (this.scrollBlocked()) {
      event.preventDefault();
      window.scrollTo(0, 0);
      return;
    }

    const scrollY = window.scrollY;

    // Запускаем анимацию когда пользователь проскролил больше порога
    if (scrollY > this.scrollThreshold && !this.animationTriggered) {
      this.animationTriggered = true;
      this.startAnimation();
    }
  }

  @HostListener('wheel', ['$event'])
  onWheel(event: WheelEvent): void {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    // Если скролл заблокирован и пользователь пытается скроллить вниз
    if (this.scrollBlocked() && event.deltaY > 0) {
      // Запускаем анимацию при попытке скролла
      if (!this.animationTriggered) {
        this.animationTriggered = true;
        this.startAnimation();
      }
      event.preventDefault();
      return;
    }
  }

  @HostListener('keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    // Блокируем клавиши скролла если скролл заблокирован
    if (this.scrollBlocked()) {
      const scrollKeys = ['ArrowDown', 'ArrowUp', 'PageDown', 'PageUp', 'Home', 'End', ' '];
      if (scrollKeys.includes(event.key)) {
        // Запускаем анимацию при попытке скролла клавишами
        if (!this.animationTriggered && (event.key === 'ArrowDown' || event.key === 'PageDown' || event.key === ' ')) {
          this.animationTriggered = true;
          this.startAnimation();
        }
        event.preventDefault();
      }
    }
  }

  onPoemBannerClick(): void {
    if (!this.scrollBlocked()) {
      return;
    }

    // Простая обработка одиночного клика
    if (!this.animationTriggered) {
      this.animationTriggered = true;
      this.startAnimation();
    }
  }

  onPoemBannerDoubleClick(): void {
    if (!this.scrollBlocked()) {
      return;
    }

    // При двойном клике сразу показываем поэму и скроллим к слайдеру
    this.showTitle.set(false);
    this.showPoemSection.set(true);
    this.showPoemColumns.set([0, 1, 2, 3, 4]);
    this.animationTriggered = true;
    this.poemAnimationStarted = true;
    this.animationCompleted = true;

    // Разблокируем скролл и скроллим к слайдеру
    this.unblockScroll();

    setTimeout(() => {
      this.scrollToStepsSlider();
    }, 100);
  }

  onSkipClick(event: Event): void {
    event.stopPropagation();

    if (!this.animationTriggered) {
      // Анимация не запущена: запускаем анимацию поэмы
      this.animationTriggered = true;
      this.startAnimation();
    } else if (this.poemAnimationStarted && !this.animationCompleted) {
      // Анимация в процессе: завершаем анимацию немедленно и скроллим к слайдеру
      this.completeAnimationAndScroll();
    } else {
      // Анимация завершена: просто скроллим к слайдеру
      this.scrollToStepsSlider();
    }
  }

  onSkipDoubleClick(event: Event): void {
    event.stopPropagation();

    // При двойном клике на скип сразу скроллим к слайдеру
    this.showTitle.set(false);
    this.showPoemSection.set(true);
    this.showPoemColumns.set([0, 1, 2, 3, 4]);
    this.animationTriggered = true;
    this.poemAnimationStarted = true;
    this.animationCompleted = true;

    this.unblockScroll();

    setTimeout(() => {
      this.scrollToStepsSlider();
    }, 100);
  }

  private startAnimation(): void {
    this.poemAnimationStarted = true;
    this.animationCompleted = false;

    // Первая фаза: скрыть заголовок и показать секцию поэмы
    this.showTitle.set(false);
    this.showPoemSection.set(true);

    // Вторая фаза: показать колонки с интервалом
    timer(500).pipe(
      switchMap(() => interval(1500).pipe(take(5))), // 5 колонок с интервалом 1.5 сек
      tap((columnIndex: number) => {
        this.showPoemColumns.update((columns: number[]) => [...columns, columnIndex]);
      }),
      this.destroyRef
    ).subscribe({
      complete: () => {
        // После завершения анимации разблокируем скролл
        setTimeout(() => {
          this.unblockScroll();
          this.animationCompleted = true;
        }, 2000); // Даем время насладиться полной поэмой =) 
      }
    });
  }

  private completeAnimationAndScroll(): void {
    // Показываем все колонки сразу
    this.showPoemColumns.set([0, 1, 2, 3, 4]);

    // Помечаем анимацию как завершенную
    this.animationCompleted = true;

    // Разблокируем скролл
    this.unblockScroll();

    setTimeout(() => {
      this.scrollToStepsSlider();
    }, 300);
  }

  private scrollToStepsSlider(): void {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    const stepsSliderElement = this.stepsSlider()?.nativeElement as HTMLElement;

    if (stepsSliderElement) {
      // Плавно скроллим к элементу
      stepsSliderElement.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    } else {
      // Если элемент не найден, скроллим к высоте экрана
      window.scrollTo({
        top: window.innerHeight,
        behavior: 'smooth'
      });
    }
  }

  private blockScroll(): void {
    if (isPlatformBrowser(this.platformId)) {
      this.scrollBlocked.set(true);
      document.body.style.overflow = 'hidden';
      document.body.style.position = 'fixed';
      document.body.style.top = '0';
      document.body.style.left = '0';
      document.body.style.right = '0';
    }
  }

  private unblockScroll(): void {
    if (isPlatformBrowser(this.platformId)) {
      this.scrollBlocked.set(false);
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.top = '';
      document.body.style.left = '';
      document.body.style.right = '';
    }
  }

  onSliderScrolled(hasScrolledPast: boolean): void {
    this.headerStateService.isScrolledApp.set(hasScrolledPast);
  }

  onSkipToFaq(): void {
    this.scrollToFaqSection();
  }

  private scrollToFaqSection(): void {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    const faqElement = this.faqSection()?.nativeElement as HTMLElement;
    console.log('Scrolling to FAQ section:', faqElement);
    
    if (faqElement) {
      // Плавно скроллим к FAQ секции
      faqElement.scrollIntoView({
        // behavior: 'smooth', // если нужно плавнее то разблокировать
        block: 'start'
      });
    }
  }
}
