<div class="poem-banner" [class.scroll-enabled]="!scrollBlocked()" (click)="onPoemBannerClick()">
  <h1 [class.fade-out]="!showTitle()" [innerHTML]="'lending-steps.banner.title' | transloco"></h1>

  <div class="poem-section" [class.show]="showPoemSection()">
    <div class="poem-content">
      <div class="poem-column" [class.show]="showPoemColumns().includes(0)">
        <p>{{ 'lending-steps.poem.column1.line1' | transloco }}</p>
        <p>{{ 'lending-steps.poem.column1.line2' | transloco }}</p>
        <p>{{ 'lending-steps.poem.column1.line3' | transloco }}</p>
      </div>

      <div class="poem-column" [class.show]="showPoemColumns().includes(1)">
        <p>{{ 'lending-steps.poem.column2.line1' | transloco }}</p>
        <p>{{ 'lending-steps.poem.column2.line2' | transloco }}</p>
        <p>{{ 'lending-steps.poem.column2.line3' | transloco }}</p>
      </div>

      <div class="poem-column" [class.show]="showPoemColumns().includes(2)">
        <p>{{ 'lending-steps.poem.column3.line1' | transloco }}</p>
        <p>{{ 'lending-steps.poem.column3.line2' | transloco }}</p>
      </div>

      <div class="poem-column" [class.show]="showPoemColumns().includes(3)">
        <p>{{ 'lending-steps.poem.column4.line1' | transloco }}</p>
        <p>{{ 'lending-steps.poem.column4.line2' | transloco }}</p>
        <p>{{ 'lending-steps.poem.column4.line3' | transloco }}</p>
      </div>

      <div class="poem-column" [class.show]="showPoemColumns().includes(4)">
        <p>{{ 'lending-steps.poem.column5.line1' | transloco }}</p>
        <p>{{ 'lending-steps.poem.column5.line2' | transloco }}</p>
        <p>{{ 'lending-steps.poem.column5.line3' | transloco }}</p>
        <p>{{ 'lending-steps.poem.column5.line4' | transloco }}</p>
      </div>
    </div>
  </div>
  <div class="skip-section">
    <div class="skip-inner">
      <div class="skip-button" (click)="onSkipClick($event)" (dblclick)="onSkipDoubleClick($event)">
        <img src="../../../assets/images/lending-steps/skip-button.webp" alt="skip arrow">
      </div>
    </div>
  </div>
</div>

<app-steps-slider 
  #stepsSlider 
  (sliderScrolled)="onSliderScrolled($event)"
  (skipToNextSection)="onSkipToFaq()">
</app-steps-slider>

<div #faqSection class="faq-accordion-section">
  <app-faq-accordion [faqItems]="faqItems" [title]="'lending-steps.faq.title' | transloco"></app-faq-accordion>
</div>