<div class="middle_stripe">
  <!-- <breadcrumb></breadcrumb> -->
  <div class="wrapper_line">
    <!-- <div class="dec_head _background">
      <img class="carn_b_" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
      <h1 class="dec_head-title_">{{ 'event.diwali.title' | transloco }}</h1>
      <img class="carn_b_ rotate" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
    </div> -->
    <div class="flex flex-col relative justify-center">
      <p class="plane_h">{{ 'event.ashram.anniversary' | transloco }}</p>

      <div class="plane_sf_wrapper">
        <p class="plane_sf">{{ 'event.diwali.description' | transloco }}</p>
      </div>

      <div class="m-auto frame_envelope">
        <img src="assets/images/Rectangle 704.jpg" alt="image">
      </div>

      <!-- Program Section -->
      <div class="program-section">
        <h3 class="section-title">{{ 'event.program.title' | transloco }}</h3>
        <div class="program-items">
          <p class="program-item">{{ 'event.program.homas' | transloco }}</p>
          <p class="program-item">{{ 'event.program.tarpan' | transloco }}</p>
          <p class="program-item">{{ 'event.program.marjan' | transloco }}</p>
          <p class="program-item">{{ 'event.program.daily_sadhana' | transloco }}</p>
          <p class="program-item">{{ 'event.program.final_homa' | transloco }}</p>
        </div>
      </div>

      <!-- Guru Participation Section -->
      <div class="guru-section">
        <h3 class="section-title">{{ 'event.guru.participation_title' | transloco }}</h3>
        <div class="guru-items">
          <p class="guru-item">{{ 'event.guru.diwali' | transloco }}</p>
          <p class="guru-item">{{ 'event.guru.vyasa_puja' | transloco }}</p>
          <p class="guru-item">{{ 'event.guru.ashram_birthday' | transloco }}</p>
          <p class="guru-note">{{ 'event.guru.other_days' | transloco }}</p>
        </div>
      </div>

      <!-- Daily Offerings Section -->
      <div class="offerings-section">
        <p class="offerings-text">{{ 'event.daily_offerings' | transloco }}</p>
      </div>

      <!-- Invitation Section -->
      <div class="invitation-section">
        <p class="invitation-text">{{ 'event.invitation' | transloco }}</p>
      </div>

      <!-- <div class="button-wrapper">
        <a class="button-content" (click)="navigateToArticle()">
          <div class="button_cont-wrap">{{ 'forum.more_details' | transloco }}</div>
        </a>
      </div> -->
    </div>
    <form class="payment-form" [formGroup]="paymentForm" (ngSubmit)="createPayment()">
      <div class="form-control">
        <app-pay-form [paymentForm]="paymentForm" [commentText]="'event.diwali.donation_comment' | transloco"></app-pay-form>
      </div>

      <div class="plane_s_w">
        <p class="plane_s">{{ 'event.ashram.loan_description' | transloco }}</p>
      </div>

      <!-- Payment Information Section -->
      <div class="payment-info-section">
        <h3 class="payment-title">{{ 'event.payment.title' | transloco }}</h3>
        <div class="payment-methods">
          <p class="payment-method">{{ 'event.payment.tinkoff' | transloco }}</p>
          <p class="payment-method">{{ 'event.payment.sberbank' | transloco }}</p>
          <p class="payment-method">{{ 'event.payment.privat' | transloco }}</p>
          <p class="payment-method">{{ 'event.payment.paypal' | transloco }}</p>
          <p class="payment-method">{{ 'event.payment.usdt' | transloco }}</p>
          <p class="payment-method">{{ 'event.payment.wise' | transloco }}</p>
          <p class="payment-method">{{ 'event.payment.revolut' | transloco }}</p>
        </div>
      </div>
    </form>
  </div>
</div>