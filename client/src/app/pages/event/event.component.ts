import { BreadcrumbComponent } from "@/components/breadcrumb/breadcrumb.component"
import { PayFormComponent } from '@/components/pay-form/pay-form.component'
import { DonationService } from '@/services/donation.service'
import { ToasterService } from '@/services/toaster.service'
import { CommonModule, NgOptimizedImage } from "@angular/common"
import { Component, inject } from '@angular/core'
import { FormBuilder, ReactiveFormsModule, Validators } from "@angular/forms"
import { TranslocoModule, TranslocoService } from '@jsverse/transloco'
import { Router } from '@angular/router'
import { PageTitleService } from "@/services/page-title.service"

@Component({
  selector: 'app-event',
  standalone: true,
  imports: [
    BreadcrumbComponent,
    NgOptimizedImage,
    CommonModule,
    PayFormComponent,
    ReactiveFormsModule,
    TranslocoModule,
  ],
  templateUrl: './event.component.html',
  styleUrl: './event.component.scss'
})
export class EventComponent {
  fb = inject(FormBuilder)
  donationService = inject(DonationService)
  toasterService = inject(ToasterService)
  translocoService = inject(TranslocoService)
  router = inject(Router)
  readonly pageTitleService = inject(PageTitleService);

  paymentForm = this.fb.group({
    sum: [null as number | null, [Validators.required, Validators.min(100)]],
    type: ['yookassa', Validators.required],
    autoRenew: [false],
    comment: [null, Validators.required],
  })

  ngOnInit() {
    this.pageTitleService.setPageTitle('event.diwali.title');
    this.paymentForm.get('type')?.valueChanges.subscribe(type => {
      const sumControl = this.paymentForm.get('sum');
      if (type === 'stripe') {
        sumControl?.setValidators([Validators.required, Validators.min(10)]);
        if (sumControl?.value && sumControl.value < 10) {
          sumControl.setValue(10);
        }
      } else if (type === 'yookassa') {
        sumControl?.setValidators([Validators.required, Validators.min(100)]);
        if (sumControl?.value && sumControl.value < 100) {
          sumControl.setValue(100);
        }
      }
      sumControl?.updateValueAndValidity();
    });
  }

  createPayment() {
    if (this.paymentForm.valid) {
      const formValue = this.paymentForm.value;
      const currency = formValue.type === 'stripe' ? 'EUR' : 'RUB';
      const minAmount = formValue.type === 'stripe' ? 10 : 100;

      if (!formValue.type) {
        this.toasterService.showToast('Пожалуйста, выберите платежную систему', 'warning', 'bottom-middle');
        return;
      }

      if (!formValue.sum || formValue.sum < minAmount) {
        const currencySymbol = currency === 'EUR' ? '€' : '₽';
        this.toasterService.showToast(`Минимальная сумма: ${minAmount} ${currencySymbol}`, 'warning', 'bottom-middle');
        return;
      }

      this.donationService.createPayment(formValue).subscribe({
        next: (res: any) => {
          this.toasterService.showToast('Перенаправление на страницу оплаты...', 'success', 'bottom-middle', 2000);
          setTimeout(() => {
            location.href = res.paymentUrl;
          }, 1000);
        },
        error: () => {
          this.toasterService.showToast('Ошибка при создании платежа. Попробуйте еще раз.', 'error', 'bottom-middle');
        }
      })
    } else {
      this.paymentForm.markAllAsTouched();
      this.toasterService.showToast('Пожалуйста, заполните все обязательные поля корректно', 'warning', 'bottom-middle');
    }
  }

  navigateToArticle() {
    const currentLang = this.translocoService.getActiveLang();
    window.open(`https://sanatanadharma.world/${currentLang}/categories/61/divali-prazdnik-sveta-i-sily`, '_blank');
  }
}
