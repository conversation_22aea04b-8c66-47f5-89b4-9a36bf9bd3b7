.plane_h {
    font-family: Prata;
    font-weight: 400;
    font-size: 32px;
    line-height: 44px;
    text-align: center;
    vertical-align: middle;
    color: var(--font-color);
    max-width: 689px;
    margin: 0 auto;
}

.plane_s {
    font-family: Prata;
    font-weight: 400;
    font-size: 16px;
    line-height: 22px;
    text-align: center;
    vertical-align: middle;
    color: var(--font-color);
    max-width: 689px;
    margin: 0 auto;
}

.plane_s_w {
    margin-top: 29px;
}

.plane_sf_wrapper {
    margin: 57px auto 40px auto;
    // max-width: 800px;
    // padding: 50px 40px;
    // background: linear-gradient(135deg, rgba(255, 243, 224, 0.6) 0%, rgba(255, 235, 205, 0.4) 100%);
    // border-radius: 16px;
    // border: 2px solid rgba(255, 215, 160, 0.5);
    // box-shadow: 0 4px 20px rgba(53, 31, 4, 0.08);
    // position: relative;

    // &::before {
    //     content: '✨';
    //     position: absolute;
    //     top: 16px;
    //     left: 16px;
    //     font-size: 24px;
    //     opacity: 0.7;
    // }

    // &::after {
    //     content: '✨';
    //     position: absolute;
    //     bottom: 16px;
    //     right: 16px;
    //     font-size: 24px;
    //     opacity: 0.7;
    // }
}

.plane_sf {
    font-family: Prata;
    font-weight: 400;
    font-size: 20px;
    line-height: 32px;
    color: #351F04;
    text-align: center;
    margin: 0;
    position: relative;
    z-index: 1;
    letter-spacing: 0.3px;
}

.button-wrapper {
    display: flex;
    justify-content: center;
    margin: 40px auto;

    .button-content {
        cursor: pointer;

        .button_cont-wrap {
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--button_);
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            width: 290px;
            height: 50px;
            transition: all .2s;
            background-size: contain;
            background-repeat: no-repeat;
            font-family: Prata;
            font-weight: 400;
            font-size: 20px;
            line-height: 20px;
            color: var(--font-color1);

            &:hover {
                background: var(--button_figure);
                transition: all .2s;
                background-size: contain;
                background-repeat: no-repeat;
                color: rgba(255, 255, 255, 1);
            }
        }
    }
}

.frame_envelope {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 610px;
    height: 268px;
    background: url(../../../assets/images/Frame\ 3284.svg);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;

    img {
        // width: 360px;
        max-height: 95%;
        border-radius: 20px;
    }
}

// Responsive design
@media (max-width: 768px) {
    .dec_head-title_ {
        font-size: 40px !important;
        padding: 4px 0 !important;
    }

    .dec_head._background {
        margin-top: -137px;
        background-size: 394px;
        height: fit-content;
        padding: 70px 0;

        img {
            width: 100%;
            height: auto;
        }
    }

    .plane_h {
        font-size: 28px;
        line-height: 38px;
        max-width: 90%;
        padding: 0 20px;
    }

    .plane_sf {
        font-size: 18px;
        line-height: 28px;
    }

    .frame_envelope {
        width: 90%;
        max-width: 500px;
        height: auto;
        min-height: 220px;
    }
}

@media (max-width: 600px) {
    .plane_h {
        font-size: 24px;
        line-height: 32px;
    }

    .plane_sf_wrapper {
        margin: 40px auto 30px auto;
        padding: 0 20px;
    }

    .plane_sf {
        font-size: 16px;
        line-height: 24px;
    }

    .frame_envelope {
        width: 95%;
        min-height: 200px;
    }

    .button-wrapper {
        margin: 30px auto;

        .button_cont-wrap {
            width: 250px;
            height: 45px;
            font-size: 18px;
        }
    }

    .plane_s_w {
        margin-top: 20px;
        padding: 0 20px;
    }

    .plane_s {
        font-size: 14px;
        line-height: 20px;
    }
}

@media (max-width: 570px) {
    .dec_head._background {
        img {
            display: none;
        }
    }

    .dec_head-title_ {
        font-size: 32px !important;
        line-height: 32px !important;
    }

    .dec_head._background {
        margin-top: -145px;
        background-size: 339px;
        padding: 60px 0;
    }
}

@media (max-width: 450px) {
    .plane_h {
        font-size: 20px;
        line-height: 28px;
        padding: 0 15px;
    }

    .plane_sf_wrapper {
        margin: 30px auto 25px auto;
        padding: 0 15px;
    }

    .plane_sf {
        font-size: 15px;
        line-height: 22px;
    }

    .frame_envelope {
        width: 100%;
        min-height: 180px;
        padding: 0 10px;
    }

    .button-wrapper {
        margin: 25px auto;

        .button_cont-wrap {
            width: 220px;
            height: 42px;
            font-size: 16px;
        }
    }

    .dec_head-title_ {
        font-size: 28px !important;
        line-height: 28px !important;
    }
}

@media (max-width: 370px) {
    .plane_h {
        font-size: 18px;
        line-height: 24px;
    }

    .plane_sf {
        font-size: 14px;
        line-height: 20px;
    }

    .button-wrapper {
        .button_cont-wrap {
            width: 200px;
            height: 40px;
            font-size: 15px;
        }
    }

    .dec_head-title_ {
        font-size: 24px !important;
        line-height: 24px !important;
    }
}

// New sections styling
.program-section,
.guru-section,
.offerings-section,
.invitation-section,
.payment-info-section {
    margin: 40px auto;
    max-width: 800px;
    padding: 0 20px;
}

.section-title,
.payment-title {
    font-family: Prata;
    font-weight: 400;
    font-size: 24px;
    line-height: 32px;
    text-align: center;
    color: var(--font-color);
    margin-bottom: 24px;
}

.program-items,
.guru-items {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.program-item,
.guru-item {
    font-family: Prata;
    font-weight: 400;
    font-size: 18px;
    line-height: 28px;
    color: #351F04;
    text-align: left;
    padding-left: 8px;
}

.guru-note {
    font-family: Prata;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: #351F04;
    text-align: center;
    font-style: italic;
    margin-top: 8px;
}

.offerings-text,
.invitation-text {
    font-family: Prata;
    font-weight: 400;
    font-size: 18px;
    line-height: 28px;
    color: #351F04;
    text-align: center;
    max-width: 700px;
    margin: 0 auto;
}

.invitation-text {
    font-size: 20px;
    line-height: 32px;
}

.payment-methods {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 20px;
}

.payment-method {
    font-family: 'Courier New', monospace;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #351F04;
    text-align: left;
    background: rgba(255, 243, 224, 0.4);
    padding: 12px 16px;
    border-radius: 8px;
    border-left: 3px solid rgba(255, 215, 160, 0.6);
    word-break: break-all;
}

// Responsive adjustments for new sections
@media (max-width: 768px) {
    .section-title,
    .payment-title {
        font-size: 22px;
        line-height: 30px;
    }

    .program-item,
    .guru-item {
        font-size: 16px;
        line-height: 26px;
    }

    .offerings-text,
    .invitation-text {
        font-size: 16px;
        line-height: 26px;
    }

    .invitation-text {
        font-size: 18px;
        line-height: 28px;
    }
}

@media (max-width: 600px) {
    .program-section,
    .guru-section,
    .offerings-section,
    .invitation-section,
    .payment-info-section {
        margin: 30px auto;
        padding: 0 15px;
    }

    .section-title,
    .payment-title {
        font-size: 20px;
        line-height: 28px;
        margin-bottom: 20px;
    }

    .program-item,
    .guru-item {
        font-size: 15px;
        line-height: 24px;
    }

    .guru-note {
        font-size: 14px;
        line-height: 22px;
    }

    .offerings-text,
    .invitation-text {
        font-size: 15px;
        line-height: 24px;
    }

    .invitation-text {
        font-size: 16px;
        line-height: 26px;
    }

    .payment-method {
        font-size: 13px;
        line-height: 20px;
        padding: 10px 14px;
    }
}

@media (max-width: 450px) {
    .program-section,
    .guru-section,
    .offerings-section,
    .invitation-section,
    .payment-info-section {
        margin: 25px auto;
        padding: 0 10px;
    }

    .section-title,
    .payment-title {
        font-size: 18px;
        line-height: 26px;
        margin-bottom: 16px;
    }

    .program-item,
    .guru-item {
        font-size: 14px;
        line-height: 22px;
    }

    .guru-note {
        font-size: 13px;
        line-height: 20px;
    }

    .offerings-text,
    .invitation-text {
        font-size: 14px;
        line-height: 22px;
    }

    .invitation-text {
        font-size: 15px;
        line-height: 24px;
    }

    .payment-method {
        font-size: 12px;
        line-height: 18px;
        padding: 8px 12px;
    }
}

@media (max-width: 370px) {
    .section-title,
    .payment-title {
        font-size: 16px;
        line-height: 24px;
    }

    .program-item,
    .guru-item {
        font-size: 13px;
        line-height: 20px;
    }

    .offerings-text,
    .invitation-text {
        font-size: 13px;
        line-height: 20px;
    }

    .invitation-text {
        font-size: 14px;
        line-height: 22px;
    }

    .payment-method {
        font-size: 11px;
        line-height: 16px;
    }
}