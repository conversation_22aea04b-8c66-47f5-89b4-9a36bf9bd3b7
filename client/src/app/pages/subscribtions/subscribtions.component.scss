.plane_h {
    font-family: Prata;
    font-weight: 400;
    font-size: 32px;
    line-height: 44px;
    text-align: center;
    vertical-align: middle;
    color: var(--font-color);
    max-width: 689px;
    margin: 0 auto;
}

.wrapper_line {
    width: 1440px;
    max-width: unset;
}

.middle_stripe {
    width: 1440px;
    max-width: unset;
}

.plane_s {
    font-family: Prata;
    font-weight: 400;
    font-size: 16px;
    line-height: 22px;
    text-align: center;
    vertical-align: middle;
    color: var(--font-color);
    max-width: 689px;
    margin: 0 auto;
}

.cat_wrap {
    margin: 0 auto 20px auto;
    max-width: 1440px;
}

.plane_s_w {
    margin-top: 29px;
}

.curr_sw_wrap {
    position: relative;
    margin-left: 42px;
    cursor: pointer;
}

.curr_sw_wrap:hover {
    .currcy_switch {
        opacity: 1;
        z-index: 1;
        transition: 0s;
    }

    .curr_s_btn {
        img {
            transform: rotate(180deg);
            transition: .3s;
        }
    }
}

.currcy_switch {
    opacity: 0;
    z-index: -1;
    transition: .5s;
    right: -5px;
    position: absolute;
    top: 5px;
    border: 2px solid #EDC17F;
    border-radius: 8px;
    width: 100px;
    margin-top: 30px;
}

.curr_s_btn {
    display: flex;
    align-items: center;
    font-family: Prata;
    font-weight: 400;
    font-size: 16px;
    line-height: 1;
    color: #351F04;

    p {
        line-height: 0;
    }

    img {
        display: block;
        margin-left: 12px;
        margin-top: -3px;
        transition: .3s;
    }
}

.c-selection {
    font-family: Prata;
    font-weight: 400;
    font-size: 16px;
    color: #351F04;
    line-height: 40px;
    color: #351F04;
    padding: 12px 27px 0 12px;
    white-space: nowrap;
}

.pres_wr {
    display: flex;
    align-items: center;
    width: fit-content;
    height: 31px;
    opacity: 0.3;
    border: 1px solid #959D72;
    border-radius: 6px;
    padding: 10px 8px;
    font-family: Prata;
    font-weight: 400;
    font-size: 14px;
    line-height: 19px;
    color: #313520;
    cursor: pointer;
    margin-right: 7px;
}

.th_wrapper {
    display: flex;
    align-items: center;
    margin: 63px auto;
    width: fit-content;
}

.swth_wrappr {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 322px;
    height: 46px;
    background: url(../../../assets/images/Body.svg);
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    padding: 0 4px;
    cursor: pointer;

    .sw_item_w {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 160px;
        height: 38px;
    }

    .sw_item_w.active {
        background: url(../../../assets/images/Active\ block.svg);
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
    }
}

.sw_item_w {
    font-family: Prata;
    font-weight: 400;
    font-size: 20px;
    line-height: 27px;
    text-align: center;
    color: #532E00;
}

.main_pl_wrap {
    display: flex;
}

.main_pls_wrap {
    width: 64.6%;
}

.main_plfx_wrap {
    padding-top: 90px;
}

.card_pcj {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 450px;
    min-height: 418px;
    background: url(../../../assets/images/Body_p.svg) no-repeat center center;
    background-size: auto 100%;
    padding: 45px 40px 0 40px;
}

.title_pcj {
    font-family: Prata;
    font-weight: 400;
    font-size: 24px;
    line-height: 32px;
    margin-bottom: 30px;
    color: #261604;
}

.price_pcj {
    font-family: Prata;
    font-weight: 400;
    font-size: 36px;
    line-height: 48px;
    color: #532E00;
    margin-right: 10px;
}

.old-price_pcj {
    text-decoration: line-through;
    font-family: Prata;
    font-weight: 400;
    font-size: 36.65px;
    line-height: 48.87px;
    text-decoration: line-through;
    color: #dccaa6;
    margin-right: 10px;
}

.per-month_pcj {
    font-family: Prata;
    font-weight: 400;
    font-size: 24px;
    line-height: 32px;
    color: #99601A;
}

.pacjs_wr {
    display: flex;
    margin-top: 40px;
}

.card_pcj:first-of-type {
    margin-right: 30px;
}

.discount_pcj {
    display: flex;
    align-items: center;
    padding: 0 8px;
    margin: 5px 0 27px 0;
    min-width: 148px;
    width: fit-content;
    height: 31px;
    border-radius: 6px;
    border: 1px solid #959D72;
    background-color: #D5DDAF;
    font-family: Prata;
    font-weight: 400;
    font-size: 14px;
    line-height: 0;
    color: #313520;
    cursor: pointer;
    white-space: nowrap;
}

.description_pcj {
    font-family: Prata;
    font-weight: 400;
    font-size: 18px;
    line-height: 24px;
    color: #532E00;
}

.btn_pcj {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    background: url(../../../assets/images/Bode.svg);
    font-family: Prata;
    font-weight: 400;
    font-size: 18px;
    line-height: 24px;
    text-align: center;
    cursor: pointer;
    width: 170px;
    height: 40px;
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    margin-top: 36px;

    &.bf_ {
        background: url(../../../assets/images/Bode_f.svg);
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
    }

    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background-size: cover;
        background-repeat: no-repeat;
    }
}

.summary-card_pcs {
    display: flex;
    flex-direction: column;
    justify-content: center;
    background: url(../../../assets/images/Body_f.svg);
    width: 450px;
    height: 698px;
    background-size: cover;
    background-repeat: no-repeat;
    margin-left: 30px;
    padding: 0 45px;
    position: sticky;
    top: 140px;
    align-self: flex-start;
}

.summary-title_pcs {
    font-family: Prata;
    font-weight: 400;
    font-size: 24px;
    line-height: 32px;
    color: #99601A;
    margin-bottom: 40px;
}

.item_pcs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    span {
        font-family: Prata;
        font-weight: 400;
        font-size: 20px;
        line-height: 27px;
        color: #261604;
    }

    span.remove_pcs {
        color: #b97f39;
        font-size: 28px;
        font-weight: 600;
        cursor: pointer;
        margin-left: 18px;
    }
}

.item-price_pcs {
    display: flex;
    align-items: center;
    font-family: Prata;
    font-weight: 400;
    font-size: 32px;
    line-height: 44px;
    color: #532E00;
}

.divider_pcs {
    border-top: 1px solid #EDC17F;
    margin: 5px 0;
}

.total_pcs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: Prata;
    font-weight: 400;
    font-size: 20px;
    line-height: 27px;
    color: #261604;
    margin-top: 21px;
}

.total-price_pcs {
    font-family: Prata;
    font-weight: 400;
    font-size: 36px;
    line-height: 48px;
    color: #532E00;

    span {
        font-family: Prata;
        font-weight: 400;
        font-size: 24px;
        line-height: 32px;
        color: #99601A;
    }
}

.note_pcs {
    font-family: Prata;
    font-weight: 400;
    font-size: 18px;
    line-height: 24px;
    color: #532E00;
    margin-top: 40px;
}

.plates_s_wrap {
    .plane_h {
        margin: 100px auto 50px auto;
    }
}

.package-card_plate {
    background-color: #FEF1CF;
    border: 2px solid #EDC17F;
    padding: 34px 30px 36px 40px;
    width: 100%;
    border-radius: 16px;
    position: relative;
    margin-bottom: 30px;
}

.header_plate {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.title_plate {
    font-family: Prata;
    font-weight: 400;
    font-size: 24px;
    line-height: 32px;
    color: #261604;
}

.price_plate {
    font-family: Prata;
    font-weight: 400;
    font-size: 36px;
    line-height: 48px;
    color: #532E00;
}

.per-month_plate {
    font-family: Prata;
    font-weight: 400;
    font-size: 24px;
    line-height: 32px;
    color: #99601A;
}

.checkbox_plate {
    margin-left: 40px;
    transform: scale(2.4);
    cursor: pointer;
}

.description_plate {
    font-family: Prata;
    font-weight: 400;
    font-size: 16px;
    line-height: 22px;
    color: #532E00;
}

.features_plate {
    display: flex;
    justify-content: space-between;
    margin-top: 38px;
}

.column_plate {
    flex: 1;
}

.column-title_plate {
    font-family: Prata;
    font-weight: 400;
    font-size: 18px;
    line-height: 24px;
    color: #99601A;
    margin-bottom: 21px;
}

.feature_plate {
    display: flex;
    align-items: center;
    font-family: Prata;
    font-weight: 400;
    font-size: 16px;
    line-height: 22px;
    color: #261604;
    margin-bottom: 15px;
}

.albr {
    .feature_plate {
        &.ic_o::before {
            content: "";
            background-image: url(assets/images/icons/ph11.svg);
            width: 20px;
            height: 20px;
            margin-right: 7px;
            margin-top: -6px;
        }

        &.ic_tr::before {
            content: "";
            background-image: url(assets/images/icons/ph13.svg);
            width: 20px;
            height: 20px;
            margin-right: 7px;
            margin-top: -6px;
        }

        &.ic_fv::before {
            content: "";
            background-image: url(assets/images/icons/ph15.svg);
            width: 20px;
            height: 20px;
            margin-right: 7px;
            margin-top: -6px;
        }

        &.ic_t::before {
            content: "";
            background-image: url(assets/images/icons/ph12.svg);
            width: 20px;
            height: 20px;
            margin-right: 7px;
            margin-top: -6px;
        }

        &.ic_f::before {
            content: "";
            background-image: url(assets/images/icons/ph14.svg);
            width: 20px;
            height: 20px;
            margin-right: 7px;
            margin-top: -6px;
        }
    }
}

.llbr {
    .feature_plate {
        &.ic_o::before {
            content: "";
            background-image: url(assets/images/icons/ph21.svg);
            width: 20px;
            height: 20px;
            margin-right: 7px;
            margin-top: -6px;
        }

        &.ic_tr::before {
            content: "";
            background-image: url(assets/images/icons/ph23.svg);
            width: 20px;
            height: 20px;
            margin-right: 7px;
            margin-top: -6px;
        }

        &.ic_fv::before {
            content: "";
            background-image: url(assets/images/icons/ph25.svg);
            width: 20px;
            height: 20px;
            margin-right: 7px;
            margin-top: -6px;
        }

        &.ic_t::before {
            content: "";
            background-image: url(assets/images/icons/ph22.svg);
            width: 20px;
            height: 20px;
            margin-right: 7px;
            margin-top: -6px;
        }

        &.ic_f::before {
            content: "";
            background-image: url(assets/images/icons/ph24.svg);
            width: 20px;
            height: 20px;
            margin-right: 7px;
            margin-top: -6px;
        }

        &.ic_sx::before {
            content: "";
            background-image: url(assets/images/icons/ph26.svg);
            width: 20px;
            height: 20px;
            margin-right: 7px;
            margin-top: -6px;
        }
    }
}

.klbr {
    .feature_plate {
        &.ic_o::before {
            content: "";
            background-image: url(assets/images/icons/ph31.svg);
            width: 20px;
            height: 20px;
            margin-right: 7px;
            margin-top: -6px;
        }

        &.ic_tr::before {
            content: "";
            background-image: url(assets/images/icons/ph33.svg);
            width: 20px;
            height: 20px;
            margin-right: 7px;
            margin-top: -6px;
        }

        &.ic_fv::before {
            content: "";
            background-image: url(assets/images/icons/ph34.svg);
            width: 20px;
            height: 20px;
            margin-right: 7px;
            margin-top: -6px;
        }

        &.ic_t::before {
            content: "";
            background-image: url(assets/images/icons/ph32.svg);
            width: 20px;
            height: 20px;
            margin-right: 7px;
            margin-top: -6px;
        }
    }
}

.ilbr {
    .feature_plate {
        &.ic_o::before {
            content: "";
            background-image: url(assets/images/icons/ph41.svg);
            width: 20px;
            height: 20px;
            margin-right: 7px;
            margin-top: -6px;
        }

        &.ic_tr::before {
            content: "";
            background-image: url(assets/images/icons/ph43.svg);
            width: 20px;
            height: 20px;
            margin-right: 7px;
            margin-top: -6px;
        }

        &.ic_fv::before {
            content: "";
            background-image: url(assets/images/icons/ph45.svg);
            width: 20px;
            height: 20px;
            margin-right: 7px;
            margin-top: -6px;
        }

        &.ic_t::before {
            content: "";
            background-image: url(assets/images/icons/ph42.svg);
            width: 20px;
            height: 20px;
            margin-right: 7px;
            margin-top: -6px;
        }

        &.ic_f::before {
            content: "";
            background-image: url(assets/images/icons/ph44.svg);
            width: 20px;
            height: 20px;
            margin-right: 7px;
            margin-top: -6px;
        }

        &.ic_sx::before {
            content: "";
            background-image: url(assets/images/icons/ph46.svg);
            width: 20px;
            height: 20px;
            margin-right: 7px;
            margin-top: -6px;
        }
    }
}

.narr_wrapper {
    max-width: 930px;
    margin: 65px auto 0 auto;
}

.lg_header {
    font-family: Prata;
    font-weight: 400;
    font-size: 36px;
    line-height: 48px;
    text-align: center;
    color: #532E00;
}

.accordion {
    width: 100%;
    margin-top: 20px;
    margin-bottom: 80px;
}

.accordion-item {
    border-bottom: 1px solid #EDC17F;
    padding-bottom: 30px;
}

.accordion-header {
    font-family: Prata;
    font-weight: 400;
    font-size: 24px;
    line-height: 32px;
    color: #99601A;
    display: flex;
    justify-content: space-between;
    cursor: pointer;
    padding-left: 20px;
    padding-top: 60px;

    .accordion-icon.mns {
        display: none;
    }
}

.accordion-icon {
    font-size: 50px;
    color: #351F04;
    transition: transform 0.3s ease;
}

.accordion-item.active {
    .accordion-content {
        max-height: 1008px;
        opacity: 1;
    }

    .accordion-icon {
        display: none;

        &.mns {
            display: block;
        }
    }
}

.accordion-content {
    font-family: Prata;
    font-weight: 400;
    font-size: 18px;
    line-height: 24px;
    transition: max-height 0.4s ease, padding 0.3s ease;
    color: #261604;
    max-height: 0;
    max-width: 94%;
    padding-left: 20px;
    padding: 25px 0 0 20px;
    overflow: hidden;
    opacity: 0;
}

.review-list {
    display: flex;
    margin-top: 80px;
}

.review {
    margin-right: 30px;
}

.review:last-child {
    margin-right: 0;
}

.review-header {
    display: flex;
    align-items: center;
    margin-bottom: 21px;
}

.review-header img {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 14px;
}

.review-info {
    display: flex;
    flex-direction: column;
}

.review p.review-info_h {
    font-family: Prata;
    font-weight: 400;
    font-size: 18px;
    line-height: 27px;
    color: #99601A;
}

.review-info span {
    font-family: Prata;
    font-weight: 400;
    font-size: 16px;
    line-height: 22px;
    color: #99601A;
}

.review p {
    font-family: Prata;
    font-weight: 400;
    font-size: 18px;
    line-height: 24px;
    color: #351F04;
}

.discount_active {
    background: #D5DDAF;
    color: #313520;
    opacity: 1;
}