import { BreadcrumbComponent } from "@/components/breadcrumb/breadcrumb.component"
import { environment } from "@/env/environment"
import { PageTitleService } from "@/services/page-title.service"
import { ProfileService } from "@/services/profile.service"
import { ToasterService } from "@/services/toaster.service"
import { CommonModule, NgOptimizedImage } from "@angular/common"
import { Component, ElementRef, inject, ViewChild } from '@angular/core'
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from "@angular/forms"
import { ActivatedRoute, Router } from "@angular/router"
import { TranslocoService, TranslocoModule } from '@jsverse/transloco'

@Component({
  selector: 'app-subscribtions',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    CommonModule,
    BreadcrumbComponent,
    NgOptimizedImage,
    FormsModule,
    TranslocoModule
  ],
  templateUrl: './subscribtions.component.html',
  styleUrl: './subscribtions.component.scss'
})
export class SubscriptionsComponent {
  profileService = inject(ProfileService)
  fb = inject(FormBuilder)
  router = inject(Router);
  route = inject(ActivatedRoute);
  toasterService = inject(ToasterService);
  translocoService = inject(TranslocoService);
  @ViewChild('confirmDialog') confirmDialog!: ElementRef<HTMLDialogElement>;
  message: string = "";
  subscriptions: any = {};
  iswitchClicked: boolean = false;
  currentCurency: string = 'RUB';
  isYearClicked: boolean = false;
  openedFaqIndex: number | null = null;
  selectedSubscriptions: Set<string> = new Set();
  readonly pageTitleService = inject(PageTitleService);

  subscriptionForm = this.fb.group({
    type: [null, Validators.required],
    payment: ['yookassa', Validators.required],
    autoRenew: [true],
    isYearly: [false]
  })

  ngOnInit() {
    this.profileService.getProfile().subscribe()
    this.init();
  }

  init() {
    this.profileService.getSubscriptions().subscribe(res => this.subscriptions = res)
    this.pageTitleService.setPageTitle('subscriptions.title');
  }

  togglePeriod() {
    this.isYearClicked = !this.isYearClicked;
    this.subscriptionForm.patchValue({ isYearly: this.isYearClicked });
  }

  toggleSubscription(subscriptionType: string) {
    if (this.selectedSubscriptions.has(subscriptionType)) {
      this.selectedSubscriptions.delete(subscriptionType);
    } else {
      this.selectedSubscriptions.add(subscriptionType);
    }
  }

  isSubscriptionSelected(subscriptionType: string) {
    return this.selectedSubscriptions.has(subscriptionType);
  }

  getPrice(subscriptionType: string) {
    const subscription = this.subscriptions[subscriptionType];
    if (!subscription) return 0;
    const period = this.isYearClicked ? 'yearly' : 'monthly';
    const currency = this.getCurrentCurrencyKey();
    return subscription.price[period][currency];
  }

  getCurrentCurrencyKey(): 'eur' | 'rub' {
    return this.currentCurency === 'EUR' ? 'eur' : 'rub';
  }

  getCurrencySymbol() {
    return this.currentCurency === 'EUR' ? '€' : '₽';
  }

  getTotalPrice() {
    let total = 0;
    this.selectedSubscriptions.forEach(type => {
      total += this.getPrice(type);
    });
    return Math.floor(total); 
  }

  removeFromCart(subscriptionType: string) {
    this.selectedSubscriptions.delete(subscriptionType);
  }

  toggleFaq(index: number) {
    this.openedFaqIndex = this.openedFaqIndex === index ? null : index;
  }

  isFaqOpen(index: number): boolean {
    return this.openedFaqIndex === index;
  }

  paySubscription() {
    const subscriptionTypes = Array.from(this.selectedSubscriptions);
    const paymentProvider = this.currentCurency === 'EUR' ? 'stripe' : 'yookassa';

    const formData = {
      ...this.subscriptionForm.value,
      types: subscriptionTypes,
      payment: paymentProvider
    };

    this.profileService.paySubscription(formData).subscribe({
      next: (res: any) => {
        location.href = res.paymentUrl;
      },
      error: () => {
        this.toasterService.showToast(this.translocoService.translate('toast.subscription_payment_error'), 'error', 'bottom-middle', 3000);
      }
    });
  }

  get subscriptionsFiltered() {
    return Object.fromEntries(Object.keys(this.subscriptions)
      .filter(key => !this.profileService.profile?.subscriptions.some((e: any) => e.type === key))
      .map(key => [key, this.subscriptions[key]]))
  }

  currentCurrency() {
    return this.currentCurency;
  }

  changeCurrency(currency: string) {
    this.currentCurency = currency;
    const paymentProvider = currency === 'EUR' ? 'stripe' : 'yookassa';
    this.subscriptionForm.patchValue({ payment: paymentProvider });
  }

  cancelAutoRenew(sub: any) {
    this.openConfirmationDialog('Отменить подписку?').then((confirmed) => {
      if (confirmed) {
        this.profileService.cancelAutoRenew(sub.id).subscribe({
          next: () => {
            this.toasterService.showToast(this.translocoService.translate('toast.subscription_cancelled'), 'success', 'bottom-middle', 3000);
            this.profileService.getProfile().subscribe(p => {
              this.profileService.profile = p;
            });
          },
          error: () => {
            this.toasterService.showToast(this.translocoService.translate('toast.subscription_cancel_error'), 'error', 'bottom-middle', 3000);
          }
        });
      }
    });
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  openConfirmationDialog(message: string): Promise<boolean> {
    this.message = message;
    this.confirmDialog.nativeElement.showModal();
    return new Promise((resolve) => {
      const okButton = this.confirmDialog.nativeElement.querySelector('.ok-button');
      const cancelButton = this.confirmDialog.nativeElement.querySelector('.cancel-button');
      const closeDialog = (confirmed: boolean) => {
        this.closeModal(this.confirmDialog.nativeElement);
        resolve(confirmed);
      };
      okButton?.addEventListener('click', () => closeDialog(true), { once: true });
      cancelButton?.addEventListener('click', () => closeDialog(false), { once: true });
    });
  }



  protected readonly environment = environment;
}
