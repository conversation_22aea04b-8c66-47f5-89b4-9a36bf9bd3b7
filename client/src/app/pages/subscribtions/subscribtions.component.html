<div class="middle_stripe">
  <!-- <breadcrumb></breadcrumb> -->

  <dialog class="stylized_wide" #confirmDialog>
    <div class="dialog-message">
      {{ message }}
    </div>
    <div class="mt-[60px] flex gap-[16px] text-center justify-center dialog-footer">
      <button type="submit" class="confirm-btn ok-button">
        <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
        <div class="confirm-btn-label">{{ 'common.yes' | transloco }}</div>
      </button>
      <button type="submit" class="confirm-btn cancel-button">
        <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
        <div class="confirm-btn-label">{{ 'common.cancel' | transloco }}</div>
      </button>
    </div>
  </dialog>

  @if(profileService.profile) {
  <div class="tab-container profile relative">
    <div class="wrapper_line custom_">
      <!-- <div class="dec_head _background">
        <img class="carn_b_" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
        <h1 class="dec_head-title_">{{ 'subscriptions.title' | transloco }}</h1>
        <img class="carn_b_ rotate" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
      </div> -->
      <div class="cat_wrap">
        <div class="flex flex-col relative justify-center">
          <p class="plane_h">{{ 'subscriptions.find_plan' | transloco }}</p>
          <div class="plane_s_w">
            <p class="plane_s">{{ 'subscriptions.combine_description' | transloco }}</p>
          </div>
        </div>
        <div class="th_wrapper">
          <div class="pres_wr" [ngClass]="{'discount_active': isYearClicked}">
            {{ 'subscriptions.gift_months' | transloco }}
          </div>
          <div class="swth_wrappr">
            <div (click)="togglePeriod()" [ngClass]="{'active': isYearClicked}" class="sw_item_w">{{ 'subscriptions.year' | transloco }}
            </div>
            <div (click)="togglePeriod()" [ngClass]="{'active': !isYearClicked}" class="sw_item_w">{{ 'subscriptions.month' | transloco }}
            </div>
          </div>
          <div class="curr_sw_wrap">
            <div (click)="iswitchClicked = !iswitchClicked" class="curr_s_btn">
              <p>
                {{currentCurency}}
              </p>
              <img src="assets/images/Arrow_sw.svg" alt="arrow">
            </div>
            <div class="currcy_switch">
              <button [ngClass]="{'selected_c': currentCurrency() == 'RUB'}" class="c-selection"
                (click)="changeCurrency('RUB')">₽ RUB</button>
              <button [ngClass]="{'selected_c': currentCurrency() == 'EUR'}" class="c-selection"
                (click)="changeCurrency('EUR')">€ EUR</button>
            </div>
          </div>
        </div>
        <div class="main_pl_wrap">
          <div class="main_pls_wrap">
            <div class="plane_h">{{ 'subscriptions.value_packages' | transloco }}</div>
            <div class="pacjs_wr">
              <div class="card_pcj">
                <div class="title_pcj">{{ 'subscriptions.audio_library' | transloco }}</div>
                <div>
                  <span class="old-price_pcj">{{getPrice('AUDIO') + getPrice('LIBRARY')}}&nbsp;{{getCurrencySymbol()}}</span>
                  <span class="price_pcj">{{getPrice('AUDIO_AND_LIBRARY')}}&nbsp;{{getCurrencySymbol()}}</span>
                  <span class="per-month_pcj">{{isYearClicked ? ('subscriptions.per_year' | transloco) : ('subscriptions.per_month' | transloco)}}</span>
                </div>
                <div class="discount_pcj">{{ 'subscriptions.savings' | transloco }} {{(getPrice('AUDIO') + getPrice('LIBRARY')) - getPrice('AUDIO_AND_LIBRARY')}}&nbsp;{{getCurrencySymbol()}} {{isYearClicked ? ('subscriptions.per_year' | transloco) : ('subscriptions.per_month_short' | transloco)}}</div>
                <div class="description_pcj">
                  {{ 'subscriptions.audio_library_desc' | transloco }}
                </div>
                <button class="btn_pcj" (click)="toggleSubscription('AUDIO_AND_LIBRARY')">
                  {{isSubscriptionSelected('AUDIO_AND_LIBRARY') ? ('subscriptions.remove_from_cart' | transloco) : ('subscriptions.select_package' | transloco)}}
                </button>
              </div>
              <div class="card_pcj">
                <div class="title_pcj">{{ 'subscriptions.full_access' | transloco }}</div>
                <div>
                  <span class="old-price_pcj">{{getPrice('AUDIO') + getPrice('LIBRARY') + getPrice('COURSES') + getPrice('AI')}}&nbsp;{{getCurrencySymbol()}}</span>
                  <span class="price_pcj">{{getPrice('FULL_ACCESS')}}&nbsp;{{getCurrencySymbol()}}</span>
                  <span class="per-month_pcj">{{isYearClicked ? ('subscriptions.per_year' | transloco) : ('subscriptions.per_month' | transloco)}}</span>
                </div>
                <div class="discount_pcj">{{ 'subscriptions.savings' | transloco }} {{(getPrice('AUDIO') + getPrice('LIBRARY') + getPrice('COURSES') + getPrice('AI')) - getPrice('FULL_ACCESS')}}&nbsp;{{getCurrencySymbol()}}{{isYearClicked ? ('subscriptions.per_year' | transloco) : ('subscriptions.per_month_short' | transloco)}}</div>
                <div class="description_pcj">
                  {{ 'subscriptions.full_access_desc' | transloco }}
                </div>
                <button class="btn_pcj" (click)="toggleSubscription('FULL_ACCESS')">
                  {{isSubscriptionSelected('FULL_ACCESS') ? ('subscriptions.remove_from_cart' | transloco) : ('subscriptions.select_package' | transloco)}}
                </button>
              </div>
            </div>
            <div class="plates_s_wrap">
              <div class="plane_h">{{ 'subscriptions.basic_subscriptions' | transloco }}</div>
              <div class="plates_wrapper">
                <div class="package-card_plate">
                  <div class="header_plate">
                    <div class="title_plate">{{ 'subscriptions.audio_subscription' | transloco }}</div>
                    <div class="flex items-center">
                      <div class="price_plate">{{getPrice('AUDIO')}}&nbsp;{{getCurrencySymbol()}} <span class="per-month_plate">{{isYearClicked ? ('subscriptions.per_year' | transloco) : ('subscriptions.per_month' | transloco)}}</span></div>
                      <input type="checkbox" class="checkbox_plate"
                             [checked]="isSubscriptionSelected('AUDIO')"
                             (change)="toggleSubscription('AUDIO')">
                    </div>
                  </div>
                  <div class="description_plate">
                    {{ 'subscriptions.audio_full_desc' | transloco }}
                  </div>
                  <div class="features_plate">
                    <div class="column_plate albr">
                      <div class="column-title_plate">{{ 'subscriptions.whats_included' | transloco }}</div>
                      <div class="feature_plate ic_o">{{ 'subscriptions.audio_lectures_count' | transloco }}</div>
                      <div class="feature_plate ic_tr">{{ 'subscriptions.weekly_lectures' | transloco }}</div>
                      <div class="feature_plate ic_fv">{{ 'subscriptions.exclusive_access' | transloco }}
                      </div>
                    </div>
                    <div class="column_plate albr">
                      <div class="column-title_plate">{{ 'subscriptions.features' | transloco }}</div>
                      <div class="feature_plate ic_t">{{ 'subscriptions.unlimited_download' | transloco }}</div>
                      <div class="feature_plate ic_f">{{ 'subscriptions.personal_playlists' | transloco }}</div>
                    </div>
                  </div>
                </div>
                <div class="package-card_plate">
                  <div class="header_plate">
                    <div class="title_plate">{{ 'subscriptions.library' | transloco }}</div>
                    <div class="flex items-center">
                      <div class="price_plate">{{getPrice('LIBRARY')}}&nbsp;{{getCurrencySymbol()}} <span class="per-month_plate">{{isYearClicked ? ('subscriptions.per_year' | transloco) : ('subscriptions.per_month' | transloco)}}</span></div>
                      <input type="checkbox" class="checkbox_plate"
                             [checked]="isSubscriptionSelected('LIBRARY')"
                             (change)="toggleSubscription('LIBRARY')">
                    </div>
                  </div>
                  <div class="description_plate">
                    {{ 'subscriptions.library_full_desc' | transloco }}
                  </div>
                  <div class="features_plate">
                    <div class="column_plate llbr">
                      <div class="column-title_plate">{{ 'subscriptions.whats_included' | transloco }}</div>
                      <div class="feature_plate ic_o">{{ 'subscriptions.books_count' | transloco }}</div>
                      <div class="feature_plate ic_tr">{{ 'subscriptions.new_translations' | transloco }}</div>
                      <div class="feature_plate ic_fv">{{ 'subscriptions.sacred_texts_comments' | transloco }}
                      </div>
                    </div>
                    <div class="column_plate llbr">
                      <div class="column-title_plate">{{ 'subscriptions.features' | transloco }}</div>
                      <div class="feature_plate ic_t">{{ 'subscriptions.epub_download' | transloco }}</div>
                      <div class="feature_plate ic_f">{{ 'subscriptions.save_quotes' | transloco }}</div>
                      <div class="feature_plate ic_sx">{{ 'subscriptions.reading_sync' | transloco }}</div>
                    </div>
                  </div>
                </div>
                <div class="package-card_plate">
                  <div class="header_plate">
                    <div class="title_plate">{{ 'subscriptions.courses_learning' | transloco }}</div>
                    <div class="flex items-center">
                      <div class="price_plate">{{getPrice('COURSES')}}&nbsp;{{getCurrencySymbol()}} <span class="per-month_plate">{{isYearClicked ? ('subscriptions.per_year' | transloco) : ('subscriptions.per_month' | transloco)}}</span></div>
                      <input type="checkbox" class="checkbox_plate"
                             [checked]="isSubscriptionSelected('COURSES')"
                             (change)="toggleSubscription('COURSES')">
                    </div>
                  </div>
                  <div class="description_plate">
                    {{ 'subscriptions.courses_full_desc' | transloco }}
                  </div>
                  <div class="features_plate">
                    <div class="column_plate klbr">
                      <div class="column-title_plate">{{ 'subscriptions.whats_included' | transloco }}</div>
                      <div class="feature_plate ic_o">{{ 'subscriptions.programs_count' | transloco }}</div>
                      <div class="feature_plate ic_tr">{{ 'subscriptions.monthly_programs' | transloco }}</div>
                      <div class="feature_plate ic_fv">{{ 'subscriptions.video_lectures' | transloco }}
                      </div>
                    </div>
                    <div class="column_plate klbr">
                      <div class="column-title_plate">{{ 'subscriptions.features' | transloco }}</div>
                      <div class="feature_plate ic_t">{{ 'subscriptions.download_materials' | transloco }}</div>
                    </div>
                  </div>
                </div>
                <div class="package-card_plate">
                  <div class="header_plate">
                    <div class="title_plate">{{ 'subscriptions.ai_chat_viveka' | transloco }}</div>
                    <div class="flex items-center">
                      <div class="price_plate">{{getPrice('AI')}}&nbsp;{{getCurrencySymbol()}} <span class="per-month_plate">{{isYearClicked ? ('subscriptions.per_year' | transloco) : ('subscriptions.per_month' | transloco)}}</span></div>
                      <input type="checkbox" class="checkbox_plate"
                             [checked]="isSubscriptionSelected('AI')"
                             (change)="toggleSubscription('AI')">
                    </div>
                  </div>
                  <div class="description_plate">
                    {{ 'subscriptions.ai_full_desc' | transloco }} </div>
                  <div class="features_plate">
                    <div class="column_plate ilbr">
                      <div class="column-title_plate">{{ 'subscriptions.whats_included' | transloco }}</div>
                      <div class="feature_plate ic_o">{{ 'subscriptions.unlimited_chats' | transloco }}</div>
                      <div class="feature_plate ic_tr">{{ 'subscriptions.full_knowledge_base' | transloco }}</div>
                      <div class="feature_plate ic_fv">{{ 'subscriptions.additional_ai_assistants' | transloco }}
                      </div>
                    </div>
                    <div class="column_plate ilbr">
                      <div class="column-title_plate">{{ 'subscriptions.features' | transloco }}</div>
                      <div class="feature_plate ic_t">{{ 'subscriptions.spiritual_advice' | transloco }}</div>
                      <div class="feature_plate ic_f">{{ 'subscriptions.explain_teaching' | transloco }}</div>
                      <div class="feature_plate ic_sx">{{ 'subscriptions.practice_guidance' | transloco }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="main_plfx_wrap">
            <div class="summary-card_pcs">
              <div class="summary-title_pcs">{{ 'subscriptions.selected_packages' | transloco }}</div>
              @if (selectedSubscriptions.size === 0) {
                <div class="item_pcs">
                  <span>{{ 'subscriptions.no_packages_selected' | transloco }}</span>
                </div>
              }
              @for (type of selectedSubscriptions; track type) {
                <div class="item_pcs">
                  <span>{{subscriptions[type]?.name}}</span>
                  <div class="item-price_pcs">
                    {{getPrice(type)}}&nbsp;{{getCurrencySymbol()}} <span class="remove_pcs" (click)="removeFromCart(type)">×</span>
                  </div>
                </div>
              }
              @if (selectedSubscriptions.size > 0) {
                <div class="divider_pcs"></div>
                <div class="total_pcs">
                  <span>{{ 'subscriptions.total' | transloco }}</span>
                  <span class="total-price_pcs">{{getTotalPrice()}}&nbsp;{{getCurrencySymbol()}} <span>{{isYearClicked ? ('subscriptions.per_year' | transloco) : ('subscriptions.per_month' | transloco)}}</span></span>
                </div>
              }
              <button class="btn_pcj bf_"
                      [disabled]="selectedSubscriptions.size === 0"
                      (click)="paySubscription()">
                {{ 'subscriptions.subscribe' | transloco }}
              </button>
              <div class="note_pcs">
                {{ 'subscriptions.cancel_anytime' | transloco }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="narr_wrapper">
        <div class="lg_header">
          {{ 'subscriptions.faq' | transloco }}
        </div>
        <div class="accordion">
          <div [ngClass]="{'active': isFaqOpen(0)}" class="accordion-item">
            <div class="accordion-header" (click)="toggleFaq(0)">
              {{ 'subscriptions.faq_trial_question' | transloco }}
              <span class="accordion-icon">+</span>
              <span class="accordion-icon mns">−</span>
            </div>
            <div class="accordion-content">
              {{ 'subscriptions.faq_trial_answer' | transloco }}
            </div>
          </div>
          <div [ngClass]="{'active': isFaqOpen(1)}" class="accordion-item">
            <div class="accordion-header" (click)="toggleFaq(1)">
              {{ 'subscriptions.faq_cancel_question' | transloco }}
              <span class="accordion-icon">+</span>
              <span class="accordion-icon mns">−</span>
            </div>
            <div class="accordion-content">
              {{ 'subscriptions.faq_trial_answer' | transloco }}
            </div>
          </div>
          <div [ngClass]="{'active': isFaqOpen(2)}" class="accordion-item">
            <div class="accordion-header" (click)="toggleFaq(2)">
              {{ 'subscriptions.faq_yearly_question' | transloco }}
              <span class="accordion-icon">+</span>
              <span class="accordion-icon mns">−</span>
            </div>
            <div class="accordion-content">
              {{ 'subscriptions.faq_trial_answer' | transloco }}
            </div>
          </div>
          <div [ngClass]="{'active': isFaqOpen(3)}" class="accordion-item">
            <div class="accordion-header" (click)="toggleFaq(3)">
              {{ 'subscriptions.faq_full_access_question' | transloco }}
              <span class="accordion-icon">+</span>
              <span class="accordion-icon mns">−</span>
            </div>
            <div class="accordion-content">
              {{ 'subscriptions.faq_trial_answer' | transloco }}
            </div>
          </div>
          <div [ngClass]="{'active': isFaqOpen(4)}" class="accordion-item">
            <div class="accordion-header" (click)="toggleFaq(4)">
              {{ 'subscriptions.faq_hidden_fees_question' | transloco }}
              <span class="accordion-icon">+</span>
              <span class="accordion-icon mns">−</span>
            </div>
            <div class="accordion-content">
              {{ 'subscriptions.faq_trial_answer' | transloco }}
            </div>
          </div>
          <div [ngClass]="{'active': isFaqOpen(5)}" class="accordion-item">
            <div class="accordion-header" (click)="toggleFaq(5)">
              {{ 'subscriptions.faq_change_plan_question' | transloco }}
              <span class="accordion-icon">+</span>
              <span class="accordion-icon mns">−</span>
            </div>
            <div class="accordion-content">
              {{ 'subscriptions.faq_trial_answer' | transloco }}
            </div>
          </div>
          <div [ngClass]="{'active': isFaqOpen(6)}" class="accordion-item">
            <div class="accordion-header" (click)="toggleFaq(6)">
              {{ 'subscriptions.faq_contact_question' | transloco }}
              <span class="accordion-icon">+</span>
              <span class="accordion-icon mns">−</span>
            </div>
            <div class="accordion-content">
              {{ 'subscriptions.faq_trial_answer' | transloco }}
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="lg_header">Отзывы</div>
      <div class="review-list">
        <div class="review">
          <div class="review-header">
            <img src="assets/images/avatar_.png" alt="Анна Кузнецова">
            <div class="review-info">
              <p class="review-info_h">Анна Кузнецова</p>
              <span>Графический дизайнер</span>
            </div>
          </div>
          <p>Я была приятно удивлена качеством материалов, которые предлагает это сообщество.
            Как дизайнер, я обращаю внимание на детали, и видеолекции с презентациями здесь сделаны очень
            профессионально и со вкусом.
            Возможность скачивать все курсы — огромный плюс, так как я могу заниматься в любое удобное для меня время,
            даже без интернета.
            Постоянные обновления помогают мне двигаться вперёд в духовном развитии. Очень рекомендую.</p>
        </div>
        <div class="review">
          <div class="review-header">
            <img src="assets/images/avatar_.png" alt="Елена Петрова">
            <div class="review-info">
              <p class="review-info_h">Елена Петрова</p>
              <span>IT-аналитик</span>
            </div>
          </div>
          <p>Я долго искала способ справиться с постоянным стрессом на работе, и случайно наткнулась на эту Общину.
            Сначала попробовала аудиоподписку и была поражена глубиной лекций и медитаций.
            Теперь я занимаюсь каждый день. Структурированные курсы помогли мне лучше понять себя, а практики принесли
            спокойствие.
            Это не просто сайт, а настоящий путеводитель на пути к гармонии.</p>
        </div>
        <div class="review">
          <div class="review-header">
            <img src="assets/images/avatar_.png" alt="Максим Ковалев">
            <div class="review-info">
              <p class="review-info_h">Максим Ковалев</p>
              <span>Исследователь, преподаватель</span>
            </div>
          </div>
          <p>Для моей работы важно иметь доступ к надёжным источникам информации.
            Библиотека священных текстов на этом сайте — настоящая находка.
            Книги, комментарии и переводы позволяют значительно углубить знания.
            Особенно впечатлил ИИ-чат 'Вивека' — он помогает находить нужные цитаты и объясняет сложные термины.
            Видно, что Община стремится нести знания, используя современные технологии.</p>
        </div>
      </div> -->
    </div>
  </div>
  }
</div>