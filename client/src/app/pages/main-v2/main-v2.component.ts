import { AccentCardComponent } from '@/components/cards/accent-card/accent-card.component'
import { CardWithDomeComponent } from '@/components/cards/card-with-dome/card-with-dome.component'
import { ChronologicalCardComponent } from '@/components/cards/chronological-card/chronological-card.component'
import { JoinCardComponent } from '@/components/cards/join-card/join-card.component'
import { CarouselV2Component } from '@/components/carousel-v2/carousel-v2.component'
import { ScaledCarouselComponent } from '@/components/scaled-carousel/scaled-carousel.component'
import { ConstructorService } from '@/services/constructor.service'
import { isPlatformBrowser } from '@angular/common'
import { Component, DestroyRef, ElementRef, HostListener, inject, PLATFORM_ID, signal, ViewChild } from '@angular/core'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { Meta, Title } from "@angular/platform-browser"
import { TranslocoPipe, TranslocoService } from "@jsverse/transloco"
import { environment } from '../../../environments/environment'
import { PageTitleService } from '@/services/page-title.service'

@Component({
  selector: 'app-main-v2',
  imports: [CarouselV2Component, ChronologicalCardComponent, AccentCardComponent, CardWithDomeComponent, JoinCardComponent,ScaledCarouselComponent, TranslocoPipe
  ],
  templateUrl: './main-v2.component.html',
  standalone: true,
  styleUrl: './main-v2.component.scss'
})
export class MainV2Component {
  itemsPerView = 4;

  private platformId = inject(PLATFORM_ID);
  private constructorService = inject(ConstructorService);
  private destroyRef = inject(DestroyRef);
  private translocoService = inject(TranslocoService);
  readonly pageTitleService = inject(PageTitleService);
  @ViewChild('projectsTabs') projectsTabs!: ElementRef;
  @ViewChild('bannerVideo') bannerVideo!: ElementRef<HTMLVideoElement>;


  isDarkMarker = false;

  carouselData: any[] = [];
  eventsCarousel: any = null;
  coursesCarousel: any = null;
  libraryCarousel: any = null;
  projectsCarousel: any = null;

  events: any[] = [];
  coursesItems: any[] = [];
  libraryItems: any[] = [];
  projectTabsData: any[] = [];

  displayCarousels: any[] = [];

  title = inject(Title)
  meta = inject(Meta)

  get courses() {
    return this.coursesItems;
  }

  get joinCards() {
    return [
      {
        name: this.translocoService.translate('main.join.acceptCreed.name'),
        description: this.translocoService.translate('main.join.acceptCreed.description'),
        date: '24 сентября',
        imageUrl: '/assets/images/main-v2/default-pictures/Принять_Символ_Веры.webp',
      },
      {
        name: this.translocoService.translate('main.join.takeDiksha.name'),
        description: this.translocoService.translate('main.join.takeDiksha.description'),
        date: '24 сентября',
        imageUrl: '/assets/images/main-v2/default-pictures/Принять_дикшу.webp',
      },
      {
        name: this.translocoService.translate('main.join.becomeMonk.name'),
        description: this.translocoService.translate('main.join.becomeMonk.description'),
        date: '24 сентября',
        imageUrl: '/assets/images/main-v2/default-pictures/Стать_монахом.webp',
      },
      {
        name: this.translocoService.translate('main.join.visitAshram.name'),
        description: this.translocoService.translate('main.join.visitAshram.description'),
        date: '24 сентября',
        imageUrl: '/assets/images/main-v2/default-pictures/Приехать_в_Ашрам.webp',
      },
      {
        name: this.translocoService.translate('main.join.orderRitual.name'),
        description: this.translocoService.translate('main.join.orderRitual.description'),
        date: '24 сентября',
        imageUrl: '/assets/images/main-v2/default-pictures/Заказать_ритуал.webp',
      },
      {
        name: this.translocoService.translate('main.join.createCenter.name'),
        description: this.translocoService.translate('main.join.createCenter.description'),
        date: '24 сентября',
        imageUrl: '/assets/images/main-v2/default-pictures/Центр.webp',
      }
    ];
  }

  selectedCourse = signal(this.courses[0] || null);

  private _selectedProjectTab: any = null;

  get projectTabs() {
    return this.projectTabsData;
  }

  get activeProjectTab() {
    if (this._selectedProjectTab) {
      return this._selectedProjectTab;
    }
    return this.projectTabsData[0] || null;
  }

  get eventsTitle() {
    return this.eventsCarousel?.title;
  }

  get coursesTitle() {
    return this.coursesCarousel?.title;
  }

  get libraryTitle() {
    return this.libraryCarousel?.title;
  }

  get projectsTitle() {
    return this.projectsCarousel?.title;
  }

  get sortedCarousels() {
    return this.carouselData.sort((a: any, b: any) => (a.id || 0) - (b.id || 0));
  }

  getCarouselBySize(size: string) {
    return this.sortedCarousels.find(carousel => carousel.size === size);
  }

  @HostListener('window:resize')
  onResize(): void {
    if (isPlatformBrowser(this.platformId)) {
      this.checkScreenSize();
    }
  }
  //   @HostListener('window:scroll', [])
  // private checkScroll(): void {
  //   if (isPlatformBrowser(this.platformId)) {
  //     this.isScrolled = window.scrollY > 20;
  //     const halfHeightScreen = window.innerHeight / 2;
  //     this.isDarkMarker = window.scrollY > halfHeightScreen && document.body.scrollHeight - window.scrollY > 1278;
  //   }
  // }

  ngOnInit() {
    this.pageTitleService.clearPageTitle();
    this.title.setTitle(this.translocoService.translate('main.meta.title'));
    this.meta.updateTag({ name: 'description', content: this.translocoService.translate('main.meta.description') || '' })
    this.checkScreenSize();
    this.loadCarouselData();

    this.translocoService.langChanges$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      this.loadCarouselData();
    });
  }

  private loadCarouselData() {
    this.constructorService.getCarousels().pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (data: any) => {
        this.carouselData = data;
        this.mapCarouselData();
      },
      error: (err) => {}
    });
  }

  private mapCarouselData() {
    this.eventsCarousel = this.carouselData.find(carousel => carousel.size === 'carousel1');
    if (this.eventsCarousel && this.eventsCarousel.items) {
      this.events = this.eventsCarousel.items
        .filter((item: any) => item.show)
        .map((item: any) => ({
          name: item.title,
          description: item.text,
          date: item.date,
          imageUrl: item.image ? `${environment.serverUrl}/upload/${item.image.name}` : '',
          link: item.link,
          tag: item.tag
        }));
    } else {
      this.events = [];
    }

    this.coursesCarousel = this.carouselData.find(carousel => carousel.size === 'carousel2');
    if (this.coursesCarousel && this.coursesCarousel.items) {
      this.coursesItems = this.coursesCarousel.items
        .filter((item: any) => item.show)
        .map((item: any) => ({
          name: item.title,
          description: item.text,
          date: item.date,
          imageUrl: item.image ? `${environment.serverUrl}/upload/${item.image.name}` : '',
          link: item.link,
          tag: item.tag,
          level: item.tag
        }));
    } else {
      this.coursesItems = [];
    }

    this.libraryCarousel = this.carouselData.find(carousel => carousel.size === 'carousel3');
    if (this.libraryCarousel && this.libraryCarousel.items) {
      this.libraryItems = this.libraryCarousel.items
        .filter((item: any) => item.show)
        .map((item: any) => ({
          name: item.title,
          description: item.text,
          date: item.date,
          imageUrl: item.image ? `${environment.serverUrl}/upload/${item.image.name}` : '',
          link: item.link,
          tag: item.tag
        }));
    } else {
      this.libraryItems = [];
    }

    this.projectsCarousel = this.carouselData.find(carousel => carousel.size === 'carousel4');
    if (this.projectsCarousel && this.projectsCarousel.items) {
      this.projectTabsData = this.projectsCarousel.items
        .filter((item: any) => item.show)
        .map((item: any) => ({
          title: item.title,
          description: item.text,
          date: item.date,
          imageUrl: item.image ? `${environment.serverUrl}/upload/${item.image.name}` : '',
          link: item.link,
          tag: item.tag
        }));
    } else {
      this.projectTabsData = [];
    }

    this.createDisplayCarousels();
  }

  private createDisplayCarousels() {
    const sortedCarousels = this.carouselData.sort((a: any, b: any) => (a.id || 0) - (b.id || 0));

    this.displayCarousels = sortedCarousels.map(carousel => {
      let items: any[] = [];
      let title = '';
      let sectionClass = '';
      let componentType = '';

      switch (carousel.size) {
        case 'carousel1':
          items = this.events;
          title = this.eventsTitle;
          sectionClass = 'chronological-section';
          componentType = 'chronological';
          break;
        case 'carousel2':
          items = this.coursesItems;
          title = this.coursesTitle;
          sectionClass = 'start-learning-section';
          componentType = 'courses';
          break;
        case 'carousel3':
          items = this.libraryItems;
          title = this.libraryTitle;
          sectionClass = 'chronological-section';
          componentType = 'chronological';
          break;
        case 'carousel4':
          items = this.projectTabsData;
          title = this.projectsTitle;
          sectionClass = 'projects-and-ministry-section';
          componentType = 'projects';
          break;
      }

      return {
        ...carousel,
        items,
        title,
        sectionClass,
        componentType,
        hasItems: items.length > 0,
        link: carousel.link
      };
    }).filter(carousel => carousel.hasItems);
  }

  ngAfterViewInit(): void {
    if (isPlatformBrowser(this.platformId)) {
      this.setupHorizontalScroll();
      this.playBannerVideo();
    }
  }

  private playBannerVideo(): void {
    if (this.bannerVideo && this.bannerVideo.nativeElement) {
      const video = this.bannerVideo.nativeElement;

      // Ensure video is muted for autoplay to work
      video.muted = true;

      // Try to play the video
      const playPromise = video.play();

      if (playPromise !== undefined) {
        playPromise.catch(error => {
          // Autoplay was prevented - this is expected behavior
          // The video will start playing when user interacts with the page
          if (error.name === 'NotAllowedError') {
            // Silently handle - this is normal browser behavior
            // Video will autoplay once user interacts with the page
          } else {
            console.warn('Video playback error:', error);
          }
        });
      }
    }
  }

  private setupHorizontalScroll(): void {
    if (this.projectsTabs) {
      const tabsElement = this.projectsTabs.nativeElement;
      tabsElement.addEventListener('wheel', (e: WheelEvent) => {
        // Проверяем, что есть горизонтальный скролл и прокрутка идет вертикально
        if (tabsElement.scrollWidth > tabsElement.clientWidth && e.deltaY !== 0) {
          e.preventDefault();
          e.stopPropagation();
          tabsElement.scrollLeft += e.deltaY;
        }
      }, { passive: false });
    }
  }

  checkScreenSize(): void {
    if (isPlatformBrowser(this.platformId)) {
      const width = window.innerWidth;
      if (width < 650) {
        this.itemsPerView = 2;
      } else if (width < 1100) {
        this.itemsPerView = 3;
      } else {
        this.itemsPerView = 4;
      }
    }
  }

  selectedCourseChange(course: any) {
    this.selectedCourse.set(course);
  }



  setActiveProjectTab(tab: any): void {
    this._selectedProjectTab = tab;
    this.scrollToActiveTab(tab);
  }

  private scrollToActiveTab(activeTab: any): void {
    if (!this.projectsTabs) return;

    const tabsContainer = this.projectsTabs.nativeElement;
    const tabElements = tabsContainer.querySelectorAll('.tab-item');

    const activeTabIndex = this.projectTabs.findIndex((tab: any) => tab.title === activeTab.title);

    if (activeTabIndex !== -1 && tabElements[activeTabIndex]) {
      const activeTabElement = tabElements[activeTabIndex] as HTMLElement;

      const tabLeft = activeTabElement.offsetLeft;
      const tabWidth = activeTabElement.offsetWidth;
      const containerWidth = tabsContainer.clientWidth;

      const targetScroll = tabLeft - (containerWidth / 2) + (tabWidth / 2);

      const maxScroll = tabsContainer.scrollWidth - containerWidth;
      const finalScroll = Math.max(0, Math.min(targetScroll, maxScroll));

      tabsContainer.scrollTo({
        left: finalScroll,
        behavior: 'smooth'
      });
    }
  }
}
