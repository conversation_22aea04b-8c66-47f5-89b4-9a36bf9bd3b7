

<div class="main-banner">
  <video #bannerVideo class="banner-video" autoplay muted loop playsinline>
    <source src="../../../assets/images/main-v2/Comp25.mp4" type="video/mp4">
  </video>
  <div>
    <h1>
      {{'main.banner.title' | transloco}}
    </h1>
    <h3>
      {{'main.banner.subtitle' | transloco}}
    </h3>
    <img class="logo" src="../../../assets/images/main-v2/om_big 2.webp" alt="logo">
  </div>
  <div class="call-to-action-btn">
    <div class="btn-label">
      {{'main.banner.cta' | transloco}}
    </div>
  </div>
</div>

<div class="main-contetnt-wrapper">

  @for (carousel of displayCarousels; track carousel.size) {
    @switch (carousel.componentType) {
      @case ('chronological') {
        <section [class]="carousel.sectionClass">
          <div class="carousel-header">
            <div class="carousel-title" [class.mx-auto]="carousel.size === 'carousel3'">
              {{carousel.title}}
              <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1.5 11L6.5 6L1.5 1" stroke="#532E00" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            @if (carousel.size !== 'carousel3') {
              <a class="cursor-pointer" [href]="carousel.link || '#'">{{ 'header.menu.show_more' | transloco }}</a>
            }
          </div>
          <app-carousel-v2 [items]="carousel.items" [itemsPerView]="itemsPerView">
            <ng-template let-event>
              @if (carousel.size === 'carousel1') {
                <app-chronological-card [value]="event"></app-chronological-card>
              } @else if (carousel.size === 'carousel3') {
                <app-card-with-domes [value]="event"></app-card-with-domes>
              }
            </ng-template>
          </app-carousel-v2>
        </section>
      }
      @case ('courses') {
        <section [class]="carousel.sectionClass">
          <div class="carousel-header">
            <div class="carousel-title">
              {{carousel.title}}
              <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1.5 11L6.5 6L1.5 1" stroke="#532E00" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <a class="cursor-pointer" [href]="carousel.link || '#'">{{'main.courses.viewMore' | transloco}}</a>
          </div>
          <app-scaled-carousel [items]="carousel.items" [itemsPerView]="5" (activeSlideChange)="selectedCourseChange($event)" >
            <ng-template let-course>
              <app-accent-card [value]="course" [isActive]="course.name === selectedCourse().name"></app-accent-card>
            </ng-template>
          </app-scaled-carousel>
        </section>
      }
      @case ('projects') {
        <section [class]="carousel.sectionClass">
          <div class="carousel-header flex-col gap-[34px]">
            <div class="carousel-title">
              {{carousel.title}}
              <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1.5 11L6.5 6L1.5 1" stroke="#532E00" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <a class="cursor-pointer" [href]="carousel.link || '#'">{{ 'header.menu.show_more' | transloco }}</a>
          </div>
          <div class="projects-tabs" #projectsTabs>
            <div class="projects-tabs-wrapper">
              @for (tab of carousel.items; track tab) {
                <div
                  class="tab-item cursor-pointer"
                  [class.active]="tab.title === activeProjectTab?.title"
                  (click)="setActiveProjectTab(tab)"
                >
                  {{tab.title}}
                </div>
              }
            </div>
          </div>
          @if (activeProjectTab) {
            <div class="selected-tab-content">
              <div class="tab-text-content">
                <div class="tab-date">{{activeProjectTab.date}}</div>
                <div class="tab-description">{{activeProjectTab.description}}</div>
                <a class="primaty-button" [href]="activeProjectTab?.link || '#'">{{ 'forum.more_details' | transloco }}</a>
              </div>
              <div class="tab-image">
                <div class="mask"></div>
                <img [src]="activeProjectTab.imageUrl" alt="">
              </div>
            </div>
          }
        </section>
      }
    }
  }
  <section class="join-section">
    <div class="carousel-header">
      <div class="carousel-title mx-auto">
        {{'main.join.title' | transloco}}
        <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1.5 11L6.5 6L1.5 1" stroke="#532E00" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>

      </div>
    </div>
    <div class="join-cards-wrapper">
      @for (card of joinCards; track card.name) {
        <app-join-card [value]="card"></app-join-card>
      }
    </div>
    <!-- <app-carousel-v2 [items]="courses" [itemsPerView]="4">
      <ng-template let-course>
        <div class="carousel-item">
          <div class="card">
            <img [src]="course.image" alt="" />
            <h4>{{ course.level }}</h4>
            <p>{{ course.date }}</p>
            <h3>{{ course.title }}</h3>
            <p>{{ course.description }}</p>
          </div>
        </div>
      </ng-template>
    </app-carousel-v2> -->
  </section>
</div>
