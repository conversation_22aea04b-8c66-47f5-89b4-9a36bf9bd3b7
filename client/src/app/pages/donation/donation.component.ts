import { BreadcrumbComponent } from "@/components/breadcrumb/breadcrumb.component"
import { PayFormComponent } from "@/components/pay-form/pay-form.component"
import { environment } from "@/env/environment"
import { DonationService } from "@/services/donation.service"
import { PageTitleService } from "@/services/page-title.service"
import { ToasterService } from "@/services/toaster.service"
import { CommonModule, isPlatformBrowser, NgOptimizedImage } from "@angular/common"
import { Component, inject, PLATFORM_ID } from '@angular/core'
import { ProfileService } from "@/services/profile.service"
import { FormBuilder, ReactiveFormsModule, Validators } from "@angular/forms"
import { ActivatedRoute, NavigationEnd, Router } from "@angular/router"
import { TranslocoService, TranslocoPipe } from "@jsverse/transloco"
import { filter } from "rxjs/operators"

@Component({
  selector: 'app-donation',
  standalone: true,
  imports: [
    BreadcrumbComponent,
    NgOptimizedImage,
    CommonModule,
    PayFormComponent,
    ReactiveFormsModule,
    TranslocoPipe
  ],
  templateUrl: './donation.component.html',
  styleUrl: './donation.component.scss'
})
export class DonationComponent {
  donationService = inject(DonationService);
  fb = inject(FormBuilder)
  router = inject(Router)
  route = inject(ActivatedRoute)
  toasterService = inject(ToasterService)
  translocoService = inject(TranslocoService)
  readonly pageTitleService = inject(PageTitleService);
  private platformId = inject(PLATFORM_ID);
  profileService = inject(ProfileService)
  monthly: boolean = true;
  tabs: { key: string; translationKey: string }[] = [
    { key: 'bank-transfer', translationKey: 'donation.bank_transfer' },
    { key: 'online', translationKey: 'donation.online' }
  ];
  selectedTabKey: string = 'bank-transfer';
  success = this.route.snapshot.queryParams['success'];
  environment = environment;

  bankTransferTabs = environment.hideStripe
    ? [
        {
          translationKey: 'donation.country.russia',
          value: 'russia',
        },
        {
          translationKey: 'donation.country.crypto',
          value: 'crypto',
        },
      ]
    : [
        {
          translationKey: 'donation.country.ukraine',
          value: 'ukraine',
        },
        {
          translationKey: 'donation.country.russia',
          value: 'russia',
        },
        {
          translationKey: 'donation.country.crypto',
          value: 'crypto',
        },
      ];
  selectedBankTab = this.bankTransferTabs[0];

  paymentForm = this.fb.group({
    sum: [null as number | null, [Validators.required, Validators.min(100)]],
    type: ['yookassa', Validators.required],
    autoRenew: [false],
    comment: [null, Validators.required],
  })

  ngOnInit() {
    this.pageTitleService.setPageTitle('donation.pageTitle');
    // Подписываемся на изменения роутера для определения активного таба
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        this.updateActiveTabFromUrl();
      });

    // Инициализируем активный таб при загрузке
    this.updateActiveTabFromUrl();

    // Обработка query параметра comment для автозаполнения
    this.route.queryParams.subscribe(params => {
      const commentParam = params['comment'];
      if (commentParam && this.selectedTabKey === 'online') {
        this.paymentForm.patchValue({ comment: commentParam });
      }
    });

    // Watch for payment type changes to adjust currency and validation
    this.paymentForm.get('type')?.valueChanges.subscribe(type => {
      const sumControl = this.paymentForm.get('sum');
      if (type === 'stripe') {
        // For Stripe (Europe) - minimum 10 EUR
        sumControl?.setValidators([Validators.required, Validators.min(10)]);
        if (sumControl?.value && sumControl.value < 10) {
          sumControl.setValue(10);
        }
      } else if (type === 'yookassa') {
        // For YooKassa (Russia) - minimum 100 RUB
        sumControl?.setValidators([Validators.required, Validators.min(100)]);
        if (sumControl?.value && sumControl.value < 100) {
          sumControl.setValue(100);
        }
      }
      sumControl?.updateValueAndValidity();
    });

    if(isPlatformBrowser(this.platformId) && this.success && localStorage.getItem('redirect')) {
      this.toasterService.showToast(this.translocoService.translate('donation.toast.book_paid'), 'success', 'bottom-middle', 5000);
      this.profileService.getProfile().subscribe();
      const url = localStorage.getItem('redirect')!.replace(environment.baseUrl, '')
      this.router.navigateByUrl(url);
      localStorage.removeItem('redirect');
    }
  }

  private updateActiveTabFromUrl(): void {
    const url = this.router.url;
    if (url.includes('/donation/bank-transfer')) {
      this.selectedTabKey = 'bank-transfer';
    } else if (url.includes('/donation/online')) {
      this.selectedTabKey = 'online';
      // Проверяем query параметр comment при переходе на таб "Онлайн"
      const commentParam = this.route.snapshot.queryParams['comment'];
      if (commentParam) {
        this.paymentForm.patchValue({ comment: commentParam });
      }
    }
  }

  toggleOption() {
    this.monthly = !this.monthly;
  }


  createPayment() {
    const formValue = this.paymentForm.value;
    const currency = formValue.type === 'stripe' ? 'EUR' : 'RUB';
    const minAmount = formValue.type === 'stripe' ? 10 : 100;

    // Check payment type first
    if (!formValue.type) {
      this.toasterService.showToast(this.translocoService.translate('donation.toast.select_payment_system'), 'warning', 'bottom-middle');
      return;
    }

    // Check minimum amount before general validation
    if (!formValue.sum || formValue.sum < minAmount) {
      const currencySymbol = currency === 'EUR' ? '€' : '₽';
      const minAmountText = this.translocoService.translate('donation.toast.min_amount');
      this.toasterService.showToast(`${minAmountText} ${minAmount} ${currencySymbol}`, 'warning', 'bottom-middle');
      return;
    }

    // Check general form validity
    if (!this.paymentForm.valid) {
      this.paymentForm.markAllAsTouched();
      this.toasterService.showToast(this.translocoService.translate('donation.toast.fill_required_fields'), 'warning', 'bottom-middle');
      return;
    }

    // All validations passed, create payment
    this.donationService.createPayment(formValue).subscribe({
      next: (res: any) => {
        this.toasterService.showToast(this.translocoService.translate('donation.toast.redirecting'), 'success', 'bottom-middle', 2000);
        setTimeout(() => {
          location.href = res.paymentUrl;
        }, 1000);
      },
      error: () => {
        this.toasterService.showToast(this.translocoService.translate('donation.toast.payment_error'), 'error', 'bottom-middle');
      }
    })
  }

  selectTab(tabKey: string): void {
    this.navigateToTab(tabKey);
  }

  private navigateToTab(tabKey: string): void {
    const currentQueryParams = this.route.snapshot.queryParams;
    const lang = this.translocoService.getActiveLang();
    this.router.navigate([`/${lang}/donation`, tabKey], { queryParams: currentQueryParams });
  }

  selectBankTab(tab: any): void {
    this.selectedBankTab = tab;
  }

  showNextBankTab() {
    const currentIndex = this.bankTransferTabs.findIndex(tab => tab.value === this.selectedBankTab.value);
    if (currentIndex < this.bankTransferTabs.length - 1) {
      this.selectedBankTab = this.bankTransferTabs[currentIndex + 1];
    }
  }

  showPrevBankTab() {
    const currentIndex = this.bankTransferTabs.findIndex(tab => tab.value === this.selectedBankTab.value);
    if (currentIndex > 0) {
      this.selectedBankTab = this.bankTransferTabs[currentIndex - 1];
    }
  }
}
