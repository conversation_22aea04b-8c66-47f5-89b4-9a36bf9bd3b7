<div class="middle_stripe">
  <!-- <breadcrumb></breadcrumb> -->
  <div class="wrapper_line">
    <!-- <div class="dec_head _background">
      <img class="carn_b_" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
      <h1 class="dec_head-title_">{{ 'donation.title' | transloco }}</h1>
      <img class="carn_b_ rotate" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
    </div> -->

    @if (!success) {
      <div class="profile-tabs relative tabs_w">
        @for (tab of tabs; track tab.key) {
          <div class="profile-tab" [ngClass]="{'is-active': selectedTabKey === tab.key }" (click)="selectTab(tab.key)">
            {{ tab.translationKey | transloco }}
          </div>
        }
      </div>
    }

    @if (success) {
      <div class="success-message">
        <div class="success-icon"></div>
        <div class="success-title">{{ 'donation.thank_you' | transloco }}</div>
        <div class="success-text">{{ 'donation.success_message' | transloco }}</div>
      </div>
    }

    @if (!success) {
      <div>
        @if (selectedTabKey === 'bank-transfer') {
          <div>
            <!-- Sub-tabs for bank transfer -->
            <div class="bank-sub-tabs">
              <div class="swth_wrappr">
                @for (tab of bankTransferTabs; track tab.value) {
                  <div (click)="selectBankTab(tab)" [ngClass]="{'active': selectedBankTab.value == tab.value}"
                    class="sw_item_w">{{ tab.translationKey | transloco }}
                  </div>
                }
              </div>
              <div class="mobile-favorite-tabs justify-between items-center">
                <div class="prev-button"
                  [ngClass]="{'cursor-pointer': bankTransferTabs[0].value !== selectedBankTab.value}">
                  <img src="assets/images/icons/arrow-in-circle.svg" alt="prev" (click)="showPrevBankTab()">
                </div>
                <div class="favorite-tab active">{{ selectedBankTab.translationKey | transloco }}</div>
                <div class="next-button"
                  [ngClass]="{'cursor-pointer': bankTransferTabs[bankTransferTabs.length-1].value !== selectedBankTab.value}">
                  <img src="assets/images/icons/arrow-in-circle.svg" alt="next" (click)="showNextBankTab()">
                </div>
              </div>
            </div>

            <!-- Bank transfer content based on selected sub-tab -->
            <div class="bank-transfer-section">
              <!-- Ukraine bank details -->
              @if (selectedBankTab.value === 'ukraine') {
                <div class="bank-details">
                  <div class="detail-row">
                    <span class="label">{{ 'donation.bank.recipient' | transloco }}</span>
                    <span class="value">{{ 'donation.bank.recipient_value_ukraine' | transloco }}</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">{{ 'donation.bank.iban' | transloco }}</span>
                    <span class="value">*****************************</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">{{ 'donation.bank.mfo' | transloco }}</span>
                    <span class="value">123456</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">{{ 'donation.bank.bank' | transloco }}</span>
                    <span class="value">ПриватБанк</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">{{ 'donation.bank.swift' | transloco }}</span>
                    <span class="value">PBANUA2X</span>
                  </div>
                  <div class="bank-note">
                    <span>{{ 'donation.important' | transloco }}</span> {{ 'donation.bank.note_ukraine_russia' | transloco }}
                  </div>
                </div>
              }

              <!-- Russia bank details -->
              @if (selectedBankTab.value === 'russia') {
                <div class="bank-details">
                  <div class="detail-row">
                    <span class="label">{{ 'donation.bank.recipient' | transloco }}</span>
                    <span class="value">{{ 'donation.bank.recipient_value_russia' | transloco }}</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">{{ 'donation.bank.inn' | transloco }}</span>
                    <span class="value">*********0</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">{{ 'donation.bank.kpp' | transloco }}</span>
                    <span class="value">*********</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">{{ 'donation.bank.account' | transloco }}</span>
                    <span class="value">40703810*********012</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">{{ 'donation.bank.bank' | transloco }}</span>
                    <span class="value">ПАО "Сбербанк"</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">{{ 'donation.bank.bik' | transloco }}</span>
                    <span class="value">*********</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">{{ 'donation.bank.corr_account' | transloco }}</span>
                    <span class="value">30101810400000000225</span>
                  </div>
                  <div class="bank-note">
                    <span>{{ 'donation.important' | transloco }}</span> {{ 'donation.bank.note_ukraine_russia' | transloco }}
                  </div>
                </div>
              }

              <!-- Crypto details -->
              @if (selectedBankTab.value === 'crypto') {
                <div class="bank-details">
                  <div class="detail-row">
                    <span class="label">{{ 'donation.crypto.bitcoin' | transloco }}</span>
                    <span class="value">**********************************</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">{{ 'donation.crypto.ethereum' | transloco }}</span>
                    <span class="value">******************************************</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">{{ 'donation.crypto.usdt' | transloco }}</span>
                    <span class="value">TQn9Y2khEsLJW1ChVWFMSMeRDow5oNDMnt</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">{{ 'donation.crypto.usdc' | transloco }}</span>
                    <span class="value">******************************************</span>
                  </div>
                  <div class="bank-note">
                    <span>{{ 'donation.important' | transloco }}</span> {{ 'donation.bank.note_crypto' | transloco }}
                  </div>
                </div>
              }
            </div>
          </div>
        }

        @if (selectedTabKey === 'online') {
          <div>
            <form class="payment-form" [formGroup]="paymentForm" (ngSubmit)="createPayment()">
              <div class="form-control">
                <div class="swth_wrappr _twc">
                  <div (click)="toggleOption()" [ngClass]="{'active': monthly}" class="sw_item_w">{{ 'donation.monthly' | transloco }}
                  </div>
                  <div (click)="toggleOption()" [ngClass]="{'active': !monthly}" class="sw_item_w">{{ 'donation.one_time' | transloco }}
                  </div>
                </div>
                <app-pay-form [paymentForm]="paymentForm"></app-pay-form>
              </div>
            </form>
          </div>
        }
      </div>
    }
  </div>
</div>