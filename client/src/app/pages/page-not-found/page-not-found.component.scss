.page-not-found {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  box-sizing: border-box;
  background: var(--main-background-color);
  z-index: 1;
}

.title {
  font-family: Prata, serif;
  font-weight: 400;
  font-size: 32px;
  line-height: 1.4;
  letter-spacing: 0.02em;
  text-align: center;
  color: var(--font-color1);
  margin: 0 auto;
  padding: 0 20px;
  max-width: 800px;
  width: 100%;
  opacity: 0;
  animation: manifestFromVoid 2s ease-out forwards;
}

@keyframes manifestFromVoid {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
    filter: blur(10px);
  }
  50% {
    opacity: 0.5;
    filter: blur(5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

@media (max-width: 768px) {
  .title {
    font-size: 26px;
  }
}

@media (max-width: 500px) {
  .title {
    font-size: 22px;
  }

  .page-not-found {
    padding: 30px 15px;
  }
}