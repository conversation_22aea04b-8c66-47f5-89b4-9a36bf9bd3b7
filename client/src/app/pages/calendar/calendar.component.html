<div>
  <dialog class="stylized_wide" #modal>
    <div (click)="closeModal(modal)" class="x_bt"></div>
    <div class="flex flex-col cont_mod">
      <p class="pr_20 text-center">{{ 'calendar.filters' | transloco }}</p>
      <p class="auth_head a_mg_modal">
        {{ 'calendar.select_type' | transloco }}
      </p>
      <div class="catg_wrap">
        <app-custom-dropdown [placeholderText]="'calendar.select_type' | transloco" [type]="'multiselect'" [options]="availableTypes" [title]="'name'" [selected]="selectedTypes"
          (selectedChange)="changeTypeFilter($event)">
        </app-custom-dropdown>
      </div>

      <p class="auth_head a_mg_modal">
        {{ 'calendar.select_format' | transloco }}
      </p>
      <div class="catg_wrap">
        <app-custom-dropdown [placeholderText]="'calendar.select_format' | transloco" [type]="'multiselect'" [options]="availableFormats" [title]="'name'" [selected]="selectedFormats"
          (selectedChange)="changeFormatFilter($event)">
        </app-custom-dropdown>
      </div>

      <p class="auth_head a_mg_modal">
        {{ 'calendar.select_location' | transloco }}
      </p>
      <div class="catg_wrap mbb-20">
        <app-custom-dropdown [placeholderText]="'calendar.select_location' | transloco" [type]="'multiselect'" [options]="availableLocations" [title]="'name'" [selected]="selectedLocations"
          (selectedChange)="changeLocationFilter($event)">
        </app-custom-dropdown>
      </div>
    </div>
  </dialog>
  <div class="middle_stripe">
    <!-- <breadcrumb></breadcrumb> -->
    <div class="wrapper_line">
      <!-- <div class="dec_head _background">
        <img class="carn_b_" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
        <h1 class="dec_head-title_">{{ 'calendar.title' | transloco }}</h1>
        <img class="carn_b_ rotate" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
      </div> -->
      <div class="cat_wrap">
        <form [formGroup]="filter">
          <div class="articles-search relative">
            <input formControlName="search" type="text" [placeholder]="'calendar.search' | transloco" (input)="searchSubject.next($event)">
            <div (click)="openModal()" class="p_filter">
              <span>{{ 'calendar.filter' | transloco }}</span>
            </div>

            <div class="articles-sort_ custom-dropdown" (click)="toggleSortDropdown()">
              @if (dropdownSortOpen) {
                <div class="dropdown-content">
                  @for(option of sortOptions; track option.id) {
                    <div
                      (click)="selectSort(option.value)"
                      class="dropdown-item cat_i"
                      [class.active]="currentSortField === option.value">
                      {{ option.label }}
                      <span class="sort-arrow" *ngIf="currentSortField === option.value">
                      <svg [ngClass]="{'rotate-down': sortDirection === 'Desc', 'rotate-up': sortDirection === 'Asc'}" width="30" height="30" viewBox="0 0 24 24"><path d="M7 10l5 5 5-5H7z"/></svg>
                    </span>
                    </div>
                  }
                </div>
              }
            </div>
          </div>
          <div class="flex articles-tag">
            <app-active-filters
              [filters]="activeFilters"
              (filterRemoved)="onFilterRemoved($event)">
            </app-active-filters>
          </div>
        </form>
      </div>
      <div class="mar_md">
        <div class="container">
          <div class="calendar-page">
            <div class="calendar-content">
              @if (groupedAdvertisements.length > 0) {
              @for (monthGroup of groupedAdvertisements; track monthGroup.monthYear; let isFirst = $first) {
              <!-- Разделительная линия между месяцами (кроме первого) -->
              @if (!isFirst) {
              <div class="month-separator">
                <div class="separator-line"></div>
              </div>
              }

              <!-- Заголовок месяца -->
              <div class="month-header">
                <h2 class="month-title">{{ monthGroup.monthYear }}</h2>
              </div>

              <!-- Список рекламы за месяц -->
              <div class="advertisements-list">
                <div class="ads-grid">
                  @for (ad of monthGroup.advertisements; track ad.id) {
                  <article class="ad-card" (click)="navigateToLink(ad.link)">
                    <div class="ad-image-container">
                      @if (ad.image) {
                      <img [ngSrc]="environment.serverUrl + '/upload/' + ad.image.name" [alt]="ad.title" width="300"
                        height="200" class="ad-image" (error)="onImageError($event)" priority="false">
                      } @else {
                      <div class="ad-image-placeholder">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M21 19V5C21 3.9 20.1 3 19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19ZM8.5 13.5L11 16.51L14.5 12L19 18H5L8.5 13.5Z"
                            fill="currentColor" />
                        </svg>
                      </div>
                      }
                    </div>

                    <div class="ad-content">
                      <h3 class="ad-title">{{ ad.title }}</h3>
                      <p class="ad-description">{{ ad.description }}</p>
                      <div class="ad-meta">
                        <time class="ad-date">{{ ad.date | date:'mediumDate' }}</time>
                        <span class="ad-link-indicator">{{ 'calendar.read_more' | transloco }}</span>
                      </div>
                    </div>
                  </article>
                  }
                </div>
              </div>
              }
              } @else {
              <div class="no-items">
                <p>{{ 'calendar.empty' | transloco }}</p>
              </div>
              }
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>