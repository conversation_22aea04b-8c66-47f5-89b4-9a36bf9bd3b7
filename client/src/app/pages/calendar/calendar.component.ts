import { ActiveFilter, ActiveFiltersComponent } from '@/components/active-filters/active-filters.component'
import { BreadcrumbComponent } from "@/components/breadcrumb/breadcrumb.component"
import { CustomDropdownComponent } from "@/components/custom-dropdown/custom-dropdown.component"
import { environment } from "@/env/environment"
import { AdvertisementDataService } from "@/services/advertisement-data.service"
import { CommonModule, isPlatformBrowser, NgOptimizedImage } from "@angular/common"
import { Component, ElementRef, inject, OnInit, OnDestroy, PLATFORM_ID, ViewChild, ViewEncapsulation } from "@angular/core"
import { FormBuilder, FormGroup, ReactiveFormsModule } from "@angular/forms"
import { debounceTime, distinctUntilChanged, Subject, takeUntil } from "rxjs"
import { TranslocoService, TranslocoPipe } from '@jsverse/transloco'
import { PageTitleService } from '@/services/page-title.service'


interface MonthGroup {
  monthYear: string;
  advertisements: any[];
}

@Component({
    selector: 'app-calendar',
    standalone: true,
    imports: [
      CommonModule,
      BreadcrumbComponent,
      NgOptimizedImage,
      ReactiveFormsModule,
      CustomDropdownComponent,
      ActiveFiltersComponent,
      TranslocoPipe
    ],
    encapsulation: ViewEncapsulation.None,
    templateUrl: './calendar.component.html',
    styleUrl: './calendar.component.scss'
  })
  export class CalendarComponent implements OnInit, OnDestroy {
    protected readonly environment = environment;
    advertisementDataService = inject(AdvertisementDataService);
    readonly pageTitleService = inject(PageTitleService);
    platformId = inject(PLATFORM_ID);
    fb = inject(FormBuilder);
    translocoService = inject(TranslocoService);

    private destroy$ = new Subject<void>();

    @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;

    searchSubject = new Subject<any>();
    allAdvertisements: any[] = [];
    groupedAdvertisements: MonthGroup[] = [];
    selectedTypes: any[] = [];
    selectedFormats: any[] = [];
    selectedLocations: any[] = [];
    activeFilters: ActiveFilter[] = [];
    availableTypes: any[] = [
        { id: 1, name: 'Ретрит' },
        { id: 2, name: 'Семинар' }
    ];

    availableFormats: any[] = [
        { id: 1, name: 'Онлайн-встреча' },
        { id: 2, name: 'Оффлайн-встреча' }
    ];

    availableLocations: any[] = [
        { id: 1, name: 'Россия' },
        { id: 2, name: 'Украина' },
        { id: 3, name: 'Индия' },
        { id: 4, name: 'Италия' }
    ];
    dropdownSortOpen = false;
    selectedSortLabel = 'Дате';
    sortOptions = [
        { label: 'Алфавиту', value: 'title', id: 0 },
        { label: 'Дате', value: 'date', id: 1 }
    ];
    sortDirection: 'Asc' | 'Desc' = 'Desc';
    currentSortField: string = 'date';

    filter: FormGroup = this.fb.group({
      search: '',
      sortOrder: 'dateDesc'
    });

    ngOnInit() {
        this.pageTitleService.setPageTitle('calendar.title');
        if (isPlatformBrowser(this.platformId)) {
            this.advertisementDataService.getActiveCalendarEvents()
                .pipe(takeUntil(this.destroy$))
                .subscribe({
                    next: (events: any[]) => {
                        const activeAds = events.filter((ad: any) => ad.active === true);
                        this.allAdvertisements = activeAds;
                        this.groupedAdvertisements = this.groupByMonth(activeAds);
                        this.applyFilters();
                    },
                    error: (error: any) => {
                        this.allAdvertisements = [];
                        this.groupedAdvertisements = [];
                    }
                });

            this.searchSubject.pipe(
                debounceTime(300),
                distinctUntilChanged(),
                takeUntil(this.destroy$)
            ).subscribe(() => {
                this.applyFilters();
            });
        }
    }

    ngOnDestroy() {
        this.destroy$.next();
        this.destroy$.complete();
    }



    applyFilters() {
        let filtered = [...this.allAdvertisements];

        const searchTerm = this.filter.get('search')?.value?.toLowerCase();
        if (searchTerm) {
            filtered = filtered.filter(ad =>
                ad.title?.toLowerCase().includes(searchTerm) ||
                ad.description?.toLowerCase().includes(searchTerm)
            );
        }

        if (this.selectedTypes.length > 0) {
            const selectedTypeNames = this.selectedTypes.map(t => t.name);
            filtered = filtered.filter(ad =>
                ad.type && selectedTypeNames.includes(ad.type)
            );
        }

        if (this.selectedFormats.length > 0) {
            const selectedFormatNames = this.selectedFormats.map(f => f.name);
            filtered = filtered.filter(ad =>
                ad.format && selectedFormatNames.includes(ad.format)
            );
        }

        if (this.selectedLocations.length > 0) {
            const selectedLocationNames = this.selectedLocations.map(l => l.name);
            filtered = filtered.filter(ad =>
                ad.location && selectedLocationNames.includes(ad.location)
            );
        }

        this.applySorting(filtered);
        this.updateActiveFilters();
    }

    applySorting(items: any[]) {
        const sortOrder = this.filter.get('sortOrder')?.value || 'dateDesc';
        const [field, direction] = this.parseSortOrder(sortOrder);

        items.sort((a, b) => {
            let valueA, valueB;

            if (field === 'title') {
                valueA = a.title?.toLowerCase() || '';
                valueB = b.title?.toLowerCase() || '';
            } else if (field === 'date') {
                valueA = new Date(a.date || 0).getTime();
                valueB = new Date(b.date || 0).getTime();
            } else {
                return 0;
            }

            if (direction === 'Asc') {
                return valueA > valueB ? 1 : valueA < valueB ? -1 : 0;
            } else {
                return valueA < valueB ? 1 : valueA > valueB ? -1 : 0;
            }
        });

        this.groupedAdvertisements = this.groupByMonth(items, field, direction);
    }

    parseSortOrder(sortOrder: string): [string, 'Asc' | 'Desc'] {
        const match = sortOrder.match(/^(.*?)(Asc|Desc)?$/);
        const field = match?.[1] || 'date';
        const direction = (match?.[2] as 'Asc' | 'Desc') || 'Desc';
        return [field, direction];
    }

    updateActiveFilters() {
        this.activeFilters = [];

        this.selectedTypes.forEach(type => {
            this.activeFilters.push({
                id: type.id,
                name: type.name,
                type: 'category',
                value: type
            });
        });

        this.selectedFormats.forEach(format => {
            this.activeFilters.push({
                id: format.id,
                name: format.name,
                type: 'format',
                value: format
            });
        });

        this.selectedLocations.forEach(location => {
            this.activeFilters.push({
                id: location.id,
                name: location.name,
                type: 'location',
                value: location
            });
        });
    }

    onFilterRemoved(filter: ActiveFilter) {
        if (filter.type === 'category') {
            this.selectedTypes = this.selectedTypes.filter(type => type.id !== filter.id);
            this.applyFilters();
        } else if (filter.type === 'format') {
            this.selectedFormats = this.selectedFormats.filter(format => format.id !== filter.id);
            this.applyFilters();
        } else if (filter.type === 'location') {
            this.selectedLocations = this.selectedLocations.filter(location => location.id !== filter.id);
            this.applyFilters();
        }
    }

    changeTypeFilter(types: any) {
        this.selectedTypes = types || [];
        this.applyFilters();
    }

    changeFormatFilter(formats: any) {
        this.selectedFormats = formats || [];
        this.applyFilters();
    }

    changeLocationFilter(locations: any) {
        this.selectedLocations = locations || [];
        this.applyFilters();
    }

    toggleSortDropdown() {
        this.dropdownSortOpen = !this.dropdownSortOpen;
    }

    updateSortLabels() {
        const sortValue = this.filter.get('sortOrder')?.value || '';
        const match = sortValue.match(/^(.*?)(Asc|Desc)?$/);
        this.currentSortField = match?.[1] || 'date';
        this.sortDirection = (match?.[2] as 'Asc' | 'Desc') || 'Asc';
        this.selectedSortLabel = this.sortOptions.find(option => option.value === this.currentSortField)?.label || 'Дате';
    }

    selectSort(field: string) {
        if (this.currentSortField === field) {
            this.sortDirection = this.sortDirection === 'Asc' ? 'Desc' : 'Asc';
        } else {
            this.currentSortField = field;
            this.sortDirection = 'Asc';
        }

        const sortOrder = this.currentSortField + this.sortDirection;
        this.filter.patchValue({ sortOrder });
        this.selectedSortLabel = this.sortOptions.find(option => option.value === field)?.label || '';
        this.dropdownSortOpen = false;
        this.applyFilters();
    }

    openModal() {
        this.modal.nativeElement.showModal();
    }

    closeModal(modal: HTMLDialogElement) {
        modal.close();
    }

  private groupByMonth(advertisements: any[], sortField: string = 'date', sortDirection: 'Asc' | 'Desc' = 'Asc'): MonthGroup[] {
    const groups: { [key: string]: any[] } = {};

    advertisements.forEach(ad => {
      if (ad.date) {
        const date = new Date(ad.date);
        const monthYear = date.toLocaleDateString('ru-RU', {
          year: 'numeric',
          month: 'long'
        });

        if (!groups[monthYear]) {
          groups[monthYear] = [];
        }
        groups[monthYear].push(ad);
      }
    });

    return Object.keys(groups)
      .sort((a, b) => {
        const dateA = new Date(groups[a][0].date);
        const dateB = new Date(groups[b][0].date);
        return dateA.getTime() - dateB.getTime();
      })
      .map(monthYear => ({
        monthYear,
        advertisements: groups[monthYear].sort((a, b) => {
          let valueA, valueB;

          if (sortField === 'title') {
            valueA = a.title?.toLowerCase() || '';
            valueB = b.title?.toLowerCase() || '';
          } else if (sortField === 'date') {
            valueA = new Date(a.date || 0).getTime();
            valueB = new Date(b.date || 0).getTime();
          } else {
            return 0;
          }

          if (sortDirection === 'Asc') {
            return valueA > valueB ? 1 : valueA < valueB ? -1 : 0;
          } else {
            return valueA < valueB ? 1 : valueA > valueB ? -1 : 0;
          }
        })
      }));
  }

  navigateToLink(link: string) {
    if (isPlatformBrowser(this.platformId)) {
      window.open(link, '_blank');
    }
  }

  onImageError(event: any) {
    // Hide only the image, show placeholder instead
    const imgElement = event.target;
    imgElement.style.display = 'none';
    
    // Find the placeholder and show it
    const container = imgElement.closest('.ad-image-container');
    if (container) {
      const placeholder = container.querySelector('.ad-image-placeholder');
      if (placeholder) {
        placeholder.style.display = 'flex';
      }
    }
  }
}
