<div class="flex flex-col items-center w-full">
  <form [formGroup]="form" class="profile-form flex flex-col relative">
    <div class="avatar flex justify-center items-center" (click)="triggerFileInput()">
      <img class="profile-avatar" *ngIf="form.value.avatar; else emptyAvatar"
        [src]="environment.serverUrl + '/upload/' + form.value.avatar!.name" alt="">
      <ng-template #emptyAvatar>
        <div class="empty-avatar"></div>
      </ng-template>
      <input #fileInput class="hidden" type="file" accept="image/*" (change)="uploadAvatar($event)">
    </div>
    <div class="flex flex-col gap-1">
      <label>E-mail</label>
      <div class="field-wrapper email-display">
        <span class="email-text">{{ form.value.email || ('profile.personal_data.not_specified' | transloco) }}</span>
      </div>
    </div>
    <div class="flex flex-col gap-1">
      <label>{{ 'profile.personal_data.first_name' | transloco }}</label>
      <div class="field-wrapper">
        <input type="text" formControlName="firstName">
        <!-- <span class="sufix">*</span> -->
      </div>
    </div>
    <div class="flex flex-col gap-1">
      <label>{{ 'profile.personal_data.last_name' | transloco }}</label>
      <div class="field-wrapper">
        <input type="text" formControlName="lastName">
        <!-- <span class="sufix">*</span> -->
      </div>
    </div>
    <div class="flex flex-col gap-1">
      <label>{{ 'profile.personal_data.middle_name' | transloco }}</label>
      <div class="field-wrapper">
        <input type="text" formControlName="middleName">
      </div>
    </div>
    <div class="flex flex-col gap-1">
      <label>{{ 'profile.personal_data.spiritual_name' | transloco }}</label>
      <div class="field-wrapper">
        <input type="text" formControlName="spiritualName">
      </div>
    </div>
    <div class="flex flex-col gap-1">
      <label>{{ 'profile.personal_data.phone' | transloco }}</label>
      <div class="field-wrapper">
        <input type="text" formControlName="phone">
      </div>
    </div>
    <div class="flex flex-col gap-1">
      <label>Telegram</label>
      <div class="field-wrapper">
        <input type="text" formControlName="telegram">
      </div>
    </div>
    <button type="submit" class="save-btn" (click)="onSubmit()">
      <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
      <div class="save-btn-label">{{ 'profile.personal_data.save' | transloco }}</div>
    </button>
  </form>
  <div *ngIf="authService.token()" class="button_img side">
    <a (click)="$event.preventDefault(); logout()" class="exit-button">
      {{ 'profile.personal_data.logout' | transloco }}
    </a>
  </div>
</div>
