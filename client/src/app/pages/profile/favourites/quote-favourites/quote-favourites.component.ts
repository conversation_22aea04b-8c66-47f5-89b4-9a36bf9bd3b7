import {Component, ElementRef, inject, ViewChild} from "@angular/core";
import {CommonModule, NgClass, NgOptimizedImage} from "@angular/common";
import {environment} from "@/env/environment";
import {ToasterService} from "@/services/toaster.service";
import {Router} from "@angular/router";
import {LibraryService} from "@/services/library.service";
import { TranslocoService } from "@jsverse/transloco";

@Component({
  selector: 'QuoteFavourites',
  standalone: true,
  imports: [CommonModule, NgOptimizedImage, NgClass],
  templateUrl: './quote-favourites.component.html',
  styleUrl: '../favourites.component.scss'
})
export class QuoteFavouritesComponent {
  @ViewChild('confirmDialog') confirmDialog!: ElementRef<HTMLDialogElement>;
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
  toasterService = inject(ToasterService);
  libraryService = inject(LibraryService);
  router = inject(Router);
  translocoService = inject(TranslocoService);
  message: string = "";
  selectedDropdElement: any = null;
  quoteActions = [
    'копировать',
    'удалить',
    'поделиться',
    'мне нравится',
  ];

  items: any = []
  page: number = 1;
  totalPages = 1;
  itemsPerPage: number = 5;

  ngOnInit() {
    this.get()
  }

  get() {
    this.libraryService.getQuoteFavourites().subscribe((res1:any) => {
      this.libraryService.getQuoteFavouritesContent().subscribe((res2: any) => {
        this.items = [...res1, ...res2];
        this.totalPages = Math.ceil(this.items.length / this.itemsPerPage);
        this.items = this.items.sort((a: any, b: any) => {
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        })
      })
    })
  }

  get quoteFavouritePagination() {
    return this.items.slice(0, this.page*this.itemsPerPage)
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  closeMobileActionSelect() {
    this.selectedDropdElement = null;
  }

  showMobileActionOptions(quote: any) {
    this.selectedDropdElement = quote;
  }

  onClickMobileAction() {
    this.closeMobileActionSelect();
  }

  removeQuote(item: any) {
    this.libraryService.deleteQuote(item.quote).subscribe(() => {
      const index = this.items.findIndex((e: any) => e.id === item.id);
      this.items.splice(index, 1);
    })
  }

  copyText(item: any) {
    navigator.clipboard.writeText(item.quote)
      .then(() => {
        this.toasterService.showToast(this.translocoService.translate('toast.text_copied'), 'success', 'bottom-middle', 3000);
      })
      .catch(err => {
        console.error('Не удалось скопировать текст: ', err);
        this.toasterService.showToast(this.translocoService.translate('toast.text_copy_failed'), 'error', 'bottom-middle', 3000);
      });
  }

  share(quote: any) {
    const type = quote.library ? 'library' : 'categories/1';
    const lang = this.translocoService.getActiveLang();
    const url = environment.baseUrl + `/${lang}/` + type + '/' + (quote?.library?.code || quote?.content?.slug) + '?quoteId=' + quote.id;
    navigator.clipboard.writeText(url).then(() => {
      this.toasterService.showToast(this.translocoService.translate('toast.link_copied'), 'success', 'bottom-middle', 3000);
    });
  }

  protected readonly environment = environment;
}
