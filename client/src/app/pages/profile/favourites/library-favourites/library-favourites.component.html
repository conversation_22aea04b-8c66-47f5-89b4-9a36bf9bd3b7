<div class="library-list flex-wrap">
  @for(item of items; track item.id) {
    <div class="library-item book-item">
      <library-item [item]="item" (deletedAction)="delete($event)"/>
    </div>
  }
</div>

<div class="buttn_catg" *ngIf="page < totalPages && items.length">
  <button class="load-more-button" (click)="page = page + 1; get()" [disabled]="false">
    <span>{{ 'search.load_more' | transloco }}</span>
  </button>
</div>

<dialog class="stylized_wide fixed" #confirmDialog>
  <div class="dialog-message">
    {{ message }}
  </div>
  <div class="mt-[60px] flex gap-[16px] text-center justify-center dialog-footer">
    <button type="submit" class="confirm-btn ok-button">
      <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
      <div class="confirm-btn-label">{{ 'common.yes' | transloco }}</div>
    </button>
    <button type="submit" class="confirm-btn cancel-button">
      <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
      <div class="confirm-btn-label">{{ 'common.cancel' | transloco }}</div>
    </button>
  </div>
</dialog>

<dialog #modal>
  <div>
    {{message}}
  </div>
  <div class="text-center mt-6">
    <button (click)="closeModal(modal)" class="cancel-button">Да</button>
  </div>
</dialog>
