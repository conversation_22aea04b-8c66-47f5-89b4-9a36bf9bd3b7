import {Component, ElementRef, inject, ViewChild} from "@angular/core";
import {CommonModule, NgOptimizedImage} from "@angular/common";
import {environment} from "@/env/environment";
import {ToasterService} from "@/services/toaster.service";
import {Router} from "@angular/router";
import {LibraryItemComponent} from "@/components/library-item/library-item.component";
import {LibraryService} from "@/services/library.service";
import {TranslocoModule, TranslocoService} from "@jsverse/transloco";

@Component({
  selector: 'LibraryFavourites',
  standalone: true,
  imports: [CommonModule, NgOptimizedImage, LibraryItemComponent, TranslocoModule],
  templateUrl: './library-favourites.component.html',
  styleUrl: '../favourites.component.scss'
})
export class LibraryFavouritesComponent {
  @ViewChild('confirmDialog') confirmDialog!: ElementRef<HTMLDialogElement>;
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
  toasterService = inject(ToasterService);
  libraryService = inject(LibraryService);
  private translocoService = inject(TranslocoService);
  router = inject(Router);
  message: string = "";

  items: any = []
  page: number = 1;
  totalPages = 1;

  ngOnInit() {
    this.get()
  }

  get() {
    this.libraryService
      .getFavourites(true, this.page)
      .subscribe((res: any) => {
        this.totalPages = res.pagination.totalPages;
        this.items = [...this.items, ...res.items]
      })
  }

  getUrl(item: any) {
    if(item && item.name) {
      const pathMatch = item.name.match(/^(photo\/[^\/]+)/);
      const lang = this.translocoService.getActiveLang();
      let url = '';

      if (pathMatch && pathMatch[1]) {
        const photoPath = pathMatch[1];
        url = this.environment.baseUrl + `/${lang}/` + photoPath;
      } else {
        url = this.environment.baseUrl + `/${lang}/photo/` + item.slug;
      }

      // Add photo parameter to the URL for direct photo access
      if (item.id) {
        url += '?photo=' + item.id;
      }

      return url;
    }
    return ''
  }

  copyUrl(item: any)  {
    if (item && item.name) {
      const url = this.getUrl(item);

      // Try modern clipboard API first
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(url)
          .then(() => {
            this.toasterService.showToast(this.translocoService.translate('toast.link_copied'), 'success', 'bottom-middle', 3000);
          })
          .catch(err => {
            console.error('Clipboard API failed, trying fallback: ', err);
            this.fallbackShare(url);
          });
      } else {
        // Fallback for browsers that don't support clipboard API
        this.fallbackShare(url);
      }
    }
  }

  private fallbackShare(url: string) {
    // Try Web Share API first (works well on mobile)
    if (navigator.share) {
      navigator.share({
        title: 'Фото',
        url: url
      }).then(() => {
        this.toasterService.showToast(this.translocoService.translate('toast.link_shared'), 'success', 'bottom-middle', 3000);
      }).catch(err => {
        console.error('Web Share API failed, trying legacy fallback: ', err);
        this.legacyFallbackCopy(url);
      });
    } else {
      // Legacy fallback using execCommand
      this.legacyFallbackCopy(url);
    }
  }

  private legacyFallbackCopy(url: string) {
    try {
      const textArea = document.createElement('textarea');
      textArea.value = url;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';

      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      const successful = document.execCommand('copy');
      document.body.removeChild(textArea);

      if (successful) {
        this.toasterService.showToast(this.translocoService.translate('toast.link_copied'), 'success', 'bottom-middle', 3000);
      } else {
        this.showManualCopyOption(url);
      }
    } catch (error) {
      console.error('Legacy copy failed:', error);
      this.showManualCopyOption(url);
    }
  }

  private showManualCopyOption(url: string) {
    // As a last resort, show the URL to the user for manual copying
    this.toasterService.showToast(this.translocoService.translate('toast.copy_link_manually', { url }), 'info', 'bottom-middle', 8000);
  }

  redirect(item: any) {
    const url = this.getUrl(item);
    this.router.navigateByUrl(url.replace(this.environment.baseUrl, ''));
  }

  openConfirmationDialog(message: string): Promise<boolean> {
    this.message = message;
    this.confirmDialog.nativeElement.showModal();
    return new Promise((resolve) => {
      const okButton = this.confirmDialog.nativeElement.querySelector('.ok-button');
      const cancelButton = this.confirmDialog.nativeElement.querySelector('.cancel-button');
      const closeDialog = (confirmed: boolean) => {
        this.closeModal(this.confirmDialog.nativeElement);
        resolve(confirmed);
      };
      okButton?.addEventListener('click', () => closeDialog(true), { once: true });
      cancelButton?.addEventListener('click', () => closeDialog(false), { once: true });
    });
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  openModal(message: string) {
    this.message = message;
    this.modal.nativeElement.showModal();
  }

  delete(item: any) {
    const index = this.items.findIndex((e: any) => e.id === item.id);
    if (index !== -1) {
      this.items.splice(index, 1);
    }
  }

  protected readonly environment = environment;
}
