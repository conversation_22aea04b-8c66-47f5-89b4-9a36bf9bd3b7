import { environment } from '@/env/environment'
import { ProfileService } from '@/services/profile.service'
import { ToasterService } from '@/services/toaster.service'
import { CommonModule, NgOptimizedImage } from '@angular/common'
import { Component, ElementRef, inject, ViewChild } from '@angular/core'
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms'
import { Router } from '@angular/router'
import { TranslocoPipe, TranslocoService } from '@jsverse/transloco'
import moment from 'moment/moment'

@Component({
  selector: 'app-my-subscriptions',
  standalone: true,
  imports: [CommonModule, NgOptimizedImage, ReactiveFormsModule, TranslocoPipe],
  templateUrl: './my-subscriptions.component.html',
  styleUrl: './my-subscriptions.component.scss'
})
export class MySubscriptionsComponent {
  profileService = inject(ProfileService);
  translocoService = inject(TranslocoService);
  fb = inject(FormBuilder);
  router = inject(Router);
  toasterService = inject(ToasterService);
  @ViewChild('confirmDialog') confirmDialog!: ElementRef<HTMLDialogElement>;
  message: string = "";
  subscriptions: any = {};

  subscriptionForm = this.fb.group({
    type: [null, Validators.required],
    payment: ['stripe', Validators.required],
    autoRenew: [true],
    isYearly: [false]
  });

  ngOnInit() {
    this.init();
  }

  get userSubscriptions() {
    return this.profileService.profile?.subscriptions || [];
  }

  get hasSubscriptions() {
    return this.userSubscriptions.length > 0;
  }

  init() {
    this.profileService.getSubscriptions().subscribe(res => this.subscriptions = res);
  }

  paySubscription() {
    this.profileService.paySubscription(this.subscriptionForm.value).subscribe((res: any) => {
      location.href = res.paymentUrl;
    });
  }

  activeUntil(item: any) {
    return moment(item.createdAt).add(item.isYearly ? 12 : 1, 'months').format('DD.MM.YYYY');
  }

  get subscriptionsFiltered() {
    return Object.fromEntries(Object.keys(this.subscriptions)
      .filter(key => !this.profileService.profile?.subscriptions.some((e: any) => e.type === key))
      .map(key => [key, this.subscriptions[key]]));
  }

  cancelAutoRenew(sub: any) {
    const confirmMessage = this.translocoService.translate('profile.subscriptions.stop_subscription') + '?';
    this.openConfirmationDialog(confirmMessage).then((confirmed) => {
      if (confirmed) {
        this.profileService.cancelAutoRenew(sub.id).subscribe({
          next: () => {
            const successMessage = this.translocoService.translate('profile.subscriptions.cancelled');
            this.toasterService.showToast(successMessage, 'success', 'bottom-middle', 3000);
            this.profileService.getProfile().subscribe(p => {
              this.profileService.profile = p;
            });
          },
          error: () => {
            this.toasterService.showToast(this.translocoService.translate('toast.subscription_cancel_error'), 'error', 'bottom-middle', 3000);
          }
        });
      }
    });
  }

  cancelAllSubscriptions() {
    this.openConfirmationDialog('Отменить все подписки?').then((confirmed) => {
      if (confirmed) {
        const subscriptions = this.profileService.profile?.subscriptions.filter((s: any) => s.isAutoRenew) || [];
        let completed = 0;
        let hasError = false;

        if (subscriptions.length === 0) {
          this.toasterService.showToast(this.translocoService.translate('toast.no_active_subscriptions'), 'info', 'bottom-middle', 3000);
          return;
        }

        subscriptions.forEach((sub: any) => {
          this.profileService.cancelAutoRenew(sub.id).subscribe({
            next: () => {
              completed++;
              if (completed === subscriptions.length) {
                if (!hasError) {
                  this.toasterService.showToast(this.translocoService.translate('toast.all_subscriptions_cancelled'), 'success', 'bottom-middle', 3000);
                }
                this.profileService.getProfile().subscribe(p => {
                  this.profileService.profile = p;
                });
              }
            },
            error: () => {
              hasError = true;
              completed++;
              if (completed === subscriptions.length) {
                this.toasterService.showToast(this.translocoService.translate('toast.some_subscriptions_cancel_error'), 'error', 'bottom-middle', 3000);
                this.profileService.getProfile().subscribe(p => {
                  this.profileService.profile = p;
                });
              }
            }
          });
        });
      }
    });
  }

  getSubscriptionPrice(sub: any) {
    const subscription = this.subscriptions[sub.type];
    if (!subscription?.price) return 0;
    const period = sub.isYearly ? 'yearly' : 'monthly';
    const currency = this.getSubscriptionCurrency(sub);
    return subscription.price[period]?.[currency] || 0;
  }

  getSubscriptionCurrency(sub: any): 'eur' | 'rub' {
    return sub.provider === 'stripe' ? 'eur' : 'rub';
  }

  getCurrencySymbol(sub: any) {
    return this.getSubscriptionCurrency(sub) === 'eur' ? '€' : '₽';
  }

  getTotalPrice(): { rub: number; eur: number } {
    if (!this.profileService.profile?.subscriptions) {
      return { rub: 0, eur: 0 };
    }
    const rubTotal = this.profileService.profile.subscriptions
      .filter((sub: any) => this.getSubscriptionCurrency(sub) === 'rub')
      .reduce((total: number, sub: any) => {
        const price = this.getSubscriptionPrice(sub);
        const monthlyPrice = sub.isYearly ? price / 12 : price;
        return total + monthlyPrice;
      }, 0);

    const eurTotal = this.profileService.profile.subscriptions
      .filter((sub: any) => this.getSubscriptionCurrency(sub) === 'eur')
      .reduce((total: number, sub: any) => {
        const price = this.getSubscriptionPrice(sub);
        const monthlyPrice = sub.isYearly ? price / 12 : price;
        return total + monthlyPrice;
      }, 0);

    return {
      rub: parseFloat(rubTotal.toFixed(2)),
      eur: parseFloat(eurTotal.toFixed(2))
    };
  }

  get hasMixedCurrencies() {
    if (!this.profileService.profile?.subscriptions) return false;
    const currencies = new Set(
      this.profileService.profile.subscriptions.map((sub: any) => this.getSubscriptionCurrency(sub))
    );
    return currencies.size > 1;
  }

  get hasAutoRenewSubscriptions() {
    return this.userSubscriptions.some((sub: any) => sub.isAutoRenew);
  }



  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  openConfirmationDialog(message: string): Promise<boolean> {
    this.message = message;
    this.confirmDialog.nativeElement.showModal();
    return new Promise((resolve) => {
      const okButton = this.confirmDialog.nativeElement.querySelector('.ok-button');
      const cancelButton = this.confirmDialog.nativeElement.querySelector('.cancel-button');
      const closeDialog = (confirmed: boolean) => {
        this.closeModal(this.confirmDialog.nativeElement);
        resolve(confirmed);
      };
      okButton?.addEventListener('click', () => closeDialog(true), { once: true });
      cancelButton?.addEventListener('click', () => closeDialog(false), { once: true });
    });
  }

  navigateToSubscriptions() {
    const currentLang = this.translocoService.getActiveLang();
    this.router.navigate([`/${currentLang}/subscriptions`]);
  }

  protected readonly environment = environment;
}
