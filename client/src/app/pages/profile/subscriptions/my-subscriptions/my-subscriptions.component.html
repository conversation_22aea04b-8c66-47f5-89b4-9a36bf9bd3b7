<div class="flex flex-col items-center w-full">
  <div class="w-full">
    <button (click)="navigateToSubscriptions()" class="btn_pcj_">{{ 'profile.subscriptions.add_subscription' | transloco }}</button>
    <div class="subscriptions-active" *ngIf='subscriptions'>
      <div class="card_pcj_wrap" *ngIf="hasSubscriptions">
        <div class="card_pcj_" *ngFor="let sub of userSubscriptions">
          <div>
            <div class="title_pcj_wp">
              <div class="title_pcj">{{subscriptions[$any(sub).type]?.name || ('profile.subscriptions.subscription' | transloco)}}</div>
            </div>
            <div>
              <span class="price_pcj">{{getSubscriptionPrice($any(sub))}}&nbsp;{{getCurrencySymbol($any(sub))}}</span>
              <span class="per-month_pcj">/{{$any(sub).isYearly ? ('profile.subscriptions.year' | transloco) : ('profile.subscriptions.month' | transloco)}}</span>
            </div>
          </div>
          <div class="dsci_wrapper">
            <div class="pres_wr discount_active">
              {{ 'profile.subscriptions.active' | transloco }}
            </div>
            <div class="flex items-center">
              <div class="nx_pt">{{ 'profile.subscriptions.next_payment' | transloco }}</div>
              <div class="nx_ptg">&nbsp;&nbsp;{{activeUntil($any(sub))}}</div>
            </div>
            <div class="nx_pti" *ngIf="$any(sub).isAutoRenew" (click)="cancelAutoRenew($any(sub))">{{ 'profile.subscriptions.stop_subscription' | transloco }}</div>
          </div>
        </div>
      </div>

      <div class="ttl_wrap" *ngIf="userSubscriptions.length > 0">
        <div class="flex flex-col items-end gap-2">
          <div class="flex items-end" *ngIf="getTotalPrice().rub > 0">
            <div class="title_pcj">{{ 'profile.subscriptions.total' | transloco }}&nbsp;&nbsp;</div>
            <div>
              <span class="price_pcj">{{getTotalPrice().rub}}&nbsp;₽</span>
              <span class="per-month_pcj">/{{ 'profile.subscriptions.month' | transloco }}</span>
            </div>
          </div>
          <div class="flex items-end" *ngIf="getTotalPrice().eur > 0">
            <div class="title_pcj" [class.hidden]="getTotalPrice().rub > 0">{{ 'profile.subscriptions.total' | transloco }}&nbsp;&nbsp;</div>
            <div>
              <span class="price_pcj">{{getTotalPrice().eur}}&nbsp;€</span>
              <span class="per-month_pcj">/{{ 'profile.subscriptions.month' | transloco }}</span>
            </div>
          </div>
        </div>
        <div class="stp_s" *ngIf="hasAutoRenewSubscriptions" (click)="cancelAllSubscriptions()">{{ 'profile.subscriptions.stop_all' | transloco }}</div>
      </div>

      <div class="no-subscriptions" *ngIf="!hasSubscriptions">
        <div class="no-subscriptions-text">{{ 'profile.subscriptions.no_active' | transloco }}</div>
      </div>
    </div>
  </div>
</div>

<dialog class="stylized_wide fixed" #confirmDialog>
  <div class="dialog-message">
    {{ message }}
  </div>
  <div class="mt-[60px] flex gap-[16px] text-center justify-center dialog-footer">
    <button type="submit" class="confirm-btn ok-button">
      <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
      <div class="confirm-btn-label">Да</div>
    </button>
    <button type="submit" class="confirm-btn cancel-button">
      <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
      <div class="confirm-btn-label">{{ 'profile.subscriptions.cancel' | transloco }}</div>
    </button>
  </div>
</dialog>
