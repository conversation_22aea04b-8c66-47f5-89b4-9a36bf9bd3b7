<div class="profile-subscriptions">
  @if (!environment.hideStripe) {
    <div class="tabs items-center">
      <div class="v-divider"></div>
      <ng-container *ngFor="let tab of tabsList">
        <div class="favorite-tab" [class.active]="selectedTab.value == tab.value" (click)="selectSubTab(tab)">{{tab.label}}</div>
        <div class="v-divider"></div>
      </ng-container>
    </div>
    <div class="mobile-favorite-tabs justify-between items-center">
      <div class="prev-button" [class.cursor-pointer]="tabsList[0].value !== selectedTab.value">
        <img src="../../../assets/images/icons/arrow-in-circle.svg" alt="prev" (click)="showPrev()">
      </div>
      <div class="favorite-tab active">{{selectedTab.label}}</div>
      <div class="next-button" [class.cursor-pointer]="tabsList[tabsList.length-1].value !== selectedTab.value">
        <img src="../../../assets/images/icons/arrow-in-circle.svg" alt="next" (click)="showNext()">
      </div>
    </div>
  }
  <div class="tab-content" *ngIf="selectedTab.value == 'my-subscriptions'">
    <app-my-subscriptions/>
  </div>
  <div class="tab-content" *ngIf="selectedTab.value == 'my-purchases'">
    <app-my-purchases/>
  </div>
</div>
