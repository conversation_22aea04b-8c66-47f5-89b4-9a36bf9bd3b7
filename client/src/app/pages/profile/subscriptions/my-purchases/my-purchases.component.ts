import { ProfileService } from '@/services/profile.service';
import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { Router } from '@angular/router';
import { TranslocoService, TranslocoPipe } from '@jsverse/transloco';

@Component({
  selector: 'app-my-purchases',
  standalone: true,
  imports: [CommonModule, TranslocoPipe],
  templateUrl: './my-purchases.component.html',
  styleUrl: './my-purchases.component.scss'
})
export class MyPurchasesComponent {
  profileService = inject(ProfileService);
  translocoService = inject(TranslocoService);
  router = inject(Router);

  ngOnInit() {
  }

  get allPurchases() {
    const libraryPurchases = this.profileService.profile?.libraryPurchases || [];
    const contentPurchases = this.profileService.profile?.contentPurchases || [];
    
    const mappedLibrary = libraryPurchases.map((item: any) => ({
      ...item,
      type: 'library',
      title: item.title,
      author: item.author
    }));
    
    const mappedContent = contentPurchases.map((item: any) => ({
      ...item,
      type: 'content',
      title: item.title,
      author: item.author
    }));
    
    return [...mappedLibrary, ...mappedContent];
  }

  redirect(item: any) {
    const currentLang = this.translocoService.getActiveLang();
    if (item.type === 'library') {
      this.router.navigateByUrl(`/${currentLang}/library/` + item.code);
    } else if (item.type === 'content') {
      this.router.navigateByUrl(`/${currentLang}/categories/` + item.category.id + '/' + item.slug);
    }
  }
}
