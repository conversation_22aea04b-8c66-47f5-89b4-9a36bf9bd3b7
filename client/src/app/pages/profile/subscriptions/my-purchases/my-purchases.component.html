<div class="library-list flex-col">
  @for(item of allPurchases; track item.id) {
      <div class="flex article-item justify-between gap-3 w-full items-start">
        <div class="flex max-w_md">
          <div class="flex flex-col gap-[10px] article-item-content-wrapper">
            <div class="article-item-content" (click)="redirect(item)">
              {{item?.title}}
            </div>
            <div *ngIf="item.author" class="article-item-author">
              {{item?.author}}
            </div>
          </div>
        </div>
      </div>
  }
  @if(allPurchases.length === 0) {
    <div class="no-purchases">
      <div class="no-purchases-text">{{ 'profile.purchases.no_purchases' | transloco }}</div>
    </div>
  }
</div>
