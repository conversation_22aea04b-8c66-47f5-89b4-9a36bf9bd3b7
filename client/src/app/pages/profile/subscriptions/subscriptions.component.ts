import { environment } from '@/env/environment'
import { CommonModule } from '@angular/common'
import { Component, inject, OnInit } from '@angular/core'
import { ActivatedRoute, Router } from "@angular/router"
import { MyPurchasesComponent } from "./my-purchases/my-purchases.component"
import { MySubscriptionsComponent } from "./my-subscriptions/my-subscriptions.component"
import { TranslocoService } from "@jsverse/transloco"

@Component({
  selector: 'app-subscriptions',
  standalone: true,
  imports: [
    CommonModule,
    MySubscriptionsComponent,
    MyPurchasesComponent
  ],
  templateUrl: './subscriptions.component.html',
  styleUrl: './subscriptions.component.scss'
})
export class SubscriptionsComponent implements OnInit {
  router = inject(Router);
  route = inject(ActivatedRoute);
  translocoService = inject(TranslocoService);
  environment = environment;

  get tabsList() {
    return environment.hideStripe
      ? [
          {
            label: this.translocoService.translate('profile.subscriptions.my_purchases'),
            value: 'my-purchases',
          },
        ]
      : [
          {
            label: this.translocoService.translate('profile.subscriptions.my_subscriptions'),
            value: 'my-subscriptions',
          },
          {
            label: this.translocoService.translate('profile.subscriptions.my_purchases'),
            value: 'my-purchases',
          },
        ];
  }

  selectedTab = this.tabsList[0];

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      const subtab = params['subtab'];
      if (subtab) {
        const tab = this.tabsList.find(t => t.value === subtab);
        if (tab) {
          this.selectedTab = tab;
        } else {
          this.navigateToSubTab(this.tabsList[0].value);
        }
      } else {
        this.navigateToSubTab(this.tabsList[0].value);
      }
    });
  }

  selectSubTab(tab: any): void {
    this.navigateToSubTab(tab.value);
  }

  private navigateToSubTab(subTabValue: string): void {
    const currentLang = this.translocoService.getActiveLang();
    this.router.navigate([`/${currentLang}/profile/subscriptions`, subTabValue]);
  }

  showNext() {
    const currentIndex = this.tabsList.findIndex(tab => tab.value === this.selectedTab.value);
    if (currentIndex < this.tabsList.length - 1) {
      this.navigateToSubTab(this.tabsList[currentIndex + 1].value);
    }
  }

  showPrev() {
    const currentIndex = this.tabsList.findIndex(tab => tab.value === this.selectedTab.value);
    if (currentIndex > 0) {
      this.navigateToSubTab(this.tabsList[currentIndex - 1].value);
    }
  }
}
