export enum PaymentStatus {
  PENDING = 'pending',
  SUCCESS = 'success',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export enum PaymentProvider {
  STRIPE = 'stripe',
  YOOKASSA = 'yookassa'
}

export interface PaymentHistory {
  id: number;
  status: PaymentStatus;
  date: Date;
  paymentMethod: string;
  details: string;
  amount: number;
  currency: string;
  provider: PaymentProvider;
  paymentId: string;
  createdAt: Date;
}

export interface PaymentHistoryResponse {
  data: PaymentHistory[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
