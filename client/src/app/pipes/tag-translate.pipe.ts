import { Pipe, PipeTransform, inject } from '@angular/core';
import { TranslocoService } from '@jsverse/transloco';

@Pipe({
  name: 'tagTranslate'
})
export class TagTranslatePipe implements PipeTransform {
  private translocoService = inject(TranslocoService);

  // Mapping of Russian tags to English/German translations
  private tagTranslations: { [lang: string]: { [tag: string]: string } } = {
    'en': {
      'Лекция': 'Lecture',
      'Семинар': 'Seminar',
      'Мастер-класс': 'Master Class',
      'Концерт': 'Concert',
      'Выставка': 'Exhibition',
      'Конференция': 'Conference',
      'Воркшоп': 'Workshop',
      'Тренинг': 'Training',
      'Презентация': 'Presentation',
      'Дискуссия': 'Discussion',
      'Круглый стол': 'Round Table',
      'Фестиваль': 'Festival',
      'Спектакль': 'Performance',
      'Кинопоказ': 'Screening',
      'Экскурсия': 'Excursion',
      'Медитация': 'Meditation',
      'Ретрит': 'Retreat',
      'Церемония': 'Ceremony',
      'Практика': 'Practice'
    },
    'de': {
      'Лекция': 'Vorlesung',
      'Семинар': 'Seminar',
      'Мастер-класс': 'Meisterklasse',
      'Концерт': 'Konzert',
      'Выставка': 'Ausstellung',
      'Конференция': 'Konferenz',
      'Воркшоп': 'Workshop',
      'Тренинг': 'Training',
      'Презентация': 'Präsentation',
      'Дискуссия': 'Diskussion',
      'Круглый стол': 'Runder Tisch',
      'Фестиваль': 'Festival',
      'Спектакль': 'Aufführung',
      'Кинопоказ': 'Filmvorführung',
      'Экскурсия': 'Exkursion',
      'Медитация': 'Meditation',
      'Ретрит': 'Retreat',
      'Церемония': 'Zeremonie',
      'Практика': 'Praxis'
    }
  };

  transform(tag: string): string {
    if (!tag) {
      return '';
    }

    const currentLang = this.translocoService.getActiveLang();

    // If current language is Russian, return original tag
    if (currentLang === 'ru') {
      return tag;
    }

    // Get translation for current language
    const langTranslations = this.tagTranslations[currentLang];
    if (langTranslations && langTranslations[tag]) {
      return langTranslations[tag];
    }

    // If no translation found, return original tag
    return tag;
  }
}
