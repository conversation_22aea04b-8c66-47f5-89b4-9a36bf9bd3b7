import { Injectable, OnDestroy, inject } from '@angular/core';
import { TranslocoService } from '@jsverse/transloco';
import { BehaviorSubject, Subject, takeUntil, switchMap, shareReplay, EMPTY, map, distinctUntilChanged } from 'rxjs';
import { AdvertisingService } from './advertising.service';

@Injectable({
  providedIn: 'root'
})
export class AdvertisementDataService implements OnDestroy {
  private translocoService = inject(TranslocoService);
  private advertisingService = inject(AdvertisingService);
  private destroy$ = new Subject<void>();

  // Single subject to hold all advertisement data
  private allDataSubject = new BehaviorSubject<any[]>([]);

  // Flag to prevent duplicate loading
  private isLoading = false;

  // Public observables for components to subscribe to (filtered from main data)
  public regularAds$ = this.allDataSubject.pipe(
    map(data => data.filter((item: any) => item.calendar === false || item.calendar === null)),
    shareReplay(1)
  );

  public calendarEvents$ = this.allDataSubject.pipe(
    map(data => data.filter((item: any) => item.calendar === true)),
    shareReplay(1)
  );

  constructor() {
    this.initializeDataLoading();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeDataLoading() {
    // Load data whenever language changes
    this.translocoService.langChanges$
      .pipe(
        distinctUntilChanged(), // Prevent duplicate calls for the same language
        takeUntil(this.destroy$),
        switchMap(() => {
          // Clear the advertising service cache first
          this.advertisingService.clearCache();

          // Reset loading state for new language
          this.isLoading = false;

          // Then load new data
          this.loadAllData();
          return EMPTY;
        })
      )
      .subscribe();
  }

  private loadAllData() {
    // Prevent duplicate loading if already in progress
    if (this.isLoading) {
      return;
    }

    this.isLoading = true;

    // Load all advertisements in a single request (no calendar parameter)
    this.advertisingService.getAll()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (data) => {
          // Store all data - filtering will be done by observables
          this.allDataSubject.next(data);
          this.isLoading = false;
        },
        error: (error) => {
          console.error('AdvertisementDataService: Error loading data:', error);
          this.allDataSubject.next([]);
          this.isLoading = false;
        }
      });
  }

  // Helper methods for components that need filtered data
  getActiveRegularAds() {
    return this.regularAds$.pipe(
      shareReplay(1)
    );
  }

  getActiveCalendarEvents() {
    return this.calendarEvents$.pipe(
      shareReplay(1)
    );
  }
}
