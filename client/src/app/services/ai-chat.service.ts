import { HttpClient } from '@angular/common/http'
import { Injectable, inject } from '@angular/core'
import { BehaviorSubject, of } from 'rxjs'
import { catchError, map } from 'rxjs/operators'
import { Chat, ChatMessage, ChatSource } from '../pages/ai-chat/ai-chat.component'

export interface AiResponse {
  aiResponse?: {
    message: string;
    metadata?: any;
    sources?: ChatSource[];
  };
  assistantMessage?: {
    chatId: string;
    content: string;
    id: string;
    role: string;
    timestamp: string;
    isTyping: boolean;
  };
  userMessage?: {
    chatId: string;
    content: string;
    id: string;
    role: string;
    timestamp: string;
    isTyping: boolean;
  };
  content?: string;
  sources?: ChatSource[];
}

@Injectable({
  providedIn: 'root'
})
export class AiChatService {
  private http = inject(HttpClient);
  private chatsSubject = new BehaviorSubject<Chat[] | null>(null);

  constructor() {
    this.loadChats();
  }

  getChats() {
    return this.chatsSubject.asObservable();
  }

  loadChats(): void {
    this.http.get<Chat[]>('/client/ai-chat').subscribe((chats) => {
      this.chatsSubject.next(chats);
    });
  }

  addChatToList(chat: Chat) {
    const currentChats = this.chatsSubject.value || [];
    this.chatsSubject.next([chat, ...currentChats]);
  }

  removeChatFromList(chatId: string) {
    const currentChats = this.chatsSubject.value || [];
    this.chatsSubject.next(currentChats.filter(c => c.id !== chatId));
  }

  updateChatInList(updatedChat: Chat) {
    const currentChats = this.chatsSubject.value || [];
    this.chatsSubject.next(
      currentChats.map(c => c.id === updatedChat.id ? updatedChat : c)
    );
  }

  createNewChat() {
    return this.http.post<Chat>('/client/ai-chat', {});
  }

  updateChat(chatId: string, updates: Partial<Chat>) {
    return this.http.patch<Chat>(`/client/ai-chat/${chatId}`, updates);
  }

  deleteChat(chatId: string) {
    return this.http.delete<void>(`/client/ai-chat/${chatId}`);
  }

  sendMessage(chatId: string, message: string) {
    return this.http.post<any>(`/client/ai-chat/${chatId}/messages`, { content: message });
  }

  getChatMessages(chatId: string) {
    return this.http.get<ChatMessage[]>(`/client/ai-chat/${chatId}/messages`);
  }

  fetchUrlContent(url: string) {
    return this.http.post<any>('/client/ai-chat/fetch-url', { url }).pipe(
      map(response => ({
        title: response.data.title || this.extractDomainFromUrl(url),
        content: response.data.content || 'Контент не удалось загрузить',
        url
      })),
      catchError(error => {
        return of({
          title: this.extractDomainFromUrl(url),
          content: `Не удалось загрузить контент по ссылке.\n\nОшибка: ${error.message || 'Неизвестная ошибка'}\n\nURL: ${url}`,
          url: url,
          error: error.message
        });
      })
    );
  }

  private extractDomainFromUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch {
      return 'Внешняя ссылка';
    }
  }

}
