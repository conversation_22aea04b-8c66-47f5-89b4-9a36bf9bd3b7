import { HttpClient } from '@angular/common/http'
import { inject, Injectable } from '@angular/core'

export enum ActivityType {
    REGISTRATION = 'registration',
    CONTENT_VIEW = 'content_view',
    LIBRARY_VIEW = 'library_view'
}

@Injectable({
    providedIn: 'root'
})
export class AnalyticsService {
    http = inject(HttpClient)

    logActivity(type: ActivityType, metadata?: any) {
        return this.http.post(`/client/analytics/log`, {
            type,
            metadata
        }).subscribe()
    }

    logContentView(contentId: number, contentSlug: string) {
        this.logActivity(ActivityType.CONTENT_VIEW, { contentId, contentSlug })
    }

    logLibraryView(libraryId: number, librarySlug: string) {
        this.logActivity(ActivityType.LIBRARY_VIEW, { libraryId, librarySlug })
    }
}

