import { Injectable, signal } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class PageTitleService {
  private readonly _pageTitle = signal<string>('');

  readonly pageTitle = this._pageTitle.asReadonly();

  setPageTitle(title: string): void {
    this._pageTitle.set(title);
  }

  clearPageTitle(): void {
    this._pageTitle.set('');
  } 

  getPageTitle(): string {
    return this._pageTitle();
  }
}

