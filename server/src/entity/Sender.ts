import {BaseEntity, Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";

export enum SenderStatus {
    DRAFT = 'draft',
    SCHEDULED = 'scheduled',
    SENT = 'sent',
    FAILED = 'failed'
}

@Entity('senders')
export class Sender extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({type: 'varchar', length: 255})
    title: string;

    @Column({type: 'text'})
    subject: string;

    @Column({type: 'text'})
    htmlContent: string;

    @Column({type: 'simple-array', nullable: true})
    emails: string[];

    @Column({type: 'simple-array', nullable: true})
    userIds: number[];

    @Column({type: 'simple-array', nullable: true})
    userGroups: string[];

    @Column({
        type: 'enum',
        enum: SenderStatus,
        default: SenderStatus.DRAFT
    })
    status: SenderStatus;

    @Column({type: 'timestamp', nullable: true})
    sentAt: Date;

    @Column({type: 'int', default: 0})
    recipientsCount: number;

    @Column({type: 'text', nullable: true})
    errorMessage: string;

    @CreateDateColumn({type: "timestamp"})
    createdAt: Date;

    @UpdateDateColumn({type: "timestamp"})
    updatedAt: Date;
}

