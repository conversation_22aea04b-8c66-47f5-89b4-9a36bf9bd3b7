import { Base<PERSON>ntity, <PERSON>umn, CreateDateColumn, Entity, <PERSON><PERSON>C<PERSON><PERSON>n, ManyToOne, PrimaryGeneratedColumn } from "typeorm"
import { User } from "./User"

export enum ActivityType {
    REGISTRATION = 'registration',
    CONTENT_VIEW = 'content_view',
    LIBRARY_VIEW = 'library_view',
}

@Entity('activity_logs')
export class ActivityLog extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number

    @Column({
        type: 'enum',
        enum: ActivityType
    })
    type: ActivityType

    @ManyToOne(() => User, { nullable: true, onDelete: 'SET NULL' })
    @JoinColumn()
    user: User | null

    @Column({ nullable: true })
    userId: number | null

    @Column({ nullable: true })
    ipAddress: string

    @Column({ nullable: true })
    userAgent: string

    @Column({ type: 'jsonb', nullable: true })
    metadata: any

    @CreateDateColumn()
    createdAt: Date
}

