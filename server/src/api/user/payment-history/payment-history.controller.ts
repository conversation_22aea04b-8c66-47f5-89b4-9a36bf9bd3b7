import { JwtAuthGuard } from '@/api/user/guards/auth.guard';
import { Controller, Get, Query, Req, UseGuards } from '@nestjs/common';
import { PaymentHistoryService } from './payment-history.service';

@Controller('user/payments/history')
export class PaymentHistoryController {
  constructor(private readonly paymentHistoryService: PaymentHistoryService) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  async getPaymentHistory(
    @Req() req: any,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10'
  ) {
    const userId = req.user?.id;
    const pageNum = parseInt(page, 10) || 1;
    const limitNum = parseInt(limit, 10) || 10;

    return await this.paymentHistoryService.getUserPaymentHistory(
      userId,
      pageNum,
      limitNum
    );
  }
}
