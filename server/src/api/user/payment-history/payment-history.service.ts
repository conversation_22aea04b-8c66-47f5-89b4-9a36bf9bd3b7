import { Injectable } from '@nestjs/common';
import { PaymentHistory, PaymentStatus, PaymentProvider } from '@/entity/PaymentHistory';
import { User } from '@/entity/User';

export interface CreatePaymentHistoryDto {
  userId: number;
  status: PaymentStatus;
  date: Date;
  paymentMethod?: string;
  details: string;
  amount: number;
  currency: string;
  provider?: PaymentProvider;
  paymentId?: string;
}

export interface PaymentHistoryResponse {
  id: number;
  status: PaymentStatus;
  date: Date;
  paymentMethod: string;
  details: string;
  amount: number;
  currency: string;
  provider: PaymentProvider;
  paymentId: string;
  createdAt: Date;
}

export interface PaymentHistoryListResponse {
  data: PaymentHistoryResponse[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

@Injectable()
export class PaymentHistoryService {
  async createPaymentHistory(data: CreatePaymentHistoryDto): Promise<PaymentHistory> {
    const paymentHistory = new PaymentHistory();
    paymentHistory.userId = data.userId;
    paymentHistory.status = data.status;
    paymentHistory.date = data.date;
    paymentHistory.paymentMethod = data.paymentMethod;
    paymentHistory.details = data.details;
    paymentHistory.amount = data.amount;
    paymentHistory.currency = data.currency;
    paymentHistory.provider = data.provider;
    paymentHistory.paymentId = data.paymentId;

    return await paymentHistory.save();
  }

  async getUserPaymentHistory(
    userId: number,
    page: number = 1,
    limit: number = 10
  ): Promise<PaymentHistoryListResponse> {
    const offset = (page - 1) * limit;

    const [data, total] = await PaymentHistory.findAndCount({
      where: { userId },
      order: { date: 'DESC' },
      skip: offset,
      take: limit,
    });

    const totalPages = Math.ceil(total / limit);

    return {
      data: data.map(item => ({
        id: item.id,
        status: item.status,
        date: item.date,
        paymentMethod: item.paymentMethod || '',
        details: item.details,
        amount: item.amount,
        currency: item.currency,
        provider: item.provider,
        paymentId: item.paymentId || '',
        createdAt: item.createdAt,
      })),
      total,
      page,
      limit,
      totalPages,
    };
  }

  async updatePaymentStatus(paymentId: string, status: PaymentStatus): Promise<void> {
    await PaymentHistory.update(
      { paymentId },
      { status }
    );
  }
}
