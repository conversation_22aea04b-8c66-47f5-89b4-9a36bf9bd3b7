import { AudiofileSingersService } from '@/api/firebase/audiofile-singers/audiofile-singers.service'
import { AudiofileTagsService } from '@/api/firebase/audiofile-tags/audiofile-tags.service'
import { AudiofileTypesService } from '@/api/firebase/audiofile-types/audiofile-types.service'
import { SqlSanitizer } from '@/common/utils/sql-sanitizer.util'
import { AudioFile } from '@/entity/AudioFile'
import { AudioFileLike } from '@/entity/AudioFileLike'
import { AudioFileSinger } from '@/entity/AudioFileSinger'
import { AudioFileTag } from '@/entity/AudioFileTag'
import { AudioFileType } from '@/entity/AudioFileType'
import { Injectable, NotFoundException } from '@nestjs/common'
import { copyFileSync, existsSync, rmSync } from 'fs'
import { basename } from 'path'

@Injectable()
export class AudiofilesService {
  constructor(
    private readonly audiofileTypesService: AudiofileTypesService,
    private readonly audiofileTagsService: AudiofileTagsService,
    private readonly audiofileSingersService: AudiofileSingersService,
  ) {}

  async findAll(query: any = {}, user) {
    const itemsPerPage = 10;
    let {
      page = 1,
      title,
      types,
      singers,
      tags,
      sortOrder = 'dateAsc',
    } = query;

    const queryBuilder = AudioFile.createQueryBuilder('audioFile');

    queryBuilder
      .leftJoinAndSelect('audioFile.type', 'type')
      .leftJoinAndSelect('audioFile.singer', 'singer')
      .leftJoinAndSelect('audioFile.tags', 'tag')
      .leftJoinAndSelect('audioFile.likes', 'likes')
      .leftJoinAndSelect('likes.user', 'likesUser');

    if (title) {
      const sanitizedTitle = SqlSanitizer.sanitizeLikePattern(title);
      queryBuilder.andWhere('audioFile.title ILIKE :title', { title: `%${sanitizedTitle}%` });
    }



    if (types) {
      const typeKeys = typeof types === 'string' ? [types] : types;
      queryBuilder.andWhere('type.id IN (:...typeKeys)', { typeKeys });
    }

    if (singers) {
      const singerKeys = typeof singers === 'string' ? [singers] : singers;
      queryBuilder.andWhere('singer.id IN (:...singerKeys)', { singerKeys });
    }

    if (tags) {
      const tagKeys = typeof tags === 'string' ? [tags] : tags;
      queryBuilder.andWhere('tag.id IN (:...tagKeys)', { tagKeys });
    }

    const sortOrderMap = {
      dateDesc: { field: 'createdAt', direction: 'DESC' },
      dateAsc: { field: 'createdAt', direction: 'ASC' },
      durationDesc: { field: 'duration', direction: 'DESC' },
      durationAsc: { field: 'duration', direction: 'ASC' },
      titleDesc: { field: 'title', direction: 'DESC' },
      titleAsc: { field: 'title', direction: 'ASC' },
    };

    if (sortOrderMap[sortOrder]) {
      const { field, direction } = sortOrderMap[sortOrder];
      queryBuilder.orderBy(`audioFile.${field}`, direction);
    }

    const skip = (page - 1) * itemsPerPage;
    queryBuilder.skip(skip).take(itemsPerPage);

    const [data, total] = await queryBuilder.getManyAndCount();

    return {
      items: data.map((e) => {
        return {
          ...e,
          likes: e.likes.length,
          liked: user && e.likes.some((k) => k.user.id === user.id)
        }
      }),
      pagination: {
        total,
        page,
        itemsPerPage,
        totalPages: Math.ceil(total / itemsPerPage)
      }
    };
  }

  async findOneByKey(key: string) {
    return await AudioFile.findOne({
      where: {external_id: key},
      relations: ['tags'],
    })
  }

  async create(body: any) {
    const audioFile = new AudioFile();
    const populatedAudioFile = await this.populateFields(audioFile, body);
    return await AudioFile.save(populatedAudioFile);
  }

  async update(key: string, body: any) {
    const audioFileToUpdate = await this.findOneByKey(key);
    if (!audioFileToUpdate) {
      throw new NotFoundException(`AudioFile with key "${key}" not found`);
    }

    const populatedAudioFile = await this.populateFields(audioFileToUpdate, body);

    return await AudioFile.save(populatedAudioFile);
  }

  async populateFields(audioFile: AudioFile, body: any): Promise<AudioFile> {
    const type = await this.audiofileTypesService.findOneByKey(body.audioType.key);
    const singer = body.audioSinger ? await this.audiofileSingersService.findOneByKey(body.audioSinger.key) : null;

    audioFile.external_id = body.key;
    audioFile.title = body.audioTitle;
    audioFile.url = this.copyFileFromTemp('audioFile', body, 'audio')[0]
    audioFile.duration = body.audioDuration;
    audioFile.description = body.audioDescription;
    audioFile.comment = body.audioComment;
    audioFile.paid = body.paid || false;

    audioFile.type = type;
    audioFile.singer = singer;

    if(body.audioTags && Array.isArray(body.audioTags)) {
      const tags = await Promise.all(
        body.audioTags.map(e =>
          this.audiofileTagsService.findOneByKey(e.key)
        )
      );
      audioFile.tags = tags.filter(t => t !== null);
    }


    return audioFile;
  }

  async remove(key: string) {
    const audioFileToUpdate = await this.findOneByKey(key);
    if (!audioFileToUpdate) {
      throw new NotFoundException(`AudioFile with key "${key}" not found`);
    }

    if(audioFileToUpdate.url) {
      const uploadIndex = audioFileToUpdate.url.indexOf('/upload');
      const result = '.' + audioFileToUpdate.url.substring(uploadIndex);

      if(existsSync(result)) {
        rmSync(result, {recursive: true});
      }
    }

    return await audioFileToUpdate.remove();
  }

  async getTypes() {
    return await AudioFileType.find();
  }

  async getSingers() {
    return await AudioFileSinger.find();
  }

  async getTags() {
    return await AudioFileTag.find();
  }

  copyFileFromTemp(key, body, folder) {
    const baseUrl = process.env.BASE_URL || 'http://localhost:9015'
    const uploadIndex = body[key].indexOf('/upload');
    const result = '.' + body[key].substring(uploadIndex);
    const bookName = basename(body[key])
    const newPath = `./upload/firebase/${folder}/${bookName}`;
    if(body[key].indexOf('upload/tmp') == -1) return [`${baseUrl}/upload/firebase/${folder}/${bookName}`,newPath];
    if(existsSync(result)) {
      copyFileSync(result, newPath);
    }
    return [`${baseUrl}/upload/firebase/${folder}/${bookName}`, newPath];
  }

  async like(id: number, user) {
    const audio = await AudioFile.findOneBy({id})
    const liked = await AudioFileLike.findOne({
      where: {
        audio: {
          id
        },
        user: {
          id: user.id
        }
      }
    })
    if (liked) return await AudioFileLike.delete(liked.id)
    return await AudioFileLike.save({
      audio,
      user
    })
  }
}
