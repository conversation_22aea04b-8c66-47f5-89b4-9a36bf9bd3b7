import { SenderTemplate } from '@/entity/SenderTemplate'
import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { SenderTemplateController } from './sender-template.controller'
import { SenderTemplateService } from './sender-template.service'

@Module({
    imports: [TypeOrmModule.forFeature([SenderTemplate])],
    controllers: [SenderTemplateController],
    providers: [SenderTemplateService]
})
export class SenderTemplateModule {}

