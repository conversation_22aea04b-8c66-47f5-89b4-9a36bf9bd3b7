import { Groups } from '@/api/user/decorators/groups.decorator';
import { AccessGuard } from '@/api/user/guards/access.guard';
import { JwtAuthGuard } from '@/api/user/guards/auth.guard';
import { Body, Controller, Delete, Get, Param, Patch, Post, UseGuards } from '@nestjs/common';
import { SenderTemplateService } from './sender-template.service';

@Controller('admin/sender-template')
@UseGuards(JwtAuthGuard, AccessGuard)
@Groups('ADMIN')
export class SenderTemplateController {
    constructor(private readonly senderTemplateService: SenderTemplateService) {}

    @Get()
    async findAll() {
        return await this.senderTemplateService.findAll();
    }

    @Get(':id')
    async findOne(@Param('id') id: number) {
        return await this.senderTemplateService.findOne(id);
    }

    @Post()
    async create(@Body() dto: any) {
        return await this.senderTemplateService.create(dto);
    }

    @Patch(':id')
    async update(@Param('id') id: number, @Body() dto: any) {
        return await this.senderTemplateService.update(id, dto);
    }

    @Delete(':id')
    async delete(@Param('id') id: number) {
        return await this.senderTemplateService.delete(id);
    }
}

