import {Injectable} from '@nestjs/common';
import {SenderTemplate} from '../../../entity/SenderTemplate';

@Injectable()
export class SenderTemplateService {
    async findAll() {
        return await SenderTemplate.find({
            order: {createdAt: 'DESC'}
        });
    }

    async findOne(id: number) {
        return await SenderTemplate.findOneBy({id});
    }

    async create(dto: any) {
        const template = SenderTemplate.create({
            title: dto.title,
            htmlContent: dto.htmlContent
        });
        await template.save();
        return template;
    }

    async update(id: number, dto: any) {
        const template = await SenderTemplate.findOneBy({id});
        if (!template) {
            return null;
        }
        template.title = dto.title;
        template.htmlContent = dto.htmlContent;
        await template.save();
        return template;
    }

    async delete(id: number) {
        const template = await SenderTemplate.findOneBy({id});
        if (!template) {
            return null;
        }
        await template.remove();
        return {success: true};
    }
}

