import { Sender } from '@/entity/Sender'
import { HttpModule } from '@nestjs/axios'
import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { SenderController } from './sender.controller'
import { SenderService } from './sender.service'

@Module({
    imports: [HttpModule, TypeOrmModule.forFeature([Sender])],
    controllers: [SenderController],
    providers: [SenderService],
})
export class SenderModule {}

