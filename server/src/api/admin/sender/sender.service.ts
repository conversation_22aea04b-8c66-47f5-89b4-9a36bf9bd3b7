import { Sender, SenderStatus } from '@/entity/Sender'
import { User } from '@/entity/User'
import { HttpService } from '@nestjs/axios'
import { BadRequestException, Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { firstValueFrom } from 'rxjs'
import { In } from 'typeorm'

@Injectable()
export class SenderService {
    private readonly sendPulseApiUrl = 'https://api.sendpulse.com';
    private accessToken: string;
    private tokenExpiry: number;

    constructor(
        private readonly httpService: HttpService,
        private readonly configService: ConfigService
    ) {}

    async getAll() {
        return await Sender.find({
            order: {createdAt: 'DESC'}
        });
    }

    async getOne(id: number) {
        return await Sender.findOneBy({id});
    }

    async create(dto: any) {
        const sender = Sender.create({
            title: dto.title,
            subject: dto.subject,
            htmlContent: dto.htmlContent,
            emails: dto.emails || [],
            userIds: dto.userIds || [],
            userGroups: dto.userGroups || [],
            status: SenderStatus.DRAFT
        });
        await sender.save();

        const recipients = await this.collectRecipients(sender);
        sender.recipientsCount = recipients.length;
        await sender.save();

        return sender;
    }

    async update(id: number, dto: any) {
        const sender = await Sender.findOneBy({id});
        if (!sender) {
            throw new BadRequestException('Рассылка не найдена');
        }

        sender.title = dto.title;
        sender.subject = dto.subject;
        sender.htmlContent = dto.htmlContent;
        sender.emails = dto.emails || [];
        sender.userIds = dto.userIds || [];
        sender.userGroups = dto.userGroups || [];

        const recipients = await this.collectRecipients(sender);
        sender.recipientsCount = recipients.length;

        return await sender.save();
    }

    async delete(id: number) {
        const sender = await Sender.findOneBy({id});
        if (!sender) {
            throw new BadRequestException('Рассылка не найдена');
        }
        return await sender.remove();
    }

    async send(id: number) {
        const sender = await Sender.findOneBy({id});
        if (!sender) {
            throw new BadRequestException('Рассылка не найдена');
        }

        try {
            const recipients = await this.collectRecipients(sender);
            
            if (recipients.length === 0) {
                throw new BadRequestException('Нет получателей для рассылки');
            }

            await this.ensureAccessToken();
            await this.sendViaSendPulse(sender, recipients);

            sender.status = SenderStatus.SENT;
            sender.sentAt = new Date();
            sender.recipientsCount = recipients.length;
            await sender.save();

            return {success: true, recipientsCount: recipients.length};
        } catch (error) {
            sender.status = SenderStatus.FAILED;
            sender.errorMessage = error.message;
            await sender.save();
            throw error;
        }
    }

    private async collectRecipients(sender: Sender): Promise<string[]> {
        const recipients = new Set<string>();

        if (sender.emails && sender.emails.length > 0) {
            sender.emails.forEach(email => recipients.add(email));
        }

        if (sender.userIds && sender.userIds.length > 0) {
            const users = await User.find({
                where: {id: In(sender.userIds)},
                select: ['email']
            });
            users.forEach(user => recipients.add(user.email));
        }

        if (sender.userGroups && sender.userGroups.length > 0) {
            const users = await User.createQueryBuilder('user')
                .select('user.email')
                .where('user.groups && ARRAY[:...groups]::varchar[]', {groups: sender.userGroups})
                .getMany();
            users.forEach(user => recipients.add(user.email));
        }

        return Array.from(recipients);
    }

    private async ensureAccessToken() {
        const now = Date.now();
        if (this.accessToken && this.tokenExpiry > now) {
            return;
        }

        const response = await firstValueFrom(
            this.httpService.post(`${this.sendPulseApiUrl}/oauth/access_token`, {
                grant_type: 'client_credentials',
                client_id: this.configService.get('SENDPULSE_ID'),
                client_secret: this.configService.get('SENDPULSE_SECRET')
            })
        );

        this.accessToken = response.data.access_token;
        this.tokenExpiry = now + (response.data.expires_in * 1000);
    }

    private async sendViaSendPulse(sender: Sender, recipients: string[]) {
        const htmlBase64 = Buffer.from(sender.htmlContent).toString('base64');

        const emailData = {
            email: {
                subject: sender.subject,
                html: htmlBase64,
                from: {
                    name: 'Sanatanadharma',
                    email: '<EMAIL>'
                },
                to: recipients.map(email => ({email}))
            }
        };

        await firstValueFrom(
            this.httpService.post(`${this.sendPulseApiUrl}/smtp/emails`, emailData, {
                headers: {
                    'Authorization': `Bearer ${this.accessToken}`,
                    'Content-Type': 'application/json'
                }
            })
        );
    }
}

