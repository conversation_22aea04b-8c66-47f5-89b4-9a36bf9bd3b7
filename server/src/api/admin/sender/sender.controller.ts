import { Groups } from '@/api/user/decorators/groups.decorator'
import { AccessGuard } from '@/api/user/guards/access.guard'
import { JwtAuthGuard } from '@/api/user/guards/auth.guard'
import { Body, Controller, Delete, Get, Param, Patch, Post, UseGuards } from '@nestjs/common'
import { SenderService } from './sender.service'

@Controller('admin/sender')
@Groups('ADMIN')
@UseGuards(JwtAuthGuard, AccessGuard)
export class SenderController {
    constructor(private readonly senderService: SenderService) {}

    @Get()
    async getAll() {
        return await this.senderService.getAll();
    }

    @Get(':id')
    async getOne(@Param('id') id: number) {
        return await this.senderService.getOne(id);
    }

    @Post()
    async create(@Body() dto: any) {
        return await this.senderService.create(dto);
    }

    @Patch(':id')
    async update(@Param('id') id: number, @Body() dto: any) {
        return await this.senderService.update(id, dto);
    }

    @Delete(':id')
    async delete(@Param('id') id: number) {
        return await this.senderService.delete(id);
    }

    @Post(':id/send')
    async send(@Param('id') id: number) {
        return await this.senderService.send(id);
    }
}

