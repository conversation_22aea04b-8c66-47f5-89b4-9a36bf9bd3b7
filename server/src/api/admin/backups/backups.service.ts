import { FileValidator } from '@/common/utils/file-validator.util'
import { Backup, BackupStatus } from '@/entity/Backup'
import { HttpService } from '@nestjs/axios'
import { Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { InjectRepository } from '@nestjs/typeorm'
import { exec } from 'child_process'
import * as fs from 'fs'
import * as path from 'path'
import * as tar from 'tar'
import { Repository } from 'typeorm'
import { promisify } from 'util'

const execAsync = promisify(exec);

@Injectable()
export class BackupsService {
  private readonly backupDir = path.join(process.cwd(), 'backups');

  constructor(
    @InjectRepository(Backup)
    private backupRepository: Repository<Backup>,
    private configService: ConfigService,
    private httpService: HttpService,
  ) {
    this.ensureBackupDirectory();
  }

  private ensureBackupDirectory() {
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
    }
  }

  async createBackup(createBackupDto: any) {
    await this.cleanupOldBackups();

    const backup = this.backupRepository.create({
      description: createBackupDto.description,
      status: BackupStatus.PENDING,
    });

    const savedBackup = await this.backupRepository.save(backup);

    this.performBackup(savedBackup)

    return savedBackup;
  }

  async createBackupFromFile(file: Express.Multer.File, createBackupDto: any) {
    FileValidator.validateBackupFile(file.buffer, file.originalname);

    await this.cleanupOldBackups();

    const timestamp = new Date().getTime();
    const fileName = `uploaded_backup_${timestamp}.tar.gz`;
    const filePath = path.join(this.backupDir, fileName);

    try {
      fs.writeFileSync(filePath, file.buffer);

      const fileSize = fs.statSync(filePath).size;

      const backup = this.backupRepository.create({
        description: createBackupDto.description || `Ручной импорт`,
        status: BackupStatus.COMPLETED,
        filePath,
        fileSize
      });

      const savedBackup = await this.backupRepository.save(backup);

      return savedBackup;
    } catch (error) {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
      throw new Error(`Ошибка сохранения загруженного бекапа: ${error.message}`);
    }
  }

  private async performBackup(backup: Backup) {
    try {
      await this.updateBackupStatus(backup.id, BackupStatus.IN_PROGRESS);

      const dbPath = await this.createDatabaseBackup(backup.id);
      const filesPath = await this.createFilesBackup(backup.id);
      const firebasePath = await this.createFirebaseBackup(backup.id);
      const filePath = await this.createFullBackup(backup.id, dbPath, filesPath, firebasePath);

      fs.unlinkSync(dbPath);
      fs.unlinkSync(filesPath);

      if(firebasePath) {
        fs.unlinkSync(firebasePath);
      }

      const fileSize = fs.statSync(filePath).size;

      await this.backupRepository.update(backup.id, {
        status: BackupStatus.COMPLETED,
        filePath,
        fileSize
      });
    } catch (error) {
      await this.backupRepository.update(backup.id, {
        status: BackupStatus.FAILED,
        errorMessage: error.message,
      });
    }
  }

  private async createDatabaseBackup(backupId: number) {
    const timestamp = new Date().getTime();
    const fileName = `db_backup_${backupId}_${timestamp}.sql`;
    const filePath = path.join(this.backupDir, fileName);

    const dbConfig = {
      host: this.configService.get('POSTGRES_HOST'),
      port: this.configService.get('POSTGRES_PORT'),
      username: this.configService.get('POSTGRES_USER'),
      password: this.configService.get('POSTGRES_PASSWORD'),
      database: this.configService.get('POSTGRES_DATABASE'),
    };

    const env = { ...process.env, PGPASSWORD: dbConfig.password };

    const command = `pg_dump -h ${dbConfig.host} -p ${dbConfig.port} -U ${dbConfig.username} -d ${dbConfig.database} -f "${filePath}" --verbose --no-password --exclude-table=backups`;

    try {
      await execAsync(command, { env });
      return filePath;
    } catch (error) {
      throw new Error(`Ошибка создания резервной копии базы данных: ${error.message}`);
    }
  }

  private async createFilesBackup(backupId: number) {
    const timestamp = new Date().getTime();
    const fileName = `files_backup_${backupId}_${timestamp}.tar.gz`;
    const filePath = path.join(this.backupDir, fileName);
    const uploadDir = path.join(process.cwd(), 'upload');

    if (!fs.existsSync(uploadDir)) {
      throw new Error('Папка upload не найдена');
    }

    try {
      await tar.create(
        {
          gzip: true,
          file: filePath,
          cwd: process.cwd(),
        },
        ['upload']
      );
      return filePath;
    } catch (error) {
      throw new Error(`Ошибка создания резервной копии файлов: ${error.message}`);
    }
  }

  private async createFirebaseBackup(backupId: number) {
    const timestamp = new Date().getTime();
    const fileName = `firebase_backup_${backupId}_${timestamp}.json`;
    const filePath = path.join(this.backupDir, fileName);

    try {
      const firebaseDatabaseURL = this.configService.get('FIREBASE_DATABASE_URL');
      if (!firebaseDatabaseURL) return false;

      const apiUrl = `${firebaseDatabaseURL.replace(/\/$/, '')}/.json`;

      const response = await this.httpService.axiosRef.get(apiUrl, {
        timeout: 30000,
        responseType: 'json'
      });

      const firebaseData = response.data || {};

      fs.writeFileSync(filePath, JSON.stringify(firebaseData));

      return filePath;
    } catch (error) {
      return false;
    }
  }

  private async createFullBackup(backupId: number, dbPath: string, filesPath: string, firebasePath?: string | false) {
    const timestamp = new Date().getTime();
    const fileName = `full_backup_${backupId}_${timestamp}.tar.gz`;
    const filePath = path.join(this.backupDir, fileName);

    try {
      const filesToInclude = [path.basename(dbPath), path.basename(filesPath)];

      if (firebasePath) {
        filesToInclude.push(path.basename(firebasePath));
      }

      await tar.create(
        {
          gzip: true,
          file: filePath,
          cwd: this.backupDir,
        },
        filesToInclude
      );
      return filePath;
    } catch (error) {
      throw new Error(`Ошибка создания резервной копии: ${error.message}`);
    }
  }

  private async updateBackupStatus(id: number, status: BackupStatus) {
    await this.backupRepository.update(id, { status });
  }

  async getBackups(query: any) {
    const queryBuilder = this.backupRepository.createQueryBuilder('backup');

    if (query.status) {
      queryBuilder.andWhere('backup.status = :status', { status: query.status });
    }

    return queryBuilder
      .orderBy('backup.createdAt', 'DESC')
      .getMany();
  }

  async getBackupById(id: number) {
    const backup = await this.backupRepository.findOne({ where: { id } });
    if (!backup) {
      throw new Error(`Резервная копия с id ${id} не найдена`);
    }
    return backup;
  }

  async deleteBackup(id: number) {
    const backup = await this.getBackupById(id);
    
    if (backup.filePath && fs.existsSync(backup.filePath)) {
      fs.unlinkSync(backup.filePath);
    }

    await this.backupRepository.delete(id);
  }

  async downloadBackup(id: number) {
    const backup = await this.getBackupById(id);

    if (!backup.filePath || !fs.existsSync(backup.filePath)) {
      throw new Error('Резервная копия не найдена');
    }

    return {
      filePath: backup.filePath,
      fileName: path.basename(backup.filePath),
    };
  }

  async restoreBackup(id: number) {
    const backup = await this.getBackupById(id);

    if (backup.status !== BackupStatus.COMPLETED) {
      throw new Error('Можно восстанавливать только завершенные резервные копии');
    }

    if (!backup.filePath || !fs.existsSync(backup.filePath)) {
      throw new Error('Файл резервной копии не найден');
    }

    let tempDir: string;

    try {
      tempDir = path.join(this.backupDir, `restore_temp_${Date.now()}`);
      fs.mkdirSync(tempDir, { recursive: true });

      await tar.extract({
        file: backup.filePath,
        cwd: tempDir,
      });

      const files = fs.readdirSync(tempDir);

      const dbFile = files.find(f => f.endsWith('.sql'));
      const filesArchive = files.find(f => f.includes('files_backup') && f.endsWith('.tar.gz'));
      const firebaseFile = files.find(f => f.includes('firebase_backup') && f.endsWith('.json'));

      if (!dbFile) {
        throw new Error('Файл резервной копии базы данных не найден в архиве');
      }

      if (!filesArchive) {
        throw new Error('Архив файлов не найден в резервной копии');
      }

      await this.restoreDatabase(path.join(tempDir, dbFile));
      await this.restoreFiles(path.join(tempDir, filesArchive));

      if (firebaseFile) {
        await this.restoreFirebase(path.join(tempDir, firebaseFile));
      }

      fs.rmSync(tempDir, { recursive: true, force: true });

      return { message: `Резервная копия ${id} успешно восстановлена` };
    } catch (error) {
      if (tempDir && fs.existsSync(tempDir)) {
        fs.rmSync(tempDir, { recursive: true, force: true });
      }

      throw new Error(`Ошибка восстановления: ${error.message}`);
    }
  }

  private async restoreDatabase(sqlFilePath: string) {
    const dbConfig = {
      host: this.configService.get('POSTGRES_HOST'),
      port: this.configService.get('POSTGRES_PORT'),
      username: this.configService.get('POSTGRES_USER'),
      password: this.configService.get('POSTGRES_PASSWORD'),
      database: this.configService.get('POSTGRES_DATABASE'),
    };

    const env = { ...process.env, PGPASSWORD: dbConfig.password };

    try {
      const backupTablePath = path.join(this.backupDir, `backups_table_${Date.now()}.sql`);
      const saveBackupsCommand = `pg_dump -h ${dbConfig.host} -p ${dbConfig.port} -U ${dbConfig.username} -d ${dbConfig.database} -t backups --data-only --no-owner --no-privileges -f "${backupTablePath}"`;

      await execAsync(saveBackupsCommand, { env });

      const dropCommand = `psql -h ${dbConfig.host} -p ${dbConfig.port} -U ${dbConfig.username} -d ${dbConfig.database} -c "DROP SCHEMA public CASCADE; CREATE SCHEMA public;"`;
      await execAsync(dropCommand, { env });

      const restoreCommand = `psql -h ${dbConfig.host} -p ${dbConfig.port} -U ${dbConfig.username} -d ${dbConfig.database} -f "${sqlFilePath}" --quiet`;
      await execAsync(restoreCommand, { env });

      await this.createBackupsTable(dbConfig, env);

      if (fs.existsSync(backupTablePath)) {
        const restoreBackupsCommand = `psql -h ${dbConfig.host} -p ${dbConfig.port} -U ${dbConfig.username} -d ${dbConfig.database} -f "${backupTablePath}" --quiet`;
        await execAsync(restoreBackupsCommand, { env });

        fs.unlinkSync(backupTablePath);
      }

    } catch (error) {
      throw new Error(`Ошибка восстановления базы данных: ${error.message}`);
    }
  }

  private async restoreFiles(filesArchivePath: string) {
    const uploadDir = path.join(process.cwd(), 'upload');
    const tempRestoreDir = path.join(this.backupDir, `temp_restore_${Date.now()}`);

    try {
      fs.mkdirSync(tempRestoreDir, { recursive: true });

      await tar.extract({
        file: filesArchivePath,
        cwd: tempRestoreDir,
      });

      const extractedUploadDir = path.join(tempRestoreDir, 'upload');

      if (!fs.existsSync(extractedUploadDir)) {
        throw new Error('Папка upload не найдена в архиве');
      }

      if (fs.existsSync(uploadDir)) {
        const files = fs.readdirSync(uploadDir);

        for (const file of files) {
          const filePath = path.join(uploadDir, file);
          const stat = fs.statSync(filePath);

          if (stat.isDirectory()) {
            fs.rmSync(filePath, { recursive: true, force: true });
          } else {
            fs.unlinkSync(filePath);
          }
        }
      } else {
        fs.mkdirSync(uploadDir, { recursive: true });
      }

      const extractedFiles = fs.readdirSync(extractedUploadDir);

      for (const file of extractedFiles) {
        try {
          const srcPath = path.join(extractedUploadDir, file);
          const destPath = path.join(uploadDir, file);

          const stat = fs.statSync(srcPath);
          if (stat.isDirectory()) {
            this.copyDirectoryRecursive(srcPath, destPath);
          } else {
            fs.copyFileSync(srcPath, destPath);
          }
        } catch (copyError) {
          throw copyError;
        }
      }

      fs.rmSync(tempRestoreDir, { recursive: true, force: true });
    } catch (error) {
      if (fs.existsSync(tempRestoreDir)) {
        fs.rmSync(tempRestoreDir, { recursive: true, force: true });
      }

      throw new Error(`Ошибка восстановления файлов: ${error.message}`);
    }
  }

  private async restoreFirebase(firebaseFilePath: string) {
    try {
      const firebaseDatabaseURL = this.configService.get('FIREBASE_DATABASE_URL');
      if (!firebaseDatabaseURL) {
        return;
      }

      const firebaseDataRaw = await fs.promises.readFile(firebaseFilePath, 'utf8');
      const firebaseData = JSON.parse(firebaseDataRaw);

      if (firebaseData.error) {
        return;
      }

      const apiUrl = `${firebaseDatabaseURL.replace(/\/$/, '')}/.json`;

      const response = await fetch(apiUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(firebaseData)
      });

      if (!response.ok) {
        throw new Error(`Ошибка при записи данных в Firebase: ${response.status}`);
      }

    } catch (error) {
    }
  }

  private copyDirectoryRecursive(src: string, dest: string) {
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }

    const files = fs.readdirSync(src);
    for (const file of files) {
      const srcPath = path.join(src, file);
      const destPath = path.join(dest, file);

      const stat = fs.statSync(srcPath);
      if (stat.isDirectory()) {
        this.copyDirectoryRecursive(srcPath, destPath);
      } else {
        fs.copyFileSync(srcPath, destPath);
      }
    }
  }

  private async createBackupsTable(dbConfig: any, env: any) {
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS backups (
        id SERIAL PRIMARY KEY,
        status VARCHAR(20) NOT NULL,
        description TEXT,
        file_path VARCHAR(500),
        file_size BIGINT,
        error_message TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;

    const createTableCommand = `psql -h ${dbConfig.host} -p ${dbConfig.port} -U ${dbConfig.username} -d ${dbConfig.database} -c "${createTableSQL.replace(/\n/g, ' ')}"`;

    try {
      await execAsync(createTableCommand, { env });
    } catch (error) {
      throw error;
    }
  }

  private async cleanupOldBackups() {
      const completedBackups = await this.backupRepository
        .createQueryBuilder('backup')
        .where('backup.status = :status', { status: BackupStatus.COMPLETED })
        .orderBy('backup.created_at', 'DESC')
        .getMany();

      if (completedBackups.length >= 3) {
        const backupsToDelete = completedBackups.slice(2);

        for (const backup of backupsToDelete) {

          if (backup.filePath && fs.existsSync(backup.filePath)) {
            fs.unlinkSync(backup.filePath);
          }

          await this.backupRepository.delete(backup.id);
        }
      }
  }
}
