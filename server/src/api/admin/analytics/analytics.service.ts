import { ActivityLog, ActivityType } from '@/entity/ActivityLog'
import { PaymentHistory, PaymentStatus } from '@/entity/PaymentHistory'
import { User } from '@/entity/User'
import { UserSubscriptions } from '@/entity/UserSubscriptions'
import { Injectable } from '@nestjs/common'

@Injectable()
export class AnalyticsService {
    async getDashboardStats(period: string = 'all') {
        let startDate: Date | null = null

        if (period === 'day') {
            startDate = new Date()
            startDate.setHours(0, 0, 0, 0)
        } else if (period === 'week') {
            startDate = new Date()
            startDate.setDate(startDate.getDate() - 7)
        } else if (period === 'month') {
            startDate = new Date()
            startDate.setMonth(startDate.getMonth() - 1)
        }

        const totalUsers = await User.count()

        const contentViewsQuery = ActivityLog
            .createQueryBuilder('log')
            .select('COUNT(DISTINCT COALESCE(CAST(log.userId AS VARCHAR), log.ipAddress))', 'count')
            .where('log.type = :type', { type: ActivityType.CONTENT_VIEW })

        if (startDate) {
            contentViewsQuery.andWhere('log.createdAt >= :startDate', { startDate })
        }

        const contentViews = await contentViewsQuery.getRawOne()

        const libraryViewsQuery = ActivityLog
            .createQueryBuilder('log')
            .select('COUNT(DISTINCT COALESCE(CAST(log.userId AS VARCHAR), log.ipAddress))', 'count')
            .where('log.type = :type', { type: ActivityType.LIBRARY_VIEW })

        if (startDate) {
            libraryViewsQuery.andWhere('log.createdAt >= :startDate', { startDate })
        }

        const libraryViews = await libraryViewsQuery.getRawOne()

        const registrationsQuery = ActivityLog
            .createQueryBuilder('log')
            .where('log.type = :type', { type: ActivityType.REGISTRATION })

        if (startDate) {
            registrationsQuery.andWhere('log.createdAt >= :startDate', { startDate })
        }

        const recentRegistrations = await registrationsQuery.getCount()

        const donationsQuery = PaymentHistory
            .createQueryBuilder('payment')
            .select('SUM(CASE WHEN payment.currency = \'RUB\' THEN payment.amount / 100 ELSE payment.amount END)', 'total')
            .where('payment.status = :status', { status: PaymentStatus.SUCCESS })
            .andWhere("LOWER(payment.details) LIKE LOWER('%пожертвование%')")

        if (startDate) {
            donationsQuery.andWhere('payment.date >= :startDate', { startDate })
        }

        const donations = await donationsQuery.getRawOne()

        const subscriptionsCountQuery = UserSubscriptions
            .createQueryBuilder('sub')
            .select('COUNT(*)', 'count')

        if (startDate) {
            subscriptionsCountQuery.where('sub.createdAt >= :startDate', { startDate })
        }

        const subscriptionsCount = await subscriptionsCountQuery.getRawOne()

        const subscriptionsSumQuery = PaymentHistory
            .createQueryBuilder('payment')
            .select('SUM(CASE WHEN payment.currency = \'RUB\' THEN payment.amount / 100 ELSE payment.amount END)', 'total')
            .where('payment.status = :status', { status: PaymentStatus.SUCCESS })
            .andWhere("payment.details NOT LIKE 'Покупка:%'")
            .andWhere("LOWER(payment.details) NOT LIKE LOWER('%пожертвование%')")

        if (startDate) {
            subscriptionsSumQuery.andWhere('payment.date >= :startDate', { startDate })
        }

        const subscriptionsSum = await subscriptionsSumQuery.getRawOne()

        const purchasesQuery = PaymentHistory
            .createQueryBuilder('payment')
            .select('COUNT(*)', 'count')
            .addSelect('SUM(CASE WHEN payment.currency = \'RUB\' THEN payment.amount / 100 ELSE payment.amount END)', 'total')
            .where('payment.status = :status', { status: PaymentStatus.SUCCESS })
            .andWhere("payment.details LIKE 'Покупка:%'")

        if (startDate) {
            purchasesQuery.andWhere('payment.date >= :startDate', { startDate })
        }

        const purchases = await purchasesQuery.getRawOne()

        return {
            totalUsers,
            contentViews: parseInt(contentViews.count) || 0,
            libraryViews: parseInt(libraryViews.count) || 0,
            recentRegistrations,
            donationsTotal: parseFloat(donations.total) || 0,
            subscriptionsCount: parseInt(subscriptionsCount.count) || 0,
            subscriptionsTotal: parseFloat(subscriptionsSum.total) || 0,
            purchasesCount: parseInt(purchases.count) || 0,
            purchasesTotal: parseFloat(purchases.total) || 0,
            period
        }
    }

    async getActivityLogs(page: number = 1, limit: number = 50) {
        const [logs, total] = await ActivityLog.findAndCount({
            relations: ['user'],
            order: { createdAt: 'DESC' },
            skip: (page - 1) * limit,
            take: limit
        })

        return {
            logs,
            total,
            page,
            totalPages: Math.ceil(total / limit)
        }
    }
}

