import { Groups } from '@/api/user/decorators/groups.decorator'
import { AccessGuard } from '@/api/user/guards/access.guard'
import { JwtAuthGuard } from '@/api/user/guards/auth.guard'
import { Controller, Get, Query, UseGuards } from '@nestjs/common'
import { AnalyticsService } from './analytics.service'

@Controller('admin/analytics')
@Groups('ADMIN')
@UseGuards(JwtAuthGuard, AccessGuard)
export class AnalyticsController {
    constructor(private readonly analyticsService: AnalyticsService) {}

    @Get('dashboard')
    async getDashboardStats(@Query('period') period: string = 'all') {
        return await this.analyticsService.getDashboardStats(period)
    }

    @Get('logs')
    async getActivityLogs(
        @Query('page') page: string = '1',
        @Query('limit') limit: string = '50'
    ) {
        return await this.analyticsService.getActivityLogs(
            parseInt(page),
            parseInt(limit)
        )
    }

}

