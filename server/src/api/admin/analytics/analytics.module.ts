import { ActivityLog } from '@/entity/ActivityLog'
import { User } from '@/entity/User'
import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { AnalyticsController } from './analytics.controller'
import { AnalyticsService } from './analytics.service'

@Module({
    imports: [TypeOrmModule.forFeature([ActivityLog, User])],
    controllers: [AnalyticsController],
    providers: [AnalyticsService],
    exports: [AnalyticsService]
})
export class AnalyticsModule {}

