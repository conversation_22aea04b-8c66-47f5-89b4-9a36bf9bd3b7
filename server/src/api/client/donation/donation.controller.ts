import { User } from '@/api/user/decorators/user.decorator'
import { OptionalJwtAuthGuard } from '@/api/user/guards/auth.optional.guard'
import { Body, Controller, Post, UseGuards } from '@nestjs/common'
import { Throttle } from '@nestjs/throttler'
import { CreatePaymentDto } from './create-payment.dto'
import { DonationService } from './donation.service'

@Controller('client/donation')
export class DonationController {
  constructor(private readonly donationService: DonationService) {}

  @Post('create-payment')
  @Throttle({ default: { limit: 10, ttl: 60000 } })
  @UseGuards(OptionalJwtAuthGuard)
  createPaymentLink(@Body() body: CreatePaymentDto, @User() user: any) {
    return this.donationService.createPayment(body, user);
  }
}