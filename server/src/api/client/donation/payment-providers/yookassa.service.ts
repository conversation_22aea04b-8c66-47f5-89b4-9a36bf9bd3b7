import { UrlValidator } from '@/common/utils/url-validator.util'
import { HttpService } from '@nestjs/axios'
import { Injectable, InternalServerErrorException } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { firstValueFrom } from 'rxjs'
import { v4 as uuidv4 } from 'uuid'

@Injectable()
export class YookassaService {
  private readonly apiUrl = 'https://api.yookassa.ru/v3/payments';
  private readonly shopId: string;
  private readonly secretKey: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {
    this.shopId = this.configService.get<string>('YOOKASSA_SHOP_ID');
    this.secretKey = this.configService.get<string>('YOOKASSA_SECRET_KEY');
  }

  async createPayment(amount: number, currency: string, description: string, metadata: any = {}, returnUrl?: string) {
    const idempotenceKey = uuidv4();

    const autoRenew = metadata?.autoRenew;
    const isDevelopment = process.env.BUILD_ENV === 'local';
    let finalReturnUrl = this.configService.get<string>('DONATION_SUCCESS_URL');

    if (returnUrl) {
      try {
        finalReturnUrl = UrlValidator.validateReturnUrl(returnUrl, isDevelopment);
      } catch (error) {
      }
    }

    const payload: any = {
      amount: { value: amount.toFixed(2), currency },
      confirmation: {
        type: 'redirect',
        return_url: finalReturnUrl,
      },
      capture: true,
      description: description + ` (ID пользователя: ${metadata?.userId || 0})`,
      metadata,
      receipt: {
        customer: {
          email: metadata?.email || '<EMAIL>'
        },
        items: [
          {
            description: description,
            quantity: 1.00,
            amount: {
              value: amount.toFixed(2),
              currency: currency
            },
            vat_code: 1,
            payment_mode: 'full_payment',
            payment_subject: 'payment'
          }
        ]
      }
    };

    if (autoRenew) {
      payload.save_payment_method = true;
      payload.payment_method_data = { type: 'bank_card' };
    }

    const headers = {
      'Content-Type': 'application/json',
      'Idempotence-Key': idempotenceKey,
      'Authorization': 'Basic ' + Buffer.from(`${this.shopId}:${this.secretKey}`).toString('base64'),
    };

    try {
      const response = await firstValueFrom(this.httpService.post(this.apiUrl, payload, { headers }));
      const paymentUrl = response.data.confirmation.confirmation_url;
      return { paymentUrl };
    } catch(error) {
      throw new InternalServerErrorException('Ошибка при создании платежа через ЮKassa');
    }
  }

  async getEurCourse() {
    const response = await firstValueFrom(this.httpService.get('https://www.cbr-xml-daily.ru/daily_json.js'));
    return response.data.Valute.EUR.Value;
  }

  async createRecurringPayment(amount: number, currency: string, description: string, paymentMethodId: string, metadata: any = {}, returnUrl?: string) {
    const idempotenceKey = uuidv4();
    const isDevelopment = process.env.BUILD_ENV === 'local';
    let finalReturnUrl = this.configService.get<string>('DONATION_SUCCESS_URL');

    if (returnUrl) {
      try {
        finalReturnUrl = UrlValidator.validateReturnUrl(returnUrl, isDevelopment);
      } catch (error) {
      }
    }

    const payload = {
      type: 'bank_card',
      confirmation: {
        type: 'redirect',
        return_url: finalReturnUrl,
      }
    };

    const headers = {
      'Content-Type': 'application/json',
      'Idempotence-Key': idempotenceKey,
      'Authorization': 'Basic ' + Buffer.from(`${this.shopId}:${this.secretKey}`).toString('base64'),
    };

    try {
      const response = await firstValueFrom(this.httpService.post('https://api.yookassa.ru/v3/payment_methods', payload, { headers }));
      return response.data;
    } catch (error) {
      throw new InternalServerErrorException('Ошибка при создании повторного платежа через ЮKassa');
    }
  }

  async cancelAutoRenew(paymentMethodId: string) {
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Basic ' + Buffer.from(`${this.shopId}:${this.secretKey}`).toString('base64'),
    };

    try {
      const response = await firstValueFrom(
        this.httpService.delete(`https://api.yookassa.ru/v3/payment_methods/${paymentMethodId}`, { headers })
      );
      return response.data;
    } catch (error) {
      throw new InternalServerErrorException('Ошибка при отвязке метода оплаты в ЮKassa');
    }
  }
}