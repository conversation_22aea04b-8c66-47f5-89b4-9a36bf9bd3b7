import { User } from '@/entity/User'
import { BadRequestException, Injectable } from '@nestjs/common'
import { CreatePaymentDto, PaymentProviderType } from './create-payment.dto'
import { StripeService } from './payment-providers/stripe.service'
import { YookassaService } from './payment-providers/yookassa.service'

export interface IPaymentProvider {
  createPayment(amount: number, currency: string, description: string, metadata?: any, returnUrl?: string): Promise<{ paymentUrl: string }>;
  cancelAutoRenew(subscriptionId: string): Promise<any>;
  getEurCourse?(): Promise<number>;
}

@Injectable()
export class DonationService {
  private providers: Map<PaymentProviderType, IPaymentProvider> = new Map();

  constructor(
    private readonly yookassaService: YookassaService,
    private readonly stripeService: StripeService,
  ) {
    this.providers.set(PaymentProviderType.YOOKASSA, this.yookassaService);
    this.providers.set(PaymentProviderType.STRIPE, this.stripeService);
  }

  async createPayment(body: CreatePaymentDto, user?: User) {
    const provider = this.providers.get(body.type);

    if (!provider) {
      throw new BadRequestException('Выбранный способ оплаты не поддерживается.');
    }

    let currency: string;
    const description = 'Пожертвование: ' + ((body.comment || (body.autoRenew ? `Ежемесячное пожертвование` : `Пожертвование на сумму ${body.sum}`)));

    if (body.type === PaymentProviderType.STRIPE) {
      currency = 'EUR';
    } else if (body.type === PaymentProviderType.YOOKASSA) {
      currency = 'RUB';
    }

    const metadata: any = {
      autoRenew: body.autoRenew,
      module: 'donation',
    };

    if (user?.id) {
      metadata.userId = user.id;
    }

    return provider.createPayment(body.sum, currency, description, metadata, body.returnUrl);
  }
}