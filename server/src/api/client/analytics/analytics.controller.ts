import { ActivityType } from '@/entity/ActivityLog'
import { Body, Controller, Post, Req } from '@nestjs/common'
import { AnalyticsService } from './analytics.service'

@Controller('client/analytics')
export class AnalyticsController {
    constructor(private readonly analyticsService: AnalyticsService) {}

    @Post('log')
    async logActivity(
        @Body('type') type: ActivityType,
        @Body('metadata') metadata: any,
        @Req() req: any
    ) {
        const userId = req.user?.id || null
        const ipAddress = req.ip || req.connection.remoteAddress
        const userAgent = req.headers['user-agent']

        return await this.analyticsService.logActivity(
            type,
            userId,
            ipAddress,
            userAgent,
            metadata
        )
    }
}

