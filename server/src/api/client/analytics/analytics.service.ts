import { ActivityLog, ActivityType } from '@/entity/ActivityLog'
import { Injectable } from '@nestjs/common'

@Injectable()
export class AnalyticsService {
    async logActivity(
        type: ActivityType,
        userId: number | null,
        ipAddress: string,
        userAgent: string,
        metadata?: any
    ) {
        return await ActivityLog.save({
            type,
            userId,
            ipAddress,
            userAgent,
            metadata
        })
    }
}

