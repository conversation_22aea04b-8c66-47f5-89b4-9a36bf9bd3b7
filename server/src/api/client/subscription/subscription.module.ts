import { StripeService } from '@/api/client/donation/payment-providers/stripe.service'
import { YookassaService } from '@/api/client/donation/payment-providers/yookassa.service'
import { PaymentHistoryService } from '@/api/user/payment-history/payment-history.service'
import { HttpModule } from '@nestjs/axios'
import { Module } from '@nestjs/common'
import { SubscriptionAutoRenewalService } from './subscription-auto-renewal.service'
import { SubscriptionController } from './subscription.controller'
import { SubscriptionService } from './subscription.service'

@Module({
  imports: [HttpModule],
  controllers: [SubscriptionController],
  providers: [SubscriptionService, SubscriptionAutoRenewalService, YookassaService, StripeService, PaymentHistoryService],
})
export class SubscriptionModule {}
