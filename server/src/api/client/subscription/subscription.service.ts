import { PaymentProviderType } from '@/api/client/donation/create-payment.dto'
import { IPaymentProvider } from '@/api/client/donation/donation.service'
import { StripeService } from '@/api/client/donation/payment-providers/stripe.service'
import { YookassaService } from '@/api/client/donation/payment-providers/yookassa.service'
import { PaymentHistoryService } from '@/api/user/payment-history/payment-history.service'
import { Subscriptions } from '@/common/subscription/subscription.constants'
import { Content } from '@/entity/Content'
import { LibraryTranslation } from '@/entity/LibraryTranslation'
import { PaymentProvider, PaymentStatus } from '@/entity/PaymentHistory'
import { User } from '@/entity/User'
import { UserSubscriptions } from '@/entity/UserSubscriptions'
import { BadRequestException, Injectable } from '@nestjs/common'

@Injectable()
export class SubscriptionService {
  private providers: Map<PaymentProviderType, IPaymentProvider> = new Map();

  constructor(
    private readonly yookassaService: YookassaService,
    private readonly stripeService: StripeService,
    private readonly paymentHistoryService: PaymentHistoryService,
  ) {
    this.providers.set(PaymentProviderType.YOOKASSA, this.yookassaService);
    this.providers.set(PaymentProviderType.STRIPE, this.stripeService);
  }

  async getList() {
    return Subscriptions;
  }

  async pay(body: any, user: User) {
    const payment = body.payment;
    const autoRenew: boolean = true;
    const isYearly: boolean = body.isYearly || false;
    const returnUrl = body.returnUrl;

    const subscriptionTypes = body.types || [body.type];

    if (!subscriptionTypes || subscriptionTypes.length === 0) {
      throw new BadRequestException('Не указаны подписки для оплаты');
    }

    const subscriptions = [];
    for (const type of subscriptionTypes) {
      const subscription = Subscriptions[type];
      if (!subscription) {
        throw new BadRequestException(`Подписка ${type} не найдена`);
      }
      subscriptions.push({ type, subscription });
    }

    const provider = this.providers.get(payment);
    if (!provider) {
      throw new BadRequestException('Выбранный способ оплаты не поддерживается.');
    }

    let currency: string;
    if (payment === PaymentProviderType.STRIPE) currency = 'EUR';
    else if (payment === PaymentProviderType.YOOKASSA) currency = 'RUB';
    else throw new BadRequestException('Валюта не поддерживается.');

    const period = isYearly ? 'yearly' : 'monthly';

    let totalPrice = 0;
    const subscriptionNames = [];

    for (const { type, subscription } of subscriptions) {
      const price = payment === 'stripe' ? subscription.price[period].eur : subscription.price[period].rub;
      totalPrice += price;
      subscriptionNames.push(subscription.name);
    }

    const description = `Оплата подписок: ${subscriptionNames.join(', ')} (${isYearly ? 'годовая' : 'месячная'}), пользователь ${user.firstName} ${user.lastName}`;

    const metadata = {
      module: 'subscriptions',
      value: subscriptionTypes.join(','),
      userId: user.id,
      autoRenew,
      isYearly,
    };

    if (payment === PaymentProviderType.STRIPE && autoRenew) {
      const priceIdOrLineItems = subscriptionTypes.length === 1
        ? (() => {
            const stripePriceId = subscriptions[0].subscription.stripePriceId[period];
            if (!stripePriceId) {
              throw new BadRequestException('Stripe priceId не настроен для подписки');
            }
            return stripePriceId;
          })()
        : subscriptions.map(({ subscription }) => {
            const stripePriceId = subscription.stripePriceId[period];
            if (!stripePriceId) {
              throw new BadRequestException(`Stripe priceId не настроен для подписки ${subscription.name}`);
            }
            return { price: stripePriceId, quantity: 1 };
          });

      return (this.stripeService as any).createSubscriptionCheckout(
        priceIdOrLineItems,
        user.email,
        description + ` (ID пользователя: ${metadata?.userId})`,
        metadata,
        returnUrl
      );
    }

    return provider.createPayment(totalPrice, currency, description +` (ID пользователя: ${metadata?.userId})`, metadata, returnUrl);
  }

  async onSubscriptionPaid(body: any) {
    // Stripe
    if (body?.type === 'checkout.session.completed' || body?.type === 'invoice.payment_succeeded') {
      const dataObj = body?.data?.object;
      const metadata = dataObj?.subscription_details?.metadata || dataObj?.metadata || dataObj?.payment_intent?.metadata;

      const userId = Number(metadata?.userId);
      const user = userId ? await User.findOne({ where: { id: userId }, relations: ['subscriptions', 'libraryPurchases', 'libraryFavourites', 'contentPurchases'] }) : null;

      let cardLast4 = null;
      try {
        const paymentIntentId = dataObj?.payment_intent;
        if (paymentIntentId) {
          const paymentIntent = await this.stripeService.getPaymentIntent(paymentIntentId);

          if (paymentIntent?.payment_method) {
            const paymentMethodId = typeof paymentIntent.payment_method === 'string'
              ? paymentIntent.payment_method
              : paymentIntent.payment_method.id;
            const paymentMethod = await this.stripeService.getPaymentMethod(paymentMethodId);
            cardLast4 = paymentMethod?.card?.last4;
          }
        }
      } catch (error) {
      }

      if(metadata?.type == 'library_purchase') {
        const library= await LibraryTranslation.findOneBy({id: metadata?.libraryId});
        if (user) {
          user.libraryPurchases.push(library);
          user.libraryFavourites.push(library);
        }

        if (userId) {
          await this.paymentHistoryService.createPaymentHistory({
            userId,
            status: PaymentStatus.SUCCESS,
            date: new Date(),
            paymentMethod: cardLast4 ? `**** ${cardLast4}` : 'Карта',
            details: `Покупка: ${library.title}`,
            amount: library.priceEur || 0,
            currency: 'EUR',
            provider: PaymentProvider.STRIPE,
            paymentId: dataObj?.id,
          });
        }

        return user ? await user.save() : { status: 'ok' };
      }

      if(metadata?.type == 'content_purchase') {
        const content = await Content.findOneBy({id: metadata?.contentId});
        if (user) {
          user.contentPurchases.push(content);
        }

        if (userId) {
          await this.paymentHistoryService.createPaymentHistory({
            userId,
            status: PaymentStatus.SUCCESS,
            date: new Date(),
            paymentMethod: cardLast4 ? `**** ${cardLast4}` : 'Карта',
            details: `Покупка: ${content.title}`,
            amount: content.priceEur || 0,
            currency: 'EUR',
            provider: PaymentProvider.STRIPE,
            paymentId: dataObj?.id,
          });
        }

        return user ? await user.save() : { status: 'ok' };
      }

      if(metadata?.module === 'donation') {
        const amount = dataObj?.amount_total ? dataObj.amount_total / 100 : 0;
        const description = dataObj?.description || (metadata?.autoRenew ? 'Ежемесячное пожертвование' : 'Пожертвование');

        await this.paymentHistoryService.createPaymentHistory({
          userId: userId || 0,
          status: PaymentStatus.SUCCESS,
          date: new Date(),
          paymentMethod: cardLast4 ? `**** ${cardLast4}` : 'Карта',
          details: description,
          amount: amount,
          currency: 'EUR',
          provider: PaymentProvider.STRIPE,
          paymentId: dataObj?.id,
        });

        return { status: 'ok' };
      }

      if(metadata?.autoRenew !== undefined && metadata?.module !== 'subscriptions') {
        const amount = dataObj?.amount_total ? dataObj.amount_total / 100 : 0;
        const description = dataObj?.description || (metadata?.autoRenew ? 'Ежемесячное пожертвование' : 'Пожертвование');

        if(userId) {
          await this.paymentHistoryService.createPaymentHistory({
            userId,
            status: PaymentStatus.SUCCESS,
            date: new Date(),
            paymentMethod: cardLast4 ? `**** ${cardLast4}` : 'Карта',
            details: description,
            amount: amount,
            currency: 'EUR',
            provider: PaymentProvider.STRIPE,
            paymentId: dataObj?.id,
          });
        }

        return { status: 'ok' };
      }

      if (!user || metadata?.module !== 'subscriptions') return { status: 'ok' };

      const isYearly = metadata?.isYearly === 'true';

      const subTypes = metadata.value.includes(',') ? metadata.value.split(',') : [metadata.value];

      for (const subType of subTypes) {
        let record = user.subscriptions.find(s => s.type === subType);

        if (!record) {
          record = new UserSubscriptions();
          record.type = subType;
          record.paymentId = dataObj?.id;
          record.isYearly = isYearly;
          user.subscriptions.push(record);
        }

        record.paymentId = dataObj?.id;
        record.provider = 'stripe';
        record.isAutoRenew = true;

        if (cardLast4) {
          record.cardNumber = cardLast4;
        }

        if (body?.type === 'invoice.payment_succeeded' && dataObj?.subscription) {
          record.stripeSubscriptionId = dataObj?.subscription;
          record.paymentId = dataObj?.payment_intent || dataObj?.id;
        }

        const periodDays = isYearly ? 365 : 30;
        record.isYearly = isYearly;
        record.currentPeriodEnd = new Date(Date.now() + periodDays * 24 * 60 * 60 * 1000);

        await record.save();
      }

      const subscriptionName = Subscriptions[metadata.value]?.name || 'Подписка';
      const amount = isYearly ? Subscriptions[metadata.value]?.price?.yearly?.eur : Subscriptions[metadata.value]?.price?.monthly?.eur;

      if(cardLast4) {
        await this.paymentHistoryService.createPaymentHistory({
          userId,
          status: PaymentStatus.SUCCESS,
          date: new Date(),
          paymentMethod: cardLast4 ? `**** ${cardLast4}` : 'Карта',
          details: subscriptionName,
          amount: amount || 0,
          currency: 'EUR',
          provider: PaymentProvider.STRIPE,
          paymentId: dataObj?.id,
        });
      }

      return await user.save();
    }

    // ЮKassa
    if (body?.event === 'payment.succeeded') {
      const dataObj = body?.object || body?.data?.object;
      const metadata = dataObj?.metadata;

      const userId = Number(metadata?.userId);
      const user = userId ? await User.findOne({ where: { id: userId }, relations: ['subscriptions', 'libraryPurchases', 'libraryFavourites', 'contentPurchases'] }) : null;

      if(metadata?.type == 'library_purchase') {
        const library= await LibraryTranslation.findOneBy({id: metadata?.libraryId});
        if (user) {
          user.libraryPurchases.push(library);
          user.libraryFavourites.push(library);
        }

        if (userId) {
          await this.paymentHistoryService.createPaymentHistory({
            userId,
            status: PaymentStatus.SUCCESS,
            date: new Date(),
            paymentMethod: dataObj?.payment_method?.card?.last4 ? `**** ${dataObj.payment_method.card.last4}` : 'Карта',
            details: `Покупка: ${library.title}`,
            amount: library.priceRub || 0,
            currency: 'RUB',
            provider: PaymentProvider.YOOKASSA,
            paymentId: dataObj?.id,
          });
        }

        return user ? await user.save() : { status: 'ok' };
      }

      if(metadata?.type == 'content_purchase') {
        const content = await Content.findOneBy({id: metadata?.contentId});
        if (user) {
          user.contentPurchases.push(content);
        }

        if (userId) {
          await this.paymentHistoryService.createPaymentHistory({
            userId,
            status: PaymentStatus.SUCCESS,
            date: new Date(),
            paymentMethod: dataObj?.payment_method?.card?.last4 ? `**** ${dataObj.payment_method.card.last4}` : 'Карта',
            details: `Покупка: ${content.title}`,
            amount: content.priceRub || 0,
            currency: 'RUB',
            provider: PaymentProvider.YOOKASSA,
            paymentId: dataObj?.id,
          });
        }

        return user ? await user.save() : { status: 'ok' };
      }

      if(metadata?.module === 'donation') {
        const amount = parseFloat(dataObj?.amount?.value) || 0;
        const description = dataObj?.description || (metadata?.autoRenew ? 'Ежемесячное пожертвование' : 'Пожертвование');

        await this.paymentHistoryService.createPaymentHistory({
          userId: userId || 0,
          status: PaymentStatus.SUCCESS,
          date: new Date(),
          paymentMethod: dataObj?.payment_method?.card?.last4 ? `**** ${dataObj.payment_method.card.last4}` : 'Карта',
          details: description,
          amount: amount,
          currency: 'RUB',
          provider: PaymentProvider.YOOKASSA,
          paymentId: dataObj?.id,
        });

        return { status: 'ok' };
      }

      if(metadata?.autoRenew !== undefined && metadata?.module !== 'subscriptions') {
        const amount = parseFloat(dataObj?.amount?.value) || 0;
        const description = dataObj?.description || (metadata?.autoRenew ? 'Ежемесячное пожертвование' : 'Пожертвование');

        if(userId) {
          await this.paymentHistoryService.createPaymentHistory({
            userId,
            status: PaymentStatus.SUCCESS,
            date: new Date(),
            paymentMethod: dataObj?.payment_method?.card?.last4 ? `**** ${dataObj.payment_method.card.last4}` : 'Карта',
            details: description,
            amount: amount,
            currency: 'RUB',
            provider: PaymentProvider.YOOKASSA,
            paymentId: dataObj?.id,
          });
        }

        return { status: 'ok' };
      }

      if (!user || metadata?.module !== 'subscriptions') return { status: 'ok' };

      const paymentId = dataObj?.id;
      if (await UserSubscriptions.findOneBy({ paymentId })) {
        return { status: 'ok' };
      }


      const subTypes = metadata.value.includes(',') ? metadata.value.split(',') : [metadata.value];
      const isYearly = metadata?.isYearly === 'true';
      const periodDays = isYearly ? 365 : 30;

      const pmId = dataObj?.payment_method?.id;
      const cardLast4 = dataObj?.payment_method?.card?.last4;

      for (const subType of subTypes) {
        const newSubscription = new UserSubscriptions();
        newSubscription.type = subType;
        newSubscription.paymentId = paymentId;
        newSubscription.provider = 'yookassa';
        newSubscription.isAutoRenew = true;
        newSubscription.isYearly = isYearly;
        newSubscription.currentPeriodEnd = new Date(Date.now() + periodDays * 24 * 60 * 60 * 1000);

        if (newSubscription.isAutoRenew && pmId) {
          newSubscription.yookassaPaymentMethodId = pmId;
        }

        if (cardLast4) {
          newSubscription.cardNumber = cardLast4;
        }

        await newSubscription.save();
        user.subscriptions.push(newSubscription);
      }

      const subscriptionName = Subscriptions[metadata.value]?.name || 'Подписка';
      const amount = isYearly ? Subscriptions[metadata.value]?.price?.yearly?.rub : Subscriptions[metadata.value]?.price?.monthly?.rub;

      await this.paymentHistoryService.createPaymentHistory({
        userId,
        status: PaymentStatus.SUCCESS,
        date: new Date(),
        paymentMethod: cardLast4 ? `**** ${cardLast4}` : 'Карта',
        details: subscriptionName,
        amount: amount || 0,
        currency: 'RUB',
        provider: PaymentProvider.YOOKASSA,
        paymentId: dataObj?.id,
      });

      return await user.save();
    }

    return { status: 'ignored' };
  }

  async cancelAutoRenew(subscriptionId: number, user: User) {
    const record = await UserSubscriptions.findOne({ where: { id: subscriptionId, user: { id: user.id } } });
    if (!record) throw new BadRequestException('Подписка не найдена');

    record.isAutoRenew = false;

    if (record.provider === 'stripe' && record.stripeSubscriptionId) {
      const stripe = this.providers.get(PaymentProviderType.STRIPE)
      await stripe.cancelAutoRenew(record.stripeSubscriptionId)
    }

    if (record.provider === 'yookassa' && record.yookassaPaymentMethodId) {
      const yookassa = this.providers.get(PaymentProviderType.YOOKASSA)
      //await yookassa.cancelAutoRenew(record.yookassaPaymentMethodId)
      record.yookassaPaymentMethodId = null;
    }

    await record.save();
    return { success: true };
  }

  async getPaymentMethod(paymentMethodId: string) {
    return await this.stripeService.getPaymentMethod(paymentMethodId);
  }

}
