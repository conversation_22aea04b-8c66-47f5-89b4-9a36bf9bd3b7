import { BadRequestException } from '@nestjs/common'
import { readFileSync, statSync } from 'fs'

export class FileValidator {
  private static readonly ALLOWED_IMAGE_TYPES = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml'
  ];

  private static readonly ALLOWED_IMAGE_EXTENSIONS = [
    '.jpg',
    '.jpeg',
    '.png',
    '.gif',
    '.webp',
    '.svg'
  ];

  private static readonly ALLOWED_JSON_EXTENSIONS = ['.json'];
  private static readonly ALLOWED_SQL_EXTENSIONS = ['.sql'];
  private static readonly ALLOWED_BACKUP_EXTENSIONS = ['.tar.gz', '.gz', '.tar'];

  private static readonly ALLOWED_MEDIA_EXTENSIONS = [
    '.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg',
    '.txt', '.pdf', '.epub', '.doc', '.docx',
    '.mp3', '.wav', '.ogg', '.m4a',
    '.mp4', '.avi', '.mkv', '.mov', '.webm'
  ];

  private static readonly MAX_FILE_SIZE = 10 * 1024 * 1024;
  private static readonly MAX_JSON_SIZE = 50 * 1024 * 1024;
  private static readonly MAX_SQL_SIZE = 100 * 1024 * 1024;
  private static readonly MAX_BACKUP_SIZE = 500 * 1024 * 1024;
  private static readonly MAX_MEDIA_SIZE = 200 * 1024 * 1024;

  private static readonly FILE_SIGNATURES: { [key: string]: number[][] } = {
    'image/jpeg': [[0xFF, 0xD8, 0xFF]],
    'image/png': [[0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]],
    'image/gif': [[0x47, 0x49, 0x46, 0x38]],
    'image/webp': [[0x52, 0x49, 0x46, 0x46]],
  };

  static validateImageFile(filePath: string, originalName: string, mimeType?: string): void {
    const ext = originalName.substring(originalName.lastIndexOf('.')).toLowerCase();

    if (!this.ALLOWED_IMAGE_EXTENSIONS.includes(ext)) {
      throw new BadRequestException(
        `Недопустимый тип файла. Разрешены только: ${this.ALLOWED_IMAGE_EXTENSIONS.join(', ')}`
      );
    }

    try {
      const stats = statSync(filePath);
      
      if (stats.size > this.MAX_FILE_SIZE) {
        throw new BadRequestException(
          `Размер файла превышает максимально допустимый (${this.MAX_FILE_SIZE / 1024 / 1024}MB)`
        );
      }

      if (stats.size === 0) {
        throw new BadRequestException('Файл пустой');
      }

      if (ext !== '.svg') {
        this.validateFileSignature(filePath, mimeType);
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Ошибка при проверке файла');
    }
  }

  private static validateFileSignature(filePath: string, mimeType?: string): void {
    try {
      const buffer = readFileSync(filePath);
      const header = Array.from(buffer.slice(0, 12));

      let isValid = false;

      for (const [type, signatures] of Object.entries(this.FILE_SIGNATURES)) {
        for (const signature of signatures) {
          if (this.matchesSignature(header, signature)) {
            isValid = true;
            break;
          }
        }
        if (isValid) break;
      }

      if (!isValid) {
        throw new BadRequestException('Файл не является допустимым изображением');
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Ошибка при проверке содержимого файла');
    }
  }

  private static matchesSignature(header: number[], signature: number[]): boolean {
    return signature.every((byte, index) => header[index] === byte);
  }

  static validateJsonFile(filePath: string, originalName: string): void {
    const ext = originalName.substring(originalName.lastIndexOf('.')).toLowerCase();

    if (!this.ALLOWED_JSON_EXTENSIONS.includes(ext)) {
      throw new BadRequestException('Разрешены только JSON файлы');
    }

    try {
      const stats = statSync(filePath);

      if (stats.size > this.MAX_JSON_SIZE) {
        throw new BadRequestException(
          `Размер файла превышает максимально допустимый (${this.MAX_JSON_SIZE / 1024 / 1024}MB)`
        );
      }

      if (stats.size === 0) {
        throw new BadRequestException('Файл пустой');
      }

      const content = readFileSync(filePath, 'utf8');
      JSON.parse(content);
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      if (error instanceof SyntaxError) {
        throw new BadRequestException('Файл не является валидным JSON');
      }
      throw new BadRequestException('Ошибка при проверке JSON файла');
    }
  }

  static validateJsonBuffer(buffer: Buffer, originalName: string): void {
    const ext = originalName.substring(originalName.lastIndexOf('.')).toLowerCase();

    if (!this.ALLOWED_JSON_EXTENSIONS.includes(ext)) {
      throw new BadRequestException('Разрешены только JSON файлы');
    }

    if (buffer.length > this.MAX_JSON_SIZE) {
      throw new BadRequestException(
        `Размер файла превышает максимально допустимый (${this.MAX_JSON_SIZE / 1024 / 1024}MB)`
      );
    }

    if (buffer.length === 0) {
      throw new BadRequestException('Файл пустой');
    }

    try {
      const content = buffer.toString('utf8');
      JSON.parse(content);
    } catch (error) {
      if (error instanceof SyntaxError) {
        throw new BadRequestException('Файл не является валидным JSON');
      }
      throw new BadRequestException('Ошибка при проверке JSON файла');
    }
  }

  static validateSqlFile(filePath: string, originalName: string): void {
    const ext = originalName.substring(originalName.lastIndexOf('.')).toLowerCase();

    if (!this.ALLOWED_SQL_EXTENSIONS.includes(ext)) {
      throw new BadRequestException('Разрешены только SQL файлы');
    }

    try {
      const stats = statSync(filePath);

      if (stats.size > this.MAX_SQL_SIZE) {
        throw new BadRequestException(
          `Размер файла превышает максимально допустимый (${this.MAX_SQL_SIZE / 1024 / 1024}MB)`
        );
      }

      if (stats.size === 0) {
        throw new BadRequestException('Файл пустой');
      }

      const content = readFileSync(filePath, 'utf8');

      const dangerousPatterns = [
        /DROP\s+DATABASE/i,
        /TRUNCATE\s+TABLE/i,
        /;\s*DROP/i,
      ];

      for (const pattern of dangerousPatterns) {
        if (pattern.test(content)) {
          throw new BadRequestException('SQL файл содержит потенциально опасные команды');
        }
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Ошибка при проверке SQL файла');
    }
  }

  static validateMediaFile(filePath: string, originalName: string): void {
    const ext = originalName.substring(originalName.lastIndexOf('.')).toLowerCase();

    if (!this.ALLOWED_MEDIA_EXTENSIONS.includes(ext)) {
      throw new BadRequestException(
        `Недопустимый тип файла. Разрешены: изображения, документы (txt, pdf, epub, doc, docx), аудио (mp3, wav, ogg, m4a), видео (mp4, avi, mkv, mov, webm)`
      );
    }

    try {
      const stats = statSync(filePath);

      if (stats.size > this.MAX_MEDIA_SIZE) {
        throw new BadRequestException(
          `Размер файла превышает максимально допустимый (${this.MAX_MEDIA_SIZE / 1024 / 1024}MB)`
        );
      }

      if (stats.size === 0) {
        throw new BadRequestException('Файл пустой');
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Ошибка при проверке файла');
    }
  }

  static validateBackupFile(buffer: Buffer, originalName: string): void {
    const fileName = originalName.toLowerCase();

    const isValidExtension = this.ALLOWED_BACKUP_EXTENSIONS.some(ext => fileName.endsWith(ext));

    if (!isValidExtension) {
      throw new BadRequestException('Разрешены только tar.gz, gz или tar файлы');
    }

    if (buffer.length > this.MAX_BACKUP_SIZE) {
      throw new BadRequestException(
        `Размер файла превышает максимально допустимый (${this.MAX_BACKUP_SIZE / 1024 / 1024}MB)`
      );
    }

    if (buffer.length === 0) {
      throw new BadRequestException('Файл пустой');
    }

    const header = Array.from(buffer.subarray(0, 3));
    const isGzip = header[0] === 0x1F && header[1] === 0x8B && header[2] === 0x08;

    if (!isGzip && !fileName.endsWith('.tar')) {
      throw new BadRequestException('Файл не является валидным архивом');
    }
  }

  static sanitizeFileName(fileName: string): string {
    return fileName
      .replace(/[^a-zA-Z0-9._-]/g, '_')
      .replace(/\.{2,}/g, '.')
      .substring(0, 255);
  }
}

