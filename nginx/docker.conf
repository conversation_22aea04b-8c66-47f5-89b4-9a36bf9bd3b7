server {
    listen 80;
    server_name sanatanadharma.world;
    charset utf-8;
    client_max_body_size 256M;

    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log error;

    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    gzip on;
    gzip_disable "MSIE [1-6]\.(?!.*SV1)";
    gzip_proxied any;
    gzip_buffers 16 8k;
    gzip_types image/svg+xml text/plain text/html text/xml text/css text/javascript application/javascript application/json font/ttf font/otf font/eot;
    gzip_vary on;
    gzip_comp_level 6;

    location /robots.txt {
        alias /app/server/robots.txt;
        allow all;
        log_not_found off;
    }

    location /favicon.ico {
        alias /app/client/public/favicon.ico;
        access_log off;
        log_not_found off;
    }

    location /.well-known {
        alias /app/.well-known;
        try_files $uri $uri/ =404;
    }

    location /media {
        alias /app/client/dist/browser/media;
        expires 3M;
        access_log off;
        add_header Cache-Control "max-age=2629746, public";
    }

    location /upload {
        alias /app/server/upload;
        add_header Access-Control-Allow-Origin *;
        access_log off;
        add_header Cache-Control "max-age=2629746, public";
    }

    location /upload/epub/images {
        alias /app/server/upload/epub/images;
    }

    location ~ ^/(api|sitemap.*\.xml) {
        proxy_pass http://server:9090;
    }

    location = /admin {
        return 301 /admin/;
    }

    location ^~ /admin/ {
        alias /app/admin/dist/admin/browser/;
        index index.html;
        try_files $uri $uri/ /admin/index.html;
    }

    location / {
        proxy_pass http://client:9091;
    }
}

server {
    listen 80;
    server_name dev.sanatanadharma.world;
    charset utf-8;
    client_max_body_size 256M;

    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log error;

    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    gzip on;
    gzip_disable "MSIE [1-6]\.(?!.*SV1)";
    gzip_proxied any;
    gzip_buffers 16 8k;
    gzip_types image/svg+xml text/plain text/html text/xml text/css text/javascript application/javascript application/json font/ttf font/otf font/eot;
    gzip_vary on;
    gzip_comp_level 6;

    location /robots.txt {
        alias /app/server/robots.txt;
        allow all;
        log_not_found off;
    }

    location /favicon.ico {
        alias /app/client/public/favicon.ico;
        access_log off;
        log_not_found off;
    }

    location /.well-known {
        alias /app/.well-known;
        try_files $uri $uri/ =404;
    }

    location /media {
        alias /app/client/dist/browser/media;
        expires 3M;
        access_log off;
        add_header Cache-Control "max-age=2629746, public";
    }

    location /upload {
        alias /app/server/upload;
        add_header Access-Control-Allow-Origin *;
        access_log off;
        add_header Cache-Control "max-age=2629746, public";
    }

    location /upload/epub/images {
        alias /app/server/upload/epub/images;
    }

    location ~ ^/(api|sitemap.*\.xml) {
        proxy_pass http://server:9015;
    }

    location = /admin {
        return 301 /admin/;
    }

    location ^~ /admin/ {
        alias /app/admin/dist/admin/browser/;
        index index.html;
        try_files $uri $uri/ /admin/index.html;
        add_header X-Served-By admin-dev always;
    }

    location / {
        proxy_pass http://client:9019;
    }
}
