import {HttpClient} from "@angular/common/http";
import {inject, Injectable, signal} from '@angular/core';
import {tap} from "rxjs";

@Injectable({
  providedIn: 'root'
})
export class SenderTemplateService {
  http = inject(HttpClient);
  list = signal<any[]>([]);

  getAll() {
    return this.http.get('/admin/sender-template').pipe(
      tap((res: any) => this.list.set(res))
    );
  }

  getOne(id: number) {
    return this.http.get(`/admin/sender-template/${id}`);
  }

  create(form: any) {
    return this.http.post('/admin/sender-template', form);
  }

  update(id: number, form: any) {
    return this.http.patch(`/admin/sender-template/${id}`, form);
  }

  delete(id: number) {
    return this.http.delete(`/admin/sender-template/${id}`).pipe(
      tap(() => this.getAll().subscribe())
    );
  }
}

