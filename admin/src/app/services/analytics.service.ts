import { HttpClient } from '@angular/common/http'
import { inject, Injectable } from '@angular/core'

@Injectable({
    providedIn: 'root'
})
export class AnalyticsService {
    http = inject(HttpClient)

    getDashboardStats(period: string = 'all') {
        return this.http.get(`/admin/analytics/dashboard`, {
            params: { period }
        })
    }

    getActivityLogs(page: number = 1, limit: number = 50) {
        return this.http.get(`/admin/analytics/logs`, {
            params: { page: page.toString(), limit: limit.toString() }
        })
    }
}

