import { HttpClient } from "@angular/common/http";
import { inject, Injectable, signal } from '@angular/core';
import { tap } from "rxjs";

@Injectable({
  providedIn: 'root'
})
export class ContentService {
  http = inject(HttpClient)
  tree = signal<{id: number, title: string, contents: {id: number, title: string, slug: string}[]}[]>([])
  categories = signal<{id: number, title: string}[]>([])

  getAll() {
    return this.http.get('/admin/content')
  }

  getTree() {
    return this.http.get('/admin/content/tree').subscribe((res: any) => {
      this.tree.set(res)
    })
  }

  getCategory(id: number) {
    return this.http.get('/admin/content/category/' + id)
  }

  getCategories() {
    return this.http.get('/admin/content/category/list').subscribe((res: any) => this.categories.set(res))
  }

  addContent(form: any) {
    return this.http.post('/admin/content/add', form)
  }

  addCategory(form: any) {
    return this.http.post('/admin/content/category/add', form)
  }

  getBySlug(slug: string) {
    return this.http.get(`/admin/content/${slug}`)
  }

  update(slug: string, form: any) {
    return this.http.patch(`/admin/content/${slug}`, form)
  }

  delete(slug: string, replace: any) {
    return this.http.delete(`/admin/content/${slug}`, {params: {replace}})
  }

  searchPages(query: string) {
    return this.http.get(`/admin/content/search?query=${query}`)
  }

  deleteCategory(categoryId: number) {
    return this.http.delete(`/admin/content/category/${categoryId}`).pipe(
      tap(() => this.getTree())
    )
  }

  getLinkedContent(slug: string) {
    return this.http.get(`/admin/content/link`, {params: {slug}})
  }

  updateCategoryOrder(orderData: { id: number, order: number }[]) {
    return this.http.post(`/admin/content/category/order`, { categories: orderData });
  }

  copyToProd(slug: string) {
    return this.http.post(`/admin/content/${slug}/copy-to-prod`, {})
  }
}
