import {HttpClient} from "@angular/common/http";
import {inject, Injectable, signal} from '@angular/core';
import {tap} from "rxjs";

@Injectable({
  providedIn: 'root'
})
export class SenderService {
  http = inject(HttpClient);
  list = signal<any[]>([]);

  getAll() {
    return this.http.get('/admin/sender').pipe(
      tap((res: any) => this.list.set(res))
    );
  }

  getOne(id: number) {
    return this.http.get(`/admin/sender/${id}`);
  }

  create(form: any) {
    return this.http.post('/admin/sender', form);
  }

  update(id: number, form: any) {
    return this.http.patch(`/admin/sender/${id}`, form);
  }

  delete(id: number) {
    return this.http.delete(`/admin/sender/${id}`).pipe(
      tap(() => this.getAll().subscribe())
    );
  }

  send(id: number) {
    return this.http.post(`/admin/sender/${id}/send`, {});
  }
}

