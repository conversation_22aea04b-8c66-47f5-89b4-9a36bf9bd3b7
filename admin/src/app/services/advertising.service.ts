import { HttpClient } from "@angular/common/http"
import { inject, Injectable, signal } from '@angular/core'
import { tap } from "rxjs"

@Injectable({
  providedIn: 'root'
})
export class AdvertisingService {
  http = inject(HttpClient)
  list = signal<any[]>([])

  getAll() {
    return this.http.get('/admin/news').pipe(
      tap((res: any) => this.list.set(res))
    )
  }

  getBySlug(slug: string) {
    return this.http.get(`/admin/news/${slug}`)
  }

  create(form: any) {
    return this.http.post('/admin/news', form)
  }

  update(slug: string, form: any) {
    return this.http.patch(`/admin/news/${slug}`, form)
  }

  delete(slug: string) {
    return this.http.delete(`/admin/news/${slug}`).pipe(
      tap(() => this.getAll().subscribe())
    )
  }
}
