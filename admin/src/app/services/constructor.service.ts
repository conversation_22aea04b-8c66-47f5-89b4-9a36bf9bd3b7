import { HttpClient } from "@angular/common/http";
import { inject, Injectable } from '@angular/core';
import { tap } from "rxjs";

@Injectable({
  providedIn: 'root'
})
export class ConstructorService {
  http = inject(HttpClient)
  items: any = []

  get(id: number) {
    return this.http.get('/admin/constructor/' + id)
  }

  getAll() {
    return this.http.get('/admin/constructor').pipe(
      tap((res: any) => this.items = res),
    )
  }

  getBySlug(slug: string) {
    return this.http.get('/admin/constructor/slug/' + slug)
  }

  create(items: any) {
    return this.http.post('/admin/constructor/create', items)
  }

  update(slug: string, items: any) {
    return this.http.patch('/admin/constructor/' + slug, items)
  }

  deleteBlock(slug: string) {
    return this.http.delete(`/admin/constructor/${slug}`).pipe(
      tap(() => this.getAll().subscribe())
    )
  }
}
