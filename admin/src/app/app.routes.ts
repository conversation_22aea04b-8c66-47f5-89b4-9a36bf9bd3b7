import { authGuard } from "@/guards/auth.guard"
import { AccessDeniedComponent } from "@/pages/access-denied/access-denied.component"
import { AdvertisingAddComponent } from "@/pages/advertising/advertising-add/advertising-add.component"
import { AdvertisingComponent } from "@/pages/advertising/advertising.component"
import { AudioComponent } from "@/pages/audio/audio.component"
import { AudiofilesComponent } from "@/pages/audiofiles/audiofiles.component"
import { BackupsComponent } from "@/pages/backups/backups.component"
import { ConstructorAddComponent } from "@/pages/constructor/constructor-add/constructor-add.component"
import { ConstructorComponent } from "@/pages/constructor/constructor.component"
import { CategoryAddComponent } from "@/pages/content/category-add/category-add.component"
import { ContentAddComponent } from "@/pages/content/content-add/content-add.component"
import { ContentComponent } from "@/pages/content/content.component"
import { DashboardComponent } from "@/pages/dashboard/dashboard.component"
import { ForumCategoriesAddComponent } from "@/pages/forum/forum-categories-add/forum-categories-add.component"
import { ForumCategoriesComponent } from "@/pages/forum/forum-categories/forum-categories.component"
import { LibraryAddComponent } from "@/pages/library/library-add/library-add.component"
import { LibraryComponent } from "@/pages/library/library.component"
import { MypageAddComponent } from "@/pages/mypage/mypage-add/mypage-add.component"
import { MypageComponent } from "@/pages/mypage/mypage.component"
import { PhotoAddComponent } from "@/pages/photo/photo-add/photo-add.component"
import { PhotoComponent } from "@/pages/photo/photo.component"
import { SenderAddComponent } from "@/pages/sender/sender-add/sender-add.component"
import { SenderTemplateAddComponent } from "@/pages/sender/sender-template-add/sender-template-add.component"
import { SenderTemplateComponent } from "@/pages/sender/sender-template/sender-template.component"
import { SenderComponent } from "@/pages/sender/sender.component"
import { TranslationsAddComponent } from "@/pages/translations/translations-add/translations-add.component"
import { TranslationsComponent } from "@/pages/translations/translations.component"
import { UserComponent } from "@/pages/users/user/user.component"
import { UsersComponent } from "@/pages/users/users.component"
import { Routes } from '@angular/router'
import { LayoutComponent } from "./components/layout/layout.component"
import { accessGuard } from './guards/access.guard'
import { LoginComponent } from './pages/login/login.component'

export const routes: Routes = [
    {
      path: '',
      component: LayoutComponent,
      canActivate: [authGuard],
      canActivateChild: [accessGuard],
      children: [
        {
          path: '',
          component: DashboardComponent,
          data: {
            groups: ['ADMIN']
          }
        },
        {
          path: 'dashboard',
          component: DashboardComponent,
          data: {
            groups: ['ADMIN']
          }
        },
        {
          path: 'content',
          component: ContentComponent,
          data: {
            groups: ['PAGE_MODERATOR']
          }
        },
        {
          path: 'content/add',
          component: ContentAddComponent,
          data: {
            groups: ['PAGE_MODERATOR']
          }
        },
        {
          path: 'content/:slug',
          component: ContentAddComponent,
          data: {
            groups: ['PAGE_MODERATOR']
          }
        },
        {
          path: 'category/:id',
          component: CategoryAddComponent,
          data: {
            groups: ['PAGE_MODERATOR']
          }
        },
        {
          path: 'translations',
          component: TranslationsComponent,
          data: {
            groups: ['TRANSLATION_MODERATOR']
          }
        },
        {
          path: 'translations/add',
          component: TranslationsAddComponent,
          data: {
            groups: ['TRANSLATION_MODERATOR']
          }
        },
        {
          path: 'translations/:id',
          component: TranslationsAddComponent,
          data: {
            groups: ['TRANSLATION_MODERATOR']
          }
        },
        {
          path: 'library',
          component: LibraryComponent,
          data: {
            groups: ['LIBRARY_MODERATOR']
          }
        },
        {
          path: 'library/add',
          component: LibraryAddComponent,
          data: {
            groups: ['LIBRARY_MODERATOR']
          }
        },
        {
          path: 'library/:id',
          component: LibraryAddComponent,
          data: {
            groups: ['LIBRARY_MODERATOR']
          }
        },
        {
          path: 'photo',
          component: PhotoComponent,
          data: {
            groups: ['PHOTO_MODERATOR']
          }
        },
        {
          path: 'photo/add',
          component: PhotoAddComponent,
          data: {
            groups: ['PHOTO_MODERATOR']
          }
        },
        {
          path: 'photo/:id',
          component: PhotoAddComponent,
          data: {
            groups: ['PHOTO_MODERATOR']
          }
        },
        {
          path: 'audio',
          component: AudioComponent,
          data: {
            groups: ['LECTURE_MODERATOR']
          }
        },
        {
          path: 'audio/:page',
          component: AudioComponent,
          data: {
            groups: ['LECTURE_MODERATOR']
          }
        },
        {
          path: 'constructor',
          component: ConstructorComponent,
          data: {
            groups: ['CONSTRUCTOR_MODERATOR']
          }
        },
        {
          path: 'constructor/add',
          component: ConstructorAddComponent,
          data: {
            groups: ['CONSTRUCTOR_MODERATOR']
          }
        },
        {
          path: 'constructor/:slug',
          component: ConstructorAddComponent,
          data: {
            groups: ['CONSTRUCTOR_MODERATOR']
          }
        },
        {
          path: 'users',
          component: UsersComponent,
          data: {
            groups: ['USER_MODERATOR']
          }
        },
        {
          path: 'mypage',
          component: MypageComponent,
          data: {
            groups: ['PERSONALPAGE_MODERATOR']
          }
        },
        {
          path: 'mypage/:id',
          component: MypageAddComponent,
          data: {
            groups: ['PERSONALPAGE_MODERATOR']
          }
        },
        {
          path: 'forum/categories',
          component: ForumCategoriesComponent,
          data: {
            groups: ['FORUM_MODERATOR']
          }
        },
        {
          path: 'forum/categories/add',
          component: ForumCategoriesAddComponent,
          data: {
            groups: ['FORUM_MODERATOR']
          }
        },
        {
          path: 'forum/categories/:id',
          component: ForumCategoriesAddComponent,
          data: {
            groups: ['FORUM_MODERATOR']
          }
        },
        {
          path: 'news',
          component: AdvertisingComponent,
          data: {
            groups: ['CALENDAR_MODERATOR']
          }
        },
        {
          path: 'news/add',
          component: AdvertisingAddComponent,
          data: {
            groups: ['CALENDAR_MODERATOR']
          }
        },
        {
          path: 'news/:slug',
          component: AdvertisingAddComponent,
          data: {
            groups: ['CALENDAR_MODERATOR']
          }
        },
        {
          path: 'users/:id',
          component: UserComponent,
          data: {
            groups: ['USER_MODERATOR']
          }
        },
        {
          path: 'audiofiles',
          component: AudiofilesComponent,
          data: {
            groups: ['AUDIO_MODERATOR']
          }
        },
        {
          path: 'backups',
          component: BackupsComponent,
          data: {
            groups: ['ADMIN']
          }
        },
        {
          path: 'sender',
          component: SenderComponent,
          data: {
            groups: ['ADMIN']
          }
        },
        {
          path: 'sender/add',
          component: SenderAddComponent,
          data: {
            groups: ['ADMIN']
          }
        },
        {
          path: 'sender/:id',
          component: SenderAddComponent,
          data: {
            groups: ['ADMIN']
          }
        },
        {
          path: 'sender-template',
          component: SenderTemplateComponent,
          data: {
            groups: ['ADMIN']
          }
        },
        {
          path: 'sender-template/add',
          component: SenderTemplateAddComponent,
          data: {
            groups: ['ADMIN']
          }
        },
        {
          path: 'sender-template/:id',
          component: SenderTemplateAddComponent,
          data: {
            groups: ['ADMIN']
          }
        }
      ]
    },
    {
      path: 'login',
      component: LoginComponent,
    },
    {
      path: 'access-denied',
      component: AccessDeniedComponent,
    },
    {
      path: '**',
      redirectTo: '/'
    }
];
