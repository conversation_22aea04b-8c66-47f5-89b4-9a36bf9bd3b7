import { GroupsEnum, UserService } from "@/services/user.service";
import { IconModule } from "@/shared/icon/icon.module";
import { CommonModule } from "@angular/common";
import { Component, inject, OnDestroy, OnInit } from '@angular/core';
import { NavigationEnd, Router, RouterLink } from "@angular/router";
import { NgScrollbar } from 'ngx-scrollbar';
import { Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';

@Component({
    selector: 'sidebar',
    imports: [NgScrollbar, IconModule, RouterLink, CommonModule],
    templateUrl: './sidebar.component.html',
    styleUrl: './sidebar.component.scss'
})
export class SidebarComponent implements OnInit, OnDestroy {
  router = inject(Router);
  userService = inject(UserService);
  activeDropdown: string[] = [];
  public GroupsEnum = GroupsEnum;
  private routerSubscription?: Subscription;

  ngOnInit() {
    if (this.isForumActive()) {
      this.activeDropdown.push('forum');
    }
    if (this.isSenderActive()) {
      this.activeDropdown.push('sender');
    }

    this.routerSubscription = this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        if (this.isForumActive() && !this.activeDropdown.includes('forum')) {
          this.activeDropdown.push('forum');
        } else if (!this.isForumActive() && this.activeDropdown.includes('forum')) {
          this.activeDropdown = this.activeDropdown.filter(d => d !== 'forum');
        }

        if (this.isSenderActive() && !this.activeDropdown.includes('sender')) {
          this.activeDropdown.push('sender');
        } else if (!this.isSenderActive() && this.activeDropdown.includes('sender')) {
          this.activeDropdown = this.activeDropdown.filter(d => d !== 'sender');
        }
      });
  }

  ngOnDestroy() {
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
  }

  isActiveRoute(route: string): boolean {
    const currentUrl = this.router.url;

    // Убираем query параметры и фрагменты
    const cleanUrl = currentUrl.split('?')[0].split('#')[0];

    // Проверяем точное совпадение или начало пути
    return cleanUrl === `/${route}` || cleanUrl.startsWith(`/${route}/`);
  }

  isForumActive(): boolean {
    return this.router.url.startsWith('/forum');
  }

  isSenderActive(): boolean {
    return this.router.url.startsWith('/sender');
  }

  toggleAccordion(name: string, parent?: string) {
    if (this.activeDropdown.includes(name)) {
      this.activeDropdown = this.activeDropdown.filter((d) => d !== name);
    } else {
      this.activeDropdown.push(name);
    }
  }
}
