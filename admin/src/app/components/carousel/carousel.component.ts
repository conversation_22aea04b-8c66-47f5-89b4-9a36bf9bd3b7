import { PhotopreviewComponent } from "@/components/photopreview/photopreview.component"
import { FileService } from "@/services/file.service"
import { CommonModule } from "@angular/common"
import { Component, ElementRef, EventEmitter, inject, Input, Output, ViewChild } from '@angular/core'
import { FormArray, FormBuilder, ReactiveFormsModule, Validators } from "@angular/forms"

@Component({
    selector: 'carousel',
    imports: [
        ReactiveFormsModule,
        CommonModule,
        PhotopreviewComponent,
    ],
    templateUrl: './carousel.component.html',
    styleUrl: './carousel.component.scss'
})
export class CarouselComponent {
  @ViewChild('carouselFormDialog') carouselFormDialog!: ElementRef<HTMLDialogElement>;
  @ViewChild('carouselItemFormDialog') carouselItemFormDialog!: ElementRef<HTMLDialogElement>;
  @ViewChild('carouselItemImage') carouselItemImage!: ElementRef<HTMLInputElement>;
  @Output() onItemAdded: EventEmitter<any> = new EventEmitter();
  @Output() onItemEdited: EventEmitter<any> = new EventEmitter();
  @Input('folder') folder: string = 'constructor'
  fb = inject(FormBuilder)
  fileService = inject(FileService)
  carouselForm: any = this.fb.group({
    index: null,
    title: [null, Validators.required],
    size: [null, Validators.required],
    type: 'carousel',
    link: [null],
    items: this.fb.array([]),
  })
  carouselFormItem: any = this.fb.group({
    index: null,
    title: [null, Validators.required],
    tag: [null],
    link: null,
    date: [null],
    text: [null],
    image: [null, Validators.required],
    show: true
  })
  carouselSizes = [
    { title: 'Карусель 1', value: 'carousel1'},
    { title: 'Карусель 2', value: 'carousel2'},
    { title: 'Карусель 3', value: 'carousel3'},
    { title: 'Карусель 4', value: 'carousel4'},
  ]

  showCarouselForm(reset = false) {
    this.carouselFormDialog.nativeElement.showModal()
    if(reset) {
      (this.carouselForm.get('items') as FormArray).clear()
      this.carouselForm.reset()
      this.carouselForm.patchValue({type: 'carousel'})
      this.carouselFormItem.reset()
      this.carouselFormItem.get('show').patchValue(true)
    }
  }

  showCarouselItemForm() {
    this.carouselItemImage.nativeElement.value = ''
    this.carouselItemFormDialog.nativeElement.showModal()
  }

  closeCarouselForm() {
    this.carouselFormDialog.nativeElement.close()
    this.carouselFormItem.reset()
    this.carouselForm.reset()
    this.carouselFormItem.get('show').patchValue(true)
  }

  closeCarouselItemForm() {
    this.carouselItemFormDialog.nativeElement.close()
    this.carouselFormItem.reset()
    this.carouselFormItem.get('show').patchValue(true)
  }

  addCarouselItem() {
    const itemsArray = this.carouselForm.get('items') as FormArray;

    const newItem = this.fb.group({
      index: null,
      title: this.carouselFormItem.get('title')?.value,
      tag: this.carouselFormItem.get('tag')?.value,
      link: this.carouselFormItem.get('link')?.value,
      date: this.carouselFormItem.get('date')?.value,
      text: this.carouselFormItem.get('text')?.value,
      image: this.carouselFormItem.get('image')?.value,
      show: this.carouselFormItem.get('show')?.value,
    });

    if (this.carouselFormItem.get('index')?.value !== null) {
      itemsArray.at(this.carouselFormItem.get('index')!.value).patchValue(newItem.value);
    } else {
      itemsArray.push(newItem);
    }

    this.closeCarouselItemForm();
  }

  uploadFile(e: Event) {
    const target = e.target as HTMLInputElement;
    if(!target.files) return

    this.fileService.uploadToTempFolder(target.files).subscribe((res: any) => {
      this.carouselFormItem.controls.image.patchValue(res[0])
    })
  }

  saveCarouselForm(): void {
    if (this.carouselForm.value.index !== null) {
      this.onItemEdited.emit({index: this.carouselForm.value.index, form: this.carouselForm});
    } else {
      this.onItemAdded.emit(this.carouselForm);
    }

    this.closeCarouselForm();
  }

  editCarouselItem(index: number, carousel: any) {
    carousel.index = index
    this.carouselFormItem.patchValue(carousel)
    this.showCarouselItemForm()
  }

  removeCarouselItem(index: number) {
    const itemsArray = this.carouselForm.get('items') as FormArray;
    itemsArray.removeAt(index);
  }

  onPreviewRemoved() {
    this.carouselFormItem.patchValue({image: null})
  }

  getPreviewClass(sizeValue: string): string {
    if (!sizeValue) return 'carousel-preview-placeholder';

    const validSizes = ['carousel1', 'carousel2', 'carousel3', 'carousel4'];
    if (validSizes.includes(sizeValue)) {
      return `carousel-preview-${sizeValue}`;
    }

    return 'carousel-preview-placeholder';
  }
}
