<div class="admin-component">
  <div class="admin-page-header">
    <div>
      <h1 class="admin-page-title">{{id ? 'Редактировать шаблон' : 'Создать шаблон'}}</h1>
    </div>
    <div class="admin-actions">
      <button type="button" class="btn btn-secondary" (click)="back()">Назад</button>
      <button type="button" class="btn btn-primary" (click)="onSubmit()">Сохранить</button>
    </div>
  </div>

  <div class="admin-content-wrapper">
    <form [formGroup]="form">
      <div>
        <label>Название шаблона</label>
        <input type="text" class="form-input" formControlName="title" placeholder="Введите название шаблона">
      </div>

      <div>
        <label>Текст письма (HTML)</label>
        <ckeditor formControlName="htmlContent" [editor]="Editor" [config]="editorConfig" *ngIf="isLayoutReady" (ready)="onEditorReady($event)" />
      </div>
    </form>
  </div>
</div>

