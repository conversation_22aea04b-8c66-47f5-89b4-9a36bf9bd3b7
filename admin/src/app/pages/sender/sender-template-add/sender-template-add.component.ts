import editorConfig from "@/editor.config"
import { CustomUploadAdapter } from "@/pages/content/content-add/custom-upload-adapter"
import { SenderTemplateService } from "@/services/sender-template.service"
import { ToasterService } from "@/services/toaster.service"
import { CommonModule, Location } from "@angular/common"
import { Component, inject, ViewEncapsulation } from '@angular/core'
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from "@angular/forms"
import { ActivatedRoute, Router } from "@angular/router"
import { CKEditorModule } from "@ckeditor/ckeditor5-angular"
import { ClassicEditor } from 'ckeditor5'
import { environment } from "../../../../environments/environment"

@Component({
  selector: 'app-sender-template-add',
  imports: [CommonModule, ReactiveFormsModule, FormsModule, CKEditorModule],
  templateUrl: './sender-template-add.component.html',
  styleUrl: './sender-template-add.component.scss',
  encapsulation: ViewEncapsulation.None
})
export class SenderTemplateAddComponent {
  fb = inject(FormBuilder);
  route = inject(ActivatedRoute);
  router = inject(Router);
  location = inject(Location);
  senderTemplateService = inject(SenderTemplateService);
  toasterService = inject(ToasterService);

  id: number | null = null;
  Editor = ClassicEditor;
  editorConfig: any = {};
  editorInstance: any;
  isLayoutReady = false;

  form = this.fb.group({
    title: ['', Validators.required],
    htmlContent: ['']
  });

  ngOnInit() {
    this.editorConfig = editorConfig;
    this.isLayoutReady = true;

    const id = this.route.snapshot.params['id'];
    if (id && id !== 'add') {
      this.id = +id;
      this.senderTemplateService.getOne(this.id).subscribe((template: any) => {
        this.form.patchValue({
          title: template.title,
          htmlContent: template.htmlContent
        });
      });
    }
  }

  onEditorReady(editor: any) {
    this.editorInstance = editor;
    editor.plugins.get('FileRepository').createUploadAdapter = (loader: any) => {
      return new CustomUploadAdapter(loader, environment.apiUrl);
    };
  }

  onSubmit() {
    if (this.form.invalid) {
      this.toasterService.showError('Заполните обязательные поля');
      return;
    }

    const data = this.form.value;

    if (this.id) {
      this.senderTemplateService.update(this.id, data).subscribe({
        next: () => {
          this.toasterService.showSuccess('Шаблон успешно обновлен');
          this.back();
        },
        error: () => {
          this.toasterService.showError('Ошибка обновления');
        }
      });
    } else {
      this.senderTemplateService.create(data).subscribe({
        next: (res: any) => {
          this.toasterService.showSuccess('Шаблон успешно создан');
          this.router.navigate(['/sender-template/' + res.id]);
        },
        error: () => {
          this.toasterService.showError('Ошибка создания');
        }
      });
    }
  }

  back() {
    this.location.back();
  }
}

