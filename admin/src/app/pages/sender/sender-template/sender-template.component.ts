import { AdminDialogComponent } from "@/components/admin-dialog/admin-dialog.component"
import { SenderTemplateService } from "@/services/sender-template.service"
import { ToasterService } from "@/services/toaster.service"
import { CommonModule } from "@angular/common"
import { Component, inject, ViewChild } from '@angular/core'
import { Router, RouterLink } from "@angular/router"
import moment from "moment"

@Component({
  selector: 'app-sender-template',
  imports: [CommonModule, RouterLink, AdminDialogComponent],
  templateUrl: './sender-template.component.html',
  styleUrl: './sender-template.component.scss'
})
export class SenderTemplateComponent {
  @ViewChild(AdminDialogComponent) adminDialog!: AdminDialogComponent;

  router = inject(Router);
  senderTemplateService = inject(SenderTemplateService);
  toasterService = inject(ToasterService);
  moment = moment;

  ngOnInit() {
    this.senderTemplateService.getAll().subscribe();
  }

  deleteTemplate(id: number) {
    this.adminDialog.showConfirm('Удалить шаблон?').then((confirmed) => {
      if (confirmed) {
        this.senderTemplateService.delete(id).subscribe({
          next: () => {
            this.toasterService.showSuccess('Шаблон успешно удален');
          },
          error: () => {
            this.toasterService.showError('Ошибка удаления');
          }
        });
      }
    });
  }
}

