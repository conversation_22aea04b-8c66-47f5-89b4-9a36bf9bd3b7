<div class="admin-component">
  <div class="admin-page-header">
    <div>
      <h1 class="admin-page-title">Шаблоны рассылок</h1>
    </div>
    <div class="admin-actions">
      <button type="button" class="btn btn-primary" [routerLink]="['/sender-template/add']">Создать шаблон</button>
    </div>
  </div>

  <admin-dialog></admin-dialog>

  <div class="admin-content-wrapper admin-table">
    <table>
      <thead>
        <tr>
          <th>ID</th>
          <th>Название</th>
          <th>Дата создания</th>
          <th></th>
        </tr>
      </thead>
      <tbody>
        @for(item of senderTemplateService.list(); track item.id) {
          <tr>
            <td>{{item.id}}</td>
            <td>{{item.title}}</td>
            <td>{{moment(item.createdAt).format('DD.MM.YYYY HH:mm')}}</td>
            <td>
              <div class="d-flex gap-2">
                <button class="btn btn-primary btn-sm" [routerLink]="['/sender-template/' + item.id]">Редактировать</button>
                <button class="btn btn-danger btn-sm" (click)="deleteTemplate(item.id)">Удалить</button>
              </div>
            </td>
          </tr>
        }
      </tbody>
    </table>
  </div>
</div>

