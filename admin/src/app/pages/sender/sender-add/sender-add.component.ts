import editorConfig from "@/editor.config"
import { CustomUploadAdapter } from "@/pages/content/content-add/custom-upload-adapter"
import { SenderTemplateService } from "@/services/sender-template.service"
import { SenderService } from "@/services/sender.service"
import { ToasterService } from "@/services/toaster.service"
import { UserService } from "@/services/user.service"
import { CommonModule, Location } from "@angular/common"
import { Component, inject, ViewEncapsulation } from '@angular/core'
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from "@angular/forms"
import { ActivatedRoute, Router } from "@angular/router"
import { CKEditorModule } from "@ckeditor/ckeditor5-angular"
import { NgSelectComponent } from "@ng-select/ng-select"
import { ClassicEditor } from 'ckeditor5'
import { environment } from "../../../../environments/environment"

@Component({
  selector: 'app-sender-add',
  imports: [CommonModule, ReactiveFormsModule, FormsModule, CKEditorModule, NgSelectComponent],
  templateUrl: './sender-add.component.html',
  styleUrl: './sender-add.component.scss',
  encapsulation: ViewEncapsulation.None
})
export class SenderAddComponent {
  fb = inject(FormBuilder);
  route = inject(ActivatedRoute);
  router = inject(Router);
  location = inject(Location);
  senderService = inject(SenderService);
  senderTemplateService = inject(SenderTemplateService);
  userService = inject(UserService);
  toasterService = inject(ToasterService);

  id: number | null = null;
  Editor = ClassicEditor;
  editorConfig: any = {};
  editorInstance: any;
  isLayoutReady = false;

  form = this.fb.group({
    title: ['', Validators.required],
    subject: ['', Validators.required],
    htmlContent: [''],
    emails: [[] as string[]],
    userIds: [[] as number[]],
    userGroups: [[] as string[]]
  });

  groups: any[] = [];
  users: any[] = [];
  templates: any[] = [];
  emailsInput: string = '';
  selectedTemplateId: number | null = null;

  ngOnInit() {
    this.editorConfig = editorConfig;
    this.isLayoutReady = true;

    this.userService.getGroups().subscribe((res: any) => {
      this.groups = res;
    });

    this.senderTemplateService.getAll().subscribe((res: any) => {
      this.templates = res;
    });

    this.userService.getAll(1).subscribe((res: any) => {
      this.users = res.data || res;

      const id = this.route.snapshot.params['id'];
      if (id && id !== 'add') {
        this.id = +id;
        this.senderService.getOne(this.id).subscribe((sender: any) => {
          const userIds = (sender.userIds || []).map((id: any) => typeof id === 'string' ? parseInt(id) : id);

          this.form.patchValue({
            title: sender.title,
            subject: sender.subject,
            htmlContent: sender.htmlContent,
            userIds: userIds,
            userGroups: sender.userGroups || []
          });
          this.emailsInput = sender.emails?.join(', ') || '';
        });
      }
    });
  }

  onEditorReady(editor: any) {
    this.editorInstance = editor;
    this.editorInstance.plugins.get("FileRepository").createUploadAdapter = (loader: any) => {
      return new CustomUploadAdapter(loader, `${environment.apiUrl}/file/ck/upload`);
    };
  }

  onEmailsChange() {
    const emails = this.emailsInput
      .split(',')
      .map(e => e.trim())
      .filter(e => e.length > 0);
    this.form.patchValue({emails});
  }

  onTemplateSelect() {
    if (this.selectedTemplateId) {
      const template = this.templates.find(t => t.id === this.selectedTemplateId);
      if (template) {
        this.form.patchValue({htmlContent: template.htmlContent});
      }
    }
  }

  onSubmit() {
    if (this.form.invalid) {
      this.toasterService.showError('Заполните обязательные поля');
      return;
    }

    const data = this.form.value;

    if (this.id) {
      this.senderService.update(this.id, data).subscribe({
        next: () => {
          this.toasterService.showSuccess('Рассылка успешно обновлена');
          this.back();
        },
        error: () => {
          this.toasterService.showError('Ошибка обновления');
        }
      });
    } else {
      this.senderService.create(data).subscribe({
        next: (res: any) => {
          this.toasterService.showSuccess('Рассылка успешно создана');
          this.router.navigate(['/sender/' + res.id]);
        },
        error: () => {
          this.toasterService.showError('Ошибка создания');
        }
      });
    }
  }

  back() {
    this.location.back();
  }
}

