<div class="admin-component">
  <div class="admin-page-header">
    <div>
      <h1 class="admin-page-title">{{id ? 'Редактировать рассылку' : 'Создать рассылку'}}</h1>
    </div>
    <div class="admin-actions">
      <button type="button" class="btn btn-outline-secondary" (click)="back()">Назад</button>
      <button type="button" class="btn btn-primary" (click)="onSubmit()">Сохранить</button>
    </div>
  </div>

  <div class="admin-content-wrapper">
    <form [formGroup]="form" class="space-y-5">
      <div>
        <label>Название рассылки <span class="text-danger">*</span></label>
        <input formControlName="title" type="text" class="form-input" placeholder="Название для внутреннего использования">
      </div>

      <div>
        <label>Тема письма <span class="text-danger">*</span></label>
        <input formControlName="subject" type="text" class="form-input" placeholder="Тема письма">
      </div>

      <div>
        <label>Email адреса (через запятую)</label>
        <textarea [(ngModel)]="emailsInput" [ngModelOptions]="{standalone: true}" (ngModelChange)="onEmailsChange()" class="form-input" rows="3" placeholder="<EMAIL>, <EMAIL>"></textarea>
      </div>

      <div>
        <label>Выбрать пользователей</label>
        <ng-select
          [items]="users"
          bindLabel="email"
          bindValue="id"
          [multiple]="true"
          formControlName="userIds"
          placeholder="Выберите пользователей">
          <ng-template ng-option-tmp let-item="item">
            {{item.email}} ({{item.firstName}} {{item.lastName}})
          </ng-template>
        </ng-select>
      </div>

      <div>
        <label>Выбрать группы пользователей</label>
        <ng-select
          [items]="groups"
          bindLabel="label"
          bindValue="value"
          [multiple]="true"
          formControlName="userGroups"
          placeholder="Выберите группы">
        </ng-select>
      </div>

      <div>
        <label>Выбрать шаблон</label>
        <ng-select
          [items]="templates"
          bindLabel="title"
          bindValue="id"
          [(ngModel)]="selectedTemplateId"
          [ngModelOptions]="{standalone: true}"
          (change)="onTemplateSelect()"
          placeholder="Выберите шаблон">
        </ng-select>
      </div>

      <div>
        <label>Текст письма (HTML)</label>
        <ckeditor formControlName="htmlContent" [editor]="Editor" [config]="editorConfig" *ngIf="isLayoutReady" (ready)="onEditorReady($event)" />
      </div>
    </form>
  </div>
</div>

