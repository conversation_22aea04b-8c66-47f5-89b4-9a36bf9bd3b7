import { AdminDialogComponent } from "@/components/admin-dialog/admin-dialog.component"
import { SenderService } from "@/services/sender.service"
import { ToasterService } from "@/services/toaster.service"
import { CommonModule } from "@angular/common"
import { Component, inject, ViewChild } from '@angular/core'
import { Router, RouterLink } from "@angular/router"
import moment from "moment"

@Component({
  selector: 'app-sender',
  imports: [CommonModule, RouterLink, AdminDialogComponent],
  templateUrl: './sender.component.html',
  styleUrl: './sender.component.scss'
})
export class SenderComponent {
  @ViewChild(AdminDialogComponent) adminDialog!: AdminDialogComponent;

  router = inject(Router);
  senderService = inject(SenderService);
  toasterService = inject(ToasterService);
  moment = moment;

  ngOnInit() {
    this.senderService.getAll().subscribe();
  }

  deleteSender(id: number) {
    this.adminDialog.showConfirm('Удалить рассылку?').then((confirmed) => {
      if (confirmed) {
        this.senderService.delete(id).subscribe({
          next: () => {
            this.toasterService.showSuccess('Рассылка успешно удалена');
          },
          error: () => {
            this.toasterService.showError('Ошибка удаления');
          }
        });
      }
    });
  }

  sendEmail(id: number) {
    this.adminDialog.showConfirm('Запустить рассылку?').then((confirmed) => {
      if (confirmed) {
        this.senderService.send(id).subscribe({
          next: (res: any) => {
            this.toasterService.showSuccess(`Рассылка отправлена ${res.recipientsCount} получателям`);
            this.senderService.getAll().subscribe();
          },
          error: (err) => {
            this.toasterService.showError('Ошибка отправки: ' + err.error.message);
          }
        });
      }
    });
  }

  getStatusLabel(status: string): string {
    const labels: any = {
      draft: 'Черновик',
      sent: 'Отправлено',
      failed: 'Ошибка'
    };
    return labels[status] || status;
  }
}

