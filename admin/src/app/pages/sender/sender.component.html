<div class="admin-component">
  <div class="admin-page-header">
    <div>
      <h1 class="admin-page-title">Рассылки</h1>
    </div>
    <div class="admin-actions">
      <button type="button" class="btn btn-primary" [routerLink]="['/sender/add']">Создать рассылку</button>
    </div>
  </div>

  <admin-dialog></admin-dialog>

  <div class="admin-content-wrapper admin-table">
    <table>
      <thead>
        <tr>
          <th>ID</th>
          <th>Название</th>
          <th>Тема письма</th>
          <th>Статус</th>
          <th>Получателей</th>
          <th>Дата создания</th>
          <th>Дата отправки</th>
          <th></th>
        </tr>
      </thead>
      <tbody>
        @for(item of senderService.list(); track item.id) {
          <tr>
            <td>{{item.id}}</td>
            <td>{{item.title}}</td>
            <td>{{item.subject}}</td>
            <td>{{getStatusLabel(item.status)}}</td>
            <td>{{item.recipientsCount}}</td>
            <td>{{moment(item.createdAt).format('DD.MM.YYYY HH:mm')}}</td>
            <td>{{item.sentAt ? moment(item.sentAt).format('DD.MM.YYYY HH:mm') : '-'}}</td>
            <td>
              <div class="d-flex gap-2">
                <button class="btn btn-primary btn-sm" [routerLink]="['/sender/' + item.id]">Редактировать</button>
                <button class="btn btn-success btn-sm" (click)="sendEmail(item.id)">Запустить</button>
                <button class="btn btn-danger btn-sm" (click)="deleteSender(item.id)">Удалить</button>
              </div>
            </td>
          </tr>
        }
      </tbody>
    </table>
  </div>
</div>

