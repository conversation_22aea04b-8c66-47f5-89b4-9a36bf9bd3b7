<div class="panel">
  <div class="flex items-center justify-between mb-5">
    <h5 class="font-semibold text-lg dark:text-white-light">Конструктор</h5>
    <a routerLink="/constructor/add" class="btn btn-primary">Добавить конструктор</a>
  </div>

  <div class="table-responsive">
    <table>
      <thead>
        <tr>
          <th>Заголовок</th>
          <th>Размер</th>
          <th>Действия</th>
        </tr>
      </thead>
      <tbody>
        @for(item of constructorService.items; track item.id) {
          <tr>
            <td>{{item.title}}</td>
            <td>{{item.size}}</td>
            <td>
              <div class="admin-table-actions">
                <a [routerLink]="'/constructor/' + item.slug" class="btn btn-sm btn-primary">Редактировать</a>
                <button class="btn btn-sm btn-danger" (click)="deleteConstructor(item.slug)">Удалить</button>
              </div>
            </td>
          </tr>
        }
      </tbody>
    </table>
  </div>
</div>
