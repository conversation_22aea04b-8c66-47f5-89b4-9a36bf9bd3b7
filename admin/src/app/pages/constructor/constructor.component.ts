import { AdminDialogComponent } from "@/components/admin-dialog/admin-dialog.component";
import { ConstructorService } from "@/services/constructor.service";
import { ToasterService } from "@/services/toaster.service";
import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { RouterModule } from '@angular/router';
import { environment } from "../../../environments/environment";

@Component({
    selector: 'app-constructor',
    standalone: true,
    imports: [
        CommonModule,
        RouterModule,
        AdminDialogComponent
    ],
    templateUrl: './constructor.component.html',
    styleUrl: './constructor.component.scss'
})
export class ConstructorComponent {
  constructorService = inject(ConstructorService)
  toasterService = inject(ToasterService)
  protected readonly environment = environment;

  ngOnInit() {
    this.constructorService.getAll().subscribe();
  }

  deleteConstructor(slug: string) {
    if (confirm('Вы уверены, что хотите удалить этот конструктор?')) {
      this.constructorService.deleteBlock(slug).subscribe(() => {
        this.constructorService.getAll().subscribe();
      });
    }
  }
}
