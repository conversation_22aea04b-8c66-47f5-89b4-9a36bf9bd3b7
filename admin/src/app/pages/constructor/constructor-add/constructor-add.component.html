<div class="panel" style='margin-bottom: 100px;'>
  <div class="flex items-center justify-between mb-5">
    <h5 class="font-semibold text-lg dark:text-white-light">
      {{slug && slug !== 'add' ? 'Редактировать конструктор' : 'Добавить конструктор'}}
    </h5>
    <button (click)="back()" class="btn btn-outline-secondary">Назад</button>
  </div>

  <div class="mb-5">
    <div class="mt-3 flex flex-wrap border-b border-white-light dark:border-[#191e3a]">
      @for(lang of languages; track lang) {
        <a
          href="javascript:"
          class="-mb-[1px] block border border-transparent p-3.5 py-2 !outline-none transition duration-300 hover:text-primary dark:hover:border-b-black"
          [ngClass]="{ '!border-white-light !border-b-white  text-primary dark:!border-[#191e3a] dark:!border-b-black': selectedLanguage === lang }"
          (click)="selectLanguage(lang)"
        >
          {{lang.toUpperCase()}}
        </a>
      }
    </div>
  </div>

  @for(item of addConstructorForms; track item.lang) {
    @if(selectedLanguage == item.lang) {
      <dialog #dialog class="admin-modal">
        <div class="admin-modal-content">
          <div>{{message}}</div>
          <div class="admin-modal-footer">
            <button (click)="close(dialog)" class="btn btn-primary">OK</button>
          </div>
        </div>
      </dialog>

      <form [formGroup]="item.form" class="space-y-5">
        <div>
          <label>Заголовок</label>
          <div [ngClass]="{ 'has-error': item.form.get('title')?.invalid && item.form.get('title')?.touched }" class="flex items-center">
            <input type="text" class="form-input" formControlName="title" required><span class="asterix">*</span>
          </div>
        </div>

        <div>
          <label>Размер</label>
          <div [ngClass]="{ 'has-error': item.form.get('size')?.invalid && item.form.get('size')?.touched }" class="flex items-center">
            <select formControlName="size" class="form-select" required>
              @for(size of carouselSizes; track $index) {
                <option [ngValue]="size.value">{{size.title}}</option>
              }
            </select>
            <span class="asterix">*</span>
          </div>
          @if(item.form.get('size')?.value) {
            <div class="mt-3">
              <label class="text-sm text-gray-600">Превью размера:</label>
              <div class="p-2 bg-gray-50 rounded">
                <div
                  class="carousel-preview"
                  [ngClass]="getPreviewClass(item.form.get('size')?.value)"
                  [title]="'Превью ' + item.form.get('size')?.value">
                </div>
              </div>
            </div>
          }
        </div>

        <div>
          <label>Ссылка "Смотреть больше"</label>
          <input type="text" formControlName="link" class="form-input">
        </div>

        <div>
          <label>Элементы</label>
          <button type="button" class="btn btn-primary btn-sm" (click)="showCarouselItemForm()">Добавить</button>
          <table class="mt-5" *ngIf="items.value.length">
            <thead>
            <tr>
              <th>Дата</th>
              <th>Текст</th>
              <th></th>
            </tr>
            </thead>
            <tbody>
            @for(carouselItem of items.value; track $index) {
              <tr>
                <td>{{carouselItem.date}}</td>
                <td>{{carouselItem.text}}</td>
                <td>
                  <div class="flex gap-2">
                    <button type="button" class="btn btn-primary btn-sm" (click)="editCarouselItem($index, carouselItem)">Редактировать</button>
                    <button type="button" class="btn btn-danger btn-sm" (click)="removeCarouselItem($index)">Удалить</button>
                  </div>
                </td>
              </tr>
            }
            </tbody>
          </table>
        </div>
      </form>
    }
  }

  <dialog #carouselItemFormDialog class="admin-modal">
    <div class="admin-modal-content">
      <form [formGroup]="carouselFormItem" class="space-y-3">
      <div>
        <div class="flex items-center">
          <input formControlName="show" type="checkbox" class="form-checkbox">
          Показывать
          <span title="Показывать" class="cursor-pointer mx-2">
            &#9432;
          </span>
        </div>
      </div>
        <div>
          <label>Название</label>
          <div [ngClass]="{ 'has-error': carouselFormItem.get('title')?.invalid && carouselFormItem.get('title')?.touched }" class="flex items-center">
            <input required formControlName="title" type="text" class="form-input"><span class="asterix">*</span>
            <span title="Название" class="cursor-pointer mx-2">
            &#9432;
          </span>
          </div>
        </div>
        <div>
          <label>Тег</label>
          <div class="flex items-center">
            <input formControlName="tag" type="text" class="form-input">
            <span title="Тег" class="cursor-pointer mx-2">
            &#9432;
          </span>
          </div>
        </div>
      <div>
        <label>Ссылка</label>
        <div [ngClass]="{ 'has-error': carouselFormItem.get('link')?.invalid && carouselFormItem.get('link')?.touched }" class="flex items-center">
          <input required formControlName="link" type="text" class="form-input"><span class="asterix">*</span>
          <span title="Ссылка" class="cursor-pointer mx-2">
            &#9432;
          </span>
        </div>
      </div>
      <div>
        <label>Дата</label>
        <div [ngClass]="{ 'has-error': carouselFormItem.get('date')?.invalid && carouselFormItem.get('date')?.touched }" class="flex items-center">
          <input required formControlName="date" type="date" class="form-input"><span class="asterix">*</span>
          <span title="Дата" class="cursor-pointer mx-2">
            &#9432;
          </span>
        </div>
      </div>
      <div [ngClass]="{ 'has-error': carouselFormItem.get('text')?.invalid && carouselFormItem.get('text')?.touched }">
        <label>Текст</label>
        <div  class="flex items-center">
          <textarea required formControlName="text" class="form-textarea"></textarea><span class="asterix">*</span>
          <span title="Текст" class="cursor-pointer mx-2">
            &#9432;
          </span>
        </div>
      </div>
      <div class="flex flex-col">
        <label>Изображение</label>
        <div [ngClass]="{ 'has-error': carouselFormItem.get('image')?.invalid && carouselFormItem.get('image')?.touched }" class="flex items-center">
          <input #carouselItemImage required type="file"  accept="image/*" class="form-input" (change)="uploadFile($event)"><span class="asterix">*</span>
          <span title="Изображение" class="cursor-pointer mx-2">
            &#9432;
          </span>
        </div>
        @if(carouselFormItem.value.image) {
          <PhotoPreview [disableRemoveBtn]="true" (onItemRemoved)="onPreviewRemoved()" [items]="[carouselFormItem.value.image]"/>
        }
      </div>
      <div class="admin-modal-footer">
        <button [disabled]="carouselFormItem.invalid" type="button" class="btn btn-primary btn-sm" (click)="addCarouselItem()">Сохранить</button>
        <button type="button" class="btn btn-outline-secondary btn-sm" (click)="closeCarouselItemForm()">Закрыть</button>
      </div>
    </form>
    </div>
  </dialog>

  <div class="fixed-buttons">
    @if(slug && slug !== 'add') {
      <button [disabled]="!addValidationFormSubmit()" (click)="addConstructorFormSubmit()" type="submit" class="btn btn-primary">Обновить</button>
      <button (click)="back()" class="btn">Закрыть</button>
    } @else {
      <button [disabled]="!addValidationFormSubmit()" (click)="addConstructorFormSubmit()" type="submit" class="btn btn-primary">Создать</button>
      <button (click)="back()" class="btn">Закрыть</button>
    }
  </div>
</div>

