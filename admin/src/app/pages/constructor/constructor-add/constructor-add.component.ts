import { PhotopreviewComponent } from '@/components/photopreview/photopreview.component'
import { ConstructorService } from '@/services/constructor.service'
import { FileService } from '@/services/file.service'
import { ToasterService } from '@/services/toaster.service'
import { CommonModule, Location } from '@angular/common'
import { Component, ElementRef, inject, OnInit, ViewChild } from '@angular/core'
import { FormArray, FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'

@Component({
  selector: 'app-constructor-add',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    PhotopreviewComponent
  ],
  templateUrl: './constructor-add.component.html',
  styleUrl: './constructor-add.component.scss'
})
export class ConstructorAddComponent implements OnInit {
  @ViewChild('dialog') dialog!: ElementRef<HTMLDialogElement>;
  @ViewChild('carouselItemFormDialog') carouselItemFormDialog!: ElementRef<HTMLDialogElement>;
  @ViewChild('carouselItemImage') carouselItemImage!: ElementRef<HTMLInputElement>;

  fb = inject(FormBuilder);
  route = inject(ActivatedRoute);
  router = inject(Router);
  constructorService = inject(ConstructorService);
  fileService = inject(FileService);
  toasterService = inject(ToasterService);
  location = inject(Location);

  languages = ['ru', 'en', 'de', 'ua', 'it'];
  selectedLanguage = 'ru';
  addConstructorForms: { lang: string, form: any }[] = [];
  slug = this.route.snapshot.params['slug'];
  message: string = '';

  carouselFormItem: any = this.fb.group({
    index: null,
    title: [null, Validators.required],
    tag: [null],
    link: null,
    date: [null],
    text: [null],
    image: [null, Validators.required],
    show: true
  })

  carouselSizes = [
    { title: 'Карусель 1', value: 'carousel1'},
    { title: 'Карусель 2', value: 'carousel2'},
    { title: 'Карусель 3', value: 'carousel3'},
    { title: 'Карусель 4', value: 'carousel4'},
  ]

  ngOnInit() {
    this.fillForm();

    if (this.slug && this.slug !== 'add') {
      this.constructorService.getBySlug(this.slug).subscribe((res: any) => {
        res.forEach((item: any) => {
          const formIndex = this.addConstructorForms.findIndex(f => f.lang === item.lang);
          if (formIndex !== -1) {
            this.addConstructorForms[formIndex].form.patchValue({
              title: item.title,
              size: item.size,
              type: item.type,
              link: item.link,
              items: []
            });
            const itemsArray = this.addConstructorForms[formIndex].form.get('items') as FormArray;
            itemsArray.clear();
            if(item.items && item.items.length) {
              item.items.forEach((carouselItem: any) => {
                itemsArray.push(this.fb.group(carouselItem));
              });
            }
          }
        });
      });
    }
  }

  fillForm() {
    this.languages.forEach(lang => {
      this.addConstructorForms.push({
        lang,
        form: this.fb.group({
          title: ['', Validators.required],
          size: ['', Validators.required],
          type: 'carousel',
          link: [null],
          items: this.fb.array([])
        })
      });
    });
  }

  selectLanguage(lang: string) {
    this.selectedLanguage = lang;
  }

  get currentForm() {
    return this.addConstructorForms.find(f => f.lang === this.selectedLanguage)?.form;
  }

  get items() {
    return this.currentForm?.get('items') as FormArray;
  }

  showCarouselItemForm() {
    this.carouselItemImage.nativeElement.value = ''
    this.carouselItemFormDialog.nativeElement.showModal()
  }

  closeCarouselItemForm() {
    this.carouselItemFormDialog.nativeElement.close()
    this.carouselFormItem.reset()
    this.carouselFormItem.get('show').patchValue(true)
  }

  addCarouselItem() {
    const itemsArray = this.items;

    const newItem = this.fb.group({
      index: null,
      title: this.carouselFormItem.get('title')?.value,
      tag: this.carouselFormItem.get('tag')?.value,
      link: this.carouselFormItem.get('link')?.value,
      date: this.carouselFormItem.get('date')?.value,
      text: this.carouselFormItem.get('text')?.value,
      image: this.carouselFormItem.get('image')?.value,
      show: this.carouselFormItem.get('show')?.value,
    });

    if (this.carouselFormItem.get('index')?.value !== null) {
      itemsArray.at(this.carouselFormItem.get('index')!.value).patchValue(newItem.value);
    } else {
      itemsArray.push(newItem);
    }

    this.closeCarouselItemForm();
  }

  uploadFile(e: Event) {
    const target = e.target as HTMLInputElement;
    if(!target.files) return

    this.fileService.uploadToTempFolder(target.files).subscribe((res: any) => {
      this.carouselFormItem.controls.image.patchValue(res[0])
    })
  }

  editCarouselItem(index: number, carousel: any) {
    carousel.index = index
    this.carouselFormItem.patchValue(carousel)
    this.showCarouselItemForm()
  }

  removeCarouselItem(index: number) {
    this.items.removeAt(index);
  }

  onPreviewRemoved() {
    this.carouselFormItem.patchValue({image: null})
  }

  getPreviewClass(sizeValue: string): string {
    if (!sizeValue) return 'carousel-preview-placeholder';

    const validSizes = ['carousel1', 'carousel2', 'carousel3', 'carousel4'];
    if (validSizes.includes(sizeValue)) {
      return `carousel-preview-${sizeValue}`;
    }

    return 'carousel-preview-placeholder';
  }

  addConstructorFormSubmit() {
    const form = this.addConstructorForms.map(e => ({ ...e, form: e.form.value }));

    if (this.slug && this.slug !== 'add') {
      return this.constructorService.update(this.slug, form).subscribe({
        next: () => {
          this.toasterService.showSuccess('Конструктор успешно обновлен');
          this.back();
        },
        error: () => {
          this.toasterService.showError('Конструктор не обновлен. Попробуйте еще');
        }
      });
    }

    return this.constructorService.create(form).subscribe({
      next: (res: any) => {
        this.toasterService.showSuccess('Конструктор успешно добавлен');
        this.router.navigateByUrl('/constructor/' + res.slug);
      },
      error: () => {
        this.toasterService.showError('Конструктор не добавлен. Попробуйте еще');
      }
    });
  }

  addValidationFormSubmit() {
    const currentForm = this.addConstructorForms.find(f => f.lang === this.selectedLanguage);
    return currentForm?.form.valid || false;
  }

  back() {
    this.location.back();
  }

  close(dialog: any) {
    dialog.close();
  }

  openDialog(message: string) {
    this.message = message;
    this.dialog.nativeElement.showModal();
  }
}

