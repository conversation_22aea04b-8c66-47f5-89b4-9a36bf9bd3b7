.fixed-buttons {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: flex;
  gap: 10px;
  z-index: 1000;
}

.admin-modal {
  border: none;
  border-radius: 8px;
  padding: 0;
  max-width: 500px;
  width: 90%;
}

.admin-modal-content {
  padding: 20px;
}

.admin-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.carousel-preview {
  width: 200px;
  height: 150px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.carousel-preview-carousel1 {
  background-image: url('/carousel1.png');
}

.carousel-preview-carousel2 {
  background-image: url('/carousel2.png');
}

.carousel-preview-carousel3 {
  background-image: url('/carousel3.png');
}

.carousel-preview-carousel4 {
  background-image: url('/carousel4.png');
}

.carousel-preview-placeholder {
  background-color: #f5f5f5;
}

