import { AdminDialogComponent } from "@/components/admin-dialog/admin-dialog.component"
import { PhotopreviewComponent } from "@/components/photopreview/photopreview.component"
import { LanguagesEnum } from "@/enums/languages.enum"
import { AudioService } from "@/services/audio.service"
import { ContentService } from "@/services/content.service"
import { FileService } from "@/services/file.service"
import { ToasterService } from "@/services/toaster.service"
import { CommonModule, isPlatformBrowser, Location } from "@angular/common"
import { ChangeDetectorRef, Component, ElementRef, inject, Inject, PLATFORM_ID, ViewChild, ViewEncapsulation } from '@angular/core'
import { FormArray, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from "@angular/forms"
import { ActivatedRoute, Router } from "@angular/router"
import { CKEditorModule } from "@ckeditor/ckeditor5-angular"
import { NgSelectComponent } from '@ng-select/ng-select'
import {
  AccessibilityHelp,
  Autoformat,
  AutoLink,
  Autosave,
  BalloonToolbar,
  BlockQuote,
  Bold,
  ClassicEditor,
  Code,
  type EditorConfig,
  Element,
  Essentials,
  FileRepository,
  FindAndReplace,
  FullPage,
  GeneralHtmlSupport,
  Heading,
  HorizontalLine,
  HtmlComment,
  HtmlEmbed,
  Image,
  ImageCaption,
  ImageResize,
  ImageStyle,
  ImageToolbar,
  ImageUpload,
  Indent,
  IndentBlock,
  Italic,
  Link,
  LinkImage,
  List,
  Mention,
  Paragraph,
  RemoveFormat,
  SelectAll,
  ShowBlocks,
  SimpleUploadAdapter,
  SourceEditing,
  SpecialCharacters,
  SpecialCharactersArrows,
  SpecialCharactersCurrency,
  SpecialCharactersEssentials,
  SpecialCharactersLatin,
  SpecialCharactersMathematical,
  SpecialCharactersText,
  Strikethrough,
  Table,
  TableToolbar,
  TextPartLanguage,
  TextTransformation,
  Underline,
  Undo
} from 'ckeditor5'
import { debounceTime, distinctUntilChanged, of, Subject, switchMap, tap } from "rxjs"
import { environment } from "../../../../environments/environment"
import { CustomUploadAdapter } from './custom-upload-adapter'

@Component({
    selector: 'app-content-add',
  imports: [CKEditorModule, ReactiveFormsModule, CommonModule, NgSelectComponent, PhotopreviewComponent, FormsModule, AdminDialogComponent],
    templateUrl: './content-add.component.html',
    styleUrl: './content-add.component.scss',
    encapsulation: ViewEncapsulation.None
})
export class ContentAddComponent {
  @ViewChild('dialog') dialog!: ElementRef<HTMLDialogElement>;
  @ViewChild(AdminDialogComponent) adminDialog!: AdminDialogComponent;
  @ViewChild('addButtonDialog') addButtonDialog!: ElementRef<HTMLDialogElement>;
  @ViewChild('addAudioDialog') addAudioDialog!: ElementRef<HTMLDialogElement>;
  @ViewChild('linkedContentDialog') linkedContentDialog!: ElementRef<HTMLDialogElement>;
  message: string = '';
  editorInstance: any
  selectedLanguage = 'ru'
  Editor = ClassicEditor
  editorConfig: EditorConfig = {};
  fb = inject(FormBuilder)
  router = inject(Router)
  route = inject(ActivatedRoute)
  contentService = inject(ContentService)
  changeDetector = inject(ChangeDetectorRef)
  audioService = inject(AudioService)
  fileService = inject(FileService)
  toasterService = inject(ToasterService)
  languages = Object.keys(LanguagesEnum)
  addContentForms: { lang: string, form: FormGroup }[] = []
  slug = this.route.snapshot.params['slug']
  category = null
  lanIdx = 0;
  isLayoutReady = false
  tags: any
  audiofiles_all: any = []
  currentForm: any = null
  buttonForm = this.fb.group({
    index: null,
    name: null,
    link: null,
  })
  audioForm = this.fb.group({
    index: null,
    audio: null,
    paid: false
  })
  authors: any = []
  linkedNum: number = 0
  allLinks: any = []
  replaceLinkForm = this.fb.group({
    replace: ''
  })
  audioInput$ = new Subject<string>();
  audiofiles$: any;
  isLoading = false
  environment = environment

  get tagsArray() {
    return (form: FormGroup) => (form.get('tags') as FormArray).value
  }

  constructor(
    @Inject(PLATFORM_ID) private platformId: any,
    private location: Location
  ) {}

  get buttons() {
    if (!this.currentForm) return this.fb.array([]);

    return this.currentForm.get('buttons') as FormArray
  }

  get audiofiles() {
    if (!this.currentForm) return this.fb.array([]);

    return this.currentForm.get('audiofiles') as FormArray
  }

  get youtube_links() {
    if (!this.currentForm) return this.fb.array([]);

    return this.currentForm.get('youtube_links') as FormArray;
  }

  get isPaid() {
    return this.audiofiles.value.some((e: any) => e.paid) || this.youtube_links.value.some((e: any) => e.paid);
  }

  goBack(): void {
    if (isPlatformBrowser(this.platformId)) {
      if (window.history.length > 1) {
        this.location.back();
      } else {
        this.router.navigate(['/']);
      }
    } else {
      console.log('Back button not available in SSR.');
    }
  }

  public ngAfterViewInit(): void {
    const componentContext = this;
    this.editorConfig = {
      mention: {
        feeds: [
          {
            marker: '@',
            feed: this.getFeedItems.bind(this),
            minimumCharacters: 0
          }
        ]
      },
      toolbar: {
        items: [
          'undo',
          'redo',
          '|',
          'sourceEditing',
          'showBlocks',
          '|',
          'heading',
          '|',
          'bold',
          'italic',
          'underline',
          'removeFormat',
          '|',
          'blockQuote',
          '|',
          'outdent',
          'indent',
          '|',
          'specialCharacters',
          'specialCharactersEssentials',
          '|',
          'uploadImage',
          'imageStyle:inline',
          'imageStyle:block',
          'imageStyle:side',
          'toggleImageCaption', 'imageTextAlternative', 'ckboxImageEdit',
          'link',
          // 'linkImage',
          '|',
          'insertLink',
          'editLink',
          'unlink',
          '|',
          'bulletedList',
          'numberedList',
          'insertTable',
          'mediaEmbed'
          // 'LinkImage'
        ],
        shouldNotGroupWhenFull: false
      },

      plugins: [
        AccessibilityHelp,
        Autoformat,
        AutoLink,
        Autosave,
        BalloonToolbar,
        BlockQuote,
        RemoveFormat,
        Bold,
        Code,
        Essentials,
        FindAndReplace,
        FullPage,
        GeneralHtmlSupport,
        Heading,
        HorizontalLine,
        HtmlComment,
        HtmlEmbed,
        Indent,
        IndentBlock,
        Italic,
        Link,
        Paragraph,
        SelectAll,
        ShowBlocks,
        SourceEditing,
        SpecialCharacters,
        SpecialCharactersArrows,
        SpecialCharactersCurrency,
        SpecialCharactersEssentials,
        SpecialCharactersLatin,
        SpecialCharactersMathematical,
        SpecialCharactersText,
        Strikethrough,
        TextPartLanguage,
        TextTransformation,
        Underline,
        Undo,
        Mention,
        Image,
        ImageToolbar,
        ImageCaption,
        ImageStyle,
        ImageResize,
        LinkImage,
        Image,
        ImageToolbar,
        ImageCaption,
        ImageStyle,
        ImageResize,
        ImageUpload,
        FileRepository,
        SimpleUploadAdapter,
        List,
        Table,
        TableToolbar,
        // MediaEmbed,
        function (editor) {

          editor.model.schema.setAttributeProperties('fontColor', {
            isFormatting: true
          });

          editor.model.schema.setAttributeProperties('fontBackgroundColor', {
            isFormatting: true
          });

          editor.model.schema.setAttributeProperties('htmlAttributes', {
            isFormatting: true
          });

          editor.model.schema.setAttributeProperties('htmlSpan', {
            isFormatting: true
          });

          editor.model.schema.setAttributeProperties('style', {
            isFormatting: true
          });

          editor.model.schema.setAttributeProperties('linkHref', {
            isFormatting: true,
            copyOnEnter: false,
            isAllowed: () => false
          });

          editor.conversion.for('upcast').elementToAttribute({
            view: {
              name: 'a',
              key: 'data-mention',
              classes: 'mention',
              attributes: {
                href: true,
                'data-content-id': true,
                'data-id': true
              }
            },
            model: {
              key: 'mention',
              value: (viewItem: Element) => {


                return editor.plugins.get('Mention').toMentionAttribute(viewItem, {
                  link: viewItem.getAttribute('href'),
                  content_id: viewItem.getAttribute('data-content-id')
                });
              }
            },
            converterPriority: 'high'
          } );

          editor.conversion.for('downcast').attributeToElement({
            model: 'mention',
            view: (modelAttributeValue, {writer}) => {

              const selection = componentContext.editorInstance.model.document.selection;
              const position = selection.getFirstPosition();

              setTimeout(() => {
                const currentForm = componentContext.addContentForms.find(e => e.lang == componentContext.selectedLanguage);
                if (currentForm) {
                    currentForm.form.patchValue({
                        content: componentContext.editorInstance.getData()
                    });

                    // Restore caret position after content update
                    componentContext.editorInstance.model.change((writer: any) => {
                        writer.setSelection(position);
                    });
                }
              }, 1000)

              if(!modelAttributeValue) return false

              const linkElement = writer.createAttributeElement('a', {
                class: 'mention',
                'data-mention': modelAttributeValue.id,
                'data-content-id': modelAttributeValue.content_id,
                'href': modelAttributeValue.link,
                'routerLink': modelAttributeValue.link, // Add Angular router directive
                'data-angular-link': 'true' // Add a marker to identify this as an Angular router link
              }, {
                priority: 20,
                id: modelAttributeValue.uid
              });

              return linkElement;
            }
          })
        }
      ],

      balloonToolbar: ['bold', 'italic', '|', 'link'],
      image: {

        resizeOptions: [
          {
              name: 'resizeImage:original',
              value: null,
              icon: 'original'
          },
          {
              name: 'resizeImage:custom',
              value: 'custom',
              icon: 'custom'
          },
          {
              name: 'resizeImage:50',
              value: '50',
              icon: 'medium'
          },
          {
              name: 'resizeImage:75',
              value: '75',
              icon: 'large'
          }
      ],
        toolbar: [
          'toggleImageCaption',
          'imageTextAlternative',
          'ckboxImageEdit',
          'resizeImage:50',
          'resizeImage:75',
          'resizeImage:original',
          'resizeImage:custom',
          'imageStyle:alignLeft',
          'imageStyle:alignCenter',
          'imageStyle:alignRight',
          '|',
          'imageResize'

        ]
      },
      heading: {
        options: [
          {
            model: 'paragraph',
            title: 'Paragraph',
            class: 'ck-heading_paragraph'
          },
          {
            model: 'heading1',
            view: 'h1',
            title: 'Heading 1',
            class: 'ck-heading_heading1'
          },
          // {
          //   model: 'heading2',
          //   view: 'h2',
          //   title: 'Heading 2',
          //   class: 'ck-heading_heading2'
          // },
          // {
          //   model: 'heading3',
          //   view: 'h3',
          //   title: 'Heading 3',
          //   class: 'ck-heading_heading3'
          // },
          // {
          //   model: 'heading4',
          //   view: 'h4',
          //   title: 'Heading 4',
          //   class: 'ck-heading_heading4'
          // },
          // {
          //   model: 'heading5',
          //   view: 'h5',
          //   title: 'Heading 5',
          //   class: 'ck-heading_heading5'
          // },
          // {
          //   model: 'heading6',
          //   view: 'h6',
          //   title: 'Heading 6',
          //   class: 'ck-heading_heading6'
          // }
        ]
      },
      htmlSupport: {
        allow: [
          {
            name: /^.*$/,  // Все элементы
            attributes: true,
            classes: true,
            styles: {
              // Разрешаем только определенные стили, исключая цвета
              'text-align': true,
              'margin': true,
              'padding': true,
              'font-weight': true,
              'font-style': true,
              'text-decoration': true,
              // Исключаем цвета, не указывая их здесь:
              // 'color': true,
              // 'background-color': true,
              // 'background': true,
            }
          }
        ],
        // Можно также явно запретить определенные стили
        disallow: [
          {
            name: /^.*$/,
            styles: {
              'color': true,
              'background-color': true,
              'background': true
            }
          }
        ]
      },
      link: {
        addTargetToExternalLinks: true,
        defaultProtocol: 'https://',
        decorators: {
          toggleDownloadable: {
            mode: 'manual',
            label: 'Downloadable',
            attributes: {
              download: 'file'
            }
          }
        },
      },
      list: {
        properties: {
          styles: true,
          startIndex: true,
          reversed: true
        }
      },
      menuBar: {
        isVisible: true
      },

    };

    this.isLayoutReady = true;
    this.changeDetector.detectChanges();
  }

  ngOnInit() {
    this.audioService.getTags().subscribe(res => this.tags = res)
    this.audioService.getAuthors().subscribe((res: any) => this.authors = res)
    this.contentService.getCategories()

    //this.audioService.getAudioFiles().subscribe((res: any) => this.audiofiles_all = res.items)

    this.fillForm()
    if(this.slug) this.getContent()

    this.audiofiles$ = this.audioInput$.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      tap(() => this.isLoading = true),
      switchMap(term => {
        if (!term || term.trim() === '') {
          return of([]);
        }
        return this.audioService.searchAudioFiles(term)
      }),
      tap(() => this.isLoading = false)
    );

  }

  getContent() {
    this.contentService.getBySlug(this.slug).subscribe((res: any) => {
      for(let item of this.addContentForms) {
        const itemLang = res.find((k: any) => k.lang == item.lang)
        if(!itemLang) continue
        if(!this.category) this.category = itemLang?.category?.id

        item.form.patchValue({
          ...itemLang, category: itemLang?.category?.id,
          tags: itemLang.tags.map((e: any) => e.id)
        })

        const buttonsArray: FormArray = this.fb.array([]);
        const buttons = itemLang.buttons || []
        delete itemLang.buttons;

        if(buttons) {
          for (let button of buttons) {
            buttonsArray.push(this.fb.group(button));
          }
          item.form.setControl('buttons', buttonsArray);
        }

        const audioFilesArray: FormArray = this.fb.array([]);
        const audioFiles = [...itemLang.audioFiles.map((e: any) => ({...e, type: 'audioFile'})), ...itemLang.audio.map((e: any) => ({...e, type: 'audio'}))];
        delete itemLang.audioFiles;

        if(audioFiles) {
          for (let item of audioFiles) {
            audioFilesArray.push(this.fb.group({index: null, audio: item, paid: item.paid}));
          }
          item.form.setControl('audiofiles', audioFilesArray);
        }

        const youtubeLinksArray: FormArray = this.fb.array([]);
        const youtubeLinks = itemLang.youtube_links || [];
        if (youtubeLinks) {
          for (const link of youtubeLinks) {
            youtubeLinksArray.push(this.fb.group({
              link: [link.link, Validators.required],
              paid: [link.paid || false]
            }));
          }
        }
        item.form.setControl('youtube_links', youtubeLinksArray);
      }

      this.currentForm = this.addContentForms.find((e: any) => e.lang == this.selectedLanguage)!.form;

      this.onContentPayStatusChange()
    })
  }

  fillForm() {
    this.languages.forEach(lang => {
      this.addContentForms.push({
        lang,
        form: this.fb.group({
          active: [true],
          slug: [this.slug || ''],
          title: ['', Validators.required],
          seo_title: ['', Validators.required],
          seo_description: ['', Validators.required],
          content: [''],
          category: [this.category || 2, Validators.required],
          telegram: [null],
          email: [null],
          instagram: [null],
          tags: [null],
          phone: [null],
          preview: [null, Validators.required],
          buttons: this.fb.array([]),
          audiofiles: this.fb.array([]),
          author: [null],
          youtube_links: this.fb.array([]),
          paid: [false],
          priceEur: [null],
          priceRub: [null]
        })
      })
    })
  }

  onEditorReady(editor: any) {
    this.editorInstance = editor;
    this.editorInstance.plugins.get("FileRepository").createUploadAdapter = (loader: any) => {
      return new CustomUploadAdapter(loader, `${environment.apiUrl}/file/ck/upload`);
    };

    // this.editorInstance.model.document.on('change:data', () => {
    //   const removedItems = Array.from(editor.model.document.differ.getChanges()).filter((change: any) => change.type === "remove")
    //   removedItems.forEach((removedItem: any) => {
    //     const src = removedItem.attributes.get("src")
    //     if (src) {
    //       this.fileService.deleteByPath(src, 'content').subscribe()
    //       console.log("Deleted image URL:", src)
    //     }
    //   })
    // })

    // Add paste event listener for Markdown conversion
    this.editorInstance.editing.view.document.on('paste', (evt: any, data: any) => {
      const clipboardData = data.dataTransfer;
      const pastedText = clipboardData.getData('text/plain');

      // Check if the pasted text contains Markdown syntax
      if (this.containsMarkdownSyntax(pastedText)) {
        // Prevent default paste behavior
        evt.stop();

        // Convert Markdown to HTML
        const htmlContent = this.convertMarkdownToHtml(pastedText);

        // Insert the converted HTML
        this.editorInstance.model.change((writer: any) => {
          const viewFragment = this.editorInstance.data.processor.toView(htmlContent);
          const modelFragment = this.editorInstance.data.toModel(viewFragment);
          this.editorInstance.model.insertContent(modelFragment);
        });
      }
    }, { priority: 'high' });
  }


  selectLanguage(lang: string) {
    this.selectedLanguage = lang
    switch (lang) {
      case 'RU':
        this.lanIdx = 0;
        break;
      case 'EN':
        this.lanIdx = 1;
        break;
      case 'DE':
        this.lanIdx = 2;
        break;
      default:
        break;
    }
    const editorInstance = this.editorInstance
    if (editorInstance) {
      setTimeout(() => {
        const content = this.addContentForms.find(e => e.lang == this.selectedLanguage)!.form.get('content')?.value
        editorInstance.setData(content); // Устанавливаем новое значение в CKEditor
      }, 100)
    }
    this.currentForm = this.addContentForms.find((e: any) => e.lang == this.selectedLanguage)!.form;
  }

  addValidationFormCategory() {
    let isValid = true;
    const values = this.addContentForms[this.lanIdx].form.value;
    if (!values.category) {
      isValid = false;
    }
    return isValid;
  }

  addValidationFormSEO() {
    let isValid = true;
    const values = this.addContentForms[this.lanIdx].form.value;
    if (!values.seo_description || values.seo_description.length < 2) {
      isValid = false;
    }
    return isValid;
  }

  addValidationFormSubmit(): boolean {
    const primaryForm = this.addContentForms.find(item => item.lang === 'ru');

    if (!primaryForm || primaryForm.form.invalid) {
      return false;
    }

    const otherForms = this.addContentForms.filter(item => item.lang !== 'ru');

    const hasInvalidDirtyForm = otherForms.some(
      item => {
        const isInProgress = !this.isFormEmpty(item.form);
        return isInProgress && item.form.invalid;
      } //item.form.dirty && item.form.invalid
    );

    return !hasInvalidDirtyForm;
  }

  isTabInvalid(lang: string): boolean {
    const form = this.addContentForms.find(item => item.lang === lang);
    if (!form) {
      return false;
    }

    const isInProgress = !this.isFormEmpty(form.form);

    return form.form.invalid && isInProgress;
  }

  addContentFormSubmit() {
    const category = this.addContentForms.find(e => e.lang == 'ru')!.form.value.category;
    const form = this.addContentForms.map(e => ({ ...e, form: e.lang === 'ru' ? e.form.value : {...e.form.value, category, buttons: (e.form.get('buttons') as FormArray).value} }));
    if (this.slug) {
      return this.contentService.update(this.slug, form).subscribe({
        next: () => {
          this.toasterService.showSuccess('Контент успешно обновлен');
        },
        error: () => {
          this.toasterService.showError('Контент не обновлен. Попробуйте еще');
        }
      });
    }

    return this.contentService.addContent(form).subscribe({
      next: (res: any) => {
        this.toasterService.showSuccess('Контент успешно добавлен');
        this.router.navigateByUrl('/content/' + res.slug);
      },
      error: () => {
        this.toasterService.showError('Контент не добавлен. Попробуйте еще');
      }
    });
  }

  openDialog(message: string) {
    this.message = message;
    this.dialog.nativeElement.showModal()
  }

  close(dialog: any) {
    dialog.close();
    if (this.message == 'Контент успешно добавлен.') {
      this.router.navigate(['/content']);
    }
    if (this.message == 'Контент успешно удален.') {
      this.router.navigate(['/content']);
    }
    if (this.message == 'Контент успешно обновлен.') {
      if(this.route.snapshot.params['slug'] !== this.addContentForms[0].form.get('slug')?.value){
        this.router.navigate(['/content/' + this.addContentForms[0].form.get('slug')?.value]);
      }

    }
  }

  back() {
    this.router.navigate(['/content']);
  }

  deleteWithCheckLinks() {
    this.adminDialog.showConfirm('Вы уверены, что хотите удалить этот контент?').then((confirmed) => {
      if (confirmed) {
        this.contentService.getLinkedContent(this.slug).subscribe((num: any) => {
          if(num) {
            this.linkedNum = num;
            this.contentService.getAll().subscribe((res: any) => {
              this.allLinks = res.filter((item: any) => item.slug !== this.slug);
            })

            this.linkedContentDialog.nativeElement.showModal()
          } else {
            this.replaceLinkForm.reset()
            this.delete()
          }
        })
      }
    });
  }

  delete() {
    this.contentService.delete(this.slug, this.replaceLinkForm.value.replace).subscribe({
      next: () => {
        this.toasterService.showSuccess('Контент успешно удален');
      },
      error: () => {
        this.toasterService.showError('Контент не удален. Попробуйте еще');
      }
    });
  }

  onCategoryChange(e: any) {
    this.addContentForms.map(k => k.form.patchValue({category: +e.target.value}))
  }

  async getFeedItems(query: string): Promise<any[]> {
    return this.contentService.searchPages(query).toPromise().then((results: any) => {
      const filteredResults = results.filter((item: any) => item.slug !== this.slug);

      return filteredResults.map((item: any) => ({
        id: `@${item.title}`,
        content_id: item.id,
        name: item.title,
        link: `/${item.lang}/categories/${item.category.id}/${item.slug}`,
        text: item.title
      }));
    });
  }

  copyLink(item: any) {
    window.navigator.clipboard.writeText(`${environment.clientUrl}${item.lang}/categories/${item.form.value.category}/${this.slug}`)
  }

  redirect(item: any) {
    window.open(`${environment.clientUrl}${item.lang}/categories/${item.form.value.category}/${this.slug}`, '_blank');
  }

  uploadPreview(e: Event, form: FormGroup) {
    const files = (e.target as HTMLInputElement).files!

    this.fileService.uploadToTempFolder(files).subscribe((res: any) => {
      if(!this.currentForm) {
        this.currentForm = this.addContentForms.find(e => e.lang == this.selectedLanguage)!.form;
      }

      this.currentForm.get('preview')?.setValue(res[0])
      // this.addContentForms.map(k => k.form.patchValue({
      //   preview: res[0]
      // }))
    })
  }

  generatePreview() {
    if(!this.currentForm) {
      this.currentForm = this.addContentForms.find(e => e.lang == this.selectedLanguage)!.form;
    }

    const title = this.currentForm.get('title')?.value;
    if (!title || title.trim() === '') {
      this.toasterService.showError('Введите заголовок для генерации превью');
      return;
    }

    this.isLoading = true;

    this.contentService.http.post('/firebase/generate-preview', { text: title.trim() })
      .subscribe({
        next: (response: any) => {
          if (response && response.url) {
            const url = new URL(response.url);
            const pathParts = url.pathname.split('/upload/');
            const relativePath = pathParts.length > 1 ? pathParts[1] : response.url;

            const previewObject = {
              name: relativePath,
              originalName: `generated_preview_${Date.now()}.jpg`
            };
            this.currentForm.get('preview')?.setValue(previewObject);
            this.toasterService.showSuccess('Превью успешно сгенерировано');
          } else {
            this.toasterService.showError('Не удалось сгенерировать превью');
          }
          this.isLoading = false;
        },
        error: (error) => {
          this.toasterService.showError('Ошибка при генерации превью');
          this.isLoading = false;
        }
      });
  }

  showAddButtonForm() {
    this.addButtonDialog.nativeElement.showModal()
  }

  showAddAudioForm() {
    this.addAudioDialog.nativeElement.showModal()
  }

  closeAddAudioForm() {
    this.addAudioDialog.nativeElement.close()
    this.audioForm.reset()
  }

  closeAddButtonForm() {
    this.addButtonDialog.nativeElement.close()
    this.buttonForm.reset()
  }

  addButton() {
    const buttons = this.currentForm.get('buttons') as FormArray;
    if(this.buttonForm.value.index !== null) {
      buttons.at(this.buttonForm.value.index!).patchValue(this.buttonForm.value)
    } else {
      buttons.push(this.fb.group(this.buttonForm.value))
    }
    this.closeAddButtonForm()
  }

  addAudio() {
    const audiofiles = this.currentForm.get('audiofiles') as FormArray;
    if(this.audioForm.value.index !== null) {
      audiofiles.at(this.audioForm.value.index!).patchValue(this.audioForm.value)
    } else {
      audiofiles.push(this.fb.group(this.audioForm.value))
    }
    this.closeAddAudioForm()
  }

  editButton(index: number, button: any) {
    button.index = index
    this.buttonForm.patchValue(button)
    this.showAddButtonForm()
  }

  editAudio(index: number, audio: any) {
    audio.index = index
    this.audioForm.patchValue(audio)
    this.showAddAudioForm()
  }

  removeButton(index: number) {
    this.currentForm.get('buttons').removeAt(index)
  }

  removeAudio(index: number) {
    this.currentForm.get('audiofiles').removeAt(index)
  }

  closeModal() {
    this.linkedContentDialog.nativeElement.close()
  }



  // Add this property to the class
isEditorFullscreen = false;

// Add this method to the class
toggleEditorFullscreen(): void {
  this.isEditorFullscreen = !this.isEditorFullscreen;

  // Prevent body scrolling when in fullscreen mode
  if (this.isEditorFullscreen) {
    document.body.style.overflow = 'hidden';
  } else {
    document.body.style.overflow = '';
  }

  // Refresh the editor to adjust to new container size
  if (this.editorInstance) {
    this.editorInstance.editing.view.change((writer: any) => {
      // This forces a refresh of the editing view
    });
  }

  // Focus the editor after toggling fullscreen
  setTimeout(() => {
    if (this.editorInstance) {
      this.editorInstance.editing.view.focus();
    }
  }, 100);
}
  clearAuthor() {
    // Найти текущую форму для выбранного языка
    const currentForm = this.addContentForms.find(form => form.lang === this.selectedLanguage);
    if (currentForm) {
      currentForm.form.patchValue({ author: '' });
    }
  }

  private isFormEmpty(form: FormGroup): boolean {
    const values = form.getRawValue();

    const isTitleEmpty = !values.title?.trim();
    const isSeoEmpty = !values.seo_title?.trim() && !values.seo_description?.trim();
    //const isTagsEmpty = !values.tags || values.tags.length === 0;
    const isPreviewEmpty = !values.preview;

    return isTitleEmpty && isSeoEmpty && isPreviewEmpty;
  }

  addYoutubeLink() {
    const linksArray = this.currentForm.get('youtube_links') as FormArray;

    const linkForm = this.fb.group({
      link: ['', Validators.required],
      paid: [false]
    });

    linksArray.push(linkForm);
  }

  removeYoutubeLink(index: number) {
    const linksArray = this.currentForm.get('youtube_links') as FormArray;
    linksArray.removeAt(index);
  }

  onContentPayStatusChange() {
    const audioFiles = this.audiofiles.value;
    const youtubeLinks = this.youtube_links.value;

    const hasPaidAudio = audioFiles.some((item: any) => item.paid || item?.audio?.paid);
    const hasPaidYoutube = youtubeLinks.some((item: any) => item.paid);

    this.currentForm.get('paid')?.setValue(hasPaidAudio || hasPaidYoutube, { emitEvent: false });
  }

  // Check if text contains Markdown syntax
  containsMarkdownSyntax(text: string): boolean {
    const markdownPatterns = [
      /\*\*.*?\*\*/,  // Bold **text**
      /\*.*?\*/,      // Italic *text*
      /__.*?__/,      // Bold __text__
      /_.*?_/,        // Italic _text_
      /^#{1,6}\s/m,   // Headers # ## ###
      /^\s*[\*\+\-]\s*/m,  // Unordered list (with optional indentation and space)
      /^\s*\d+\.\s*/m,     // Ordered list (with optional indentation and space)
      /^>+\s/m,       // Blockquote (single or nested >>, >>>)
      /`.*?`/,        // Inline code
      /```[\s\S]*?```/, // Code blocks
      /~~.*?~~/,      // Strikethrough
      /\[.*?\]\(.*?\)/ // Links [text](url)
    ];

    return markdownPatterns.some(pattern => pattern.test(text));
  }

  // Simple Markdown to HTML converter
  convertMarkdownToHtml(markdown: string): string {
    // Normalize line endings (Windows \r\n, Mac \r, Unix \n -> \n)
    let html = markdown.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

    // Headers
    html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
    html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
    html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');

    // Bold
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    html = html.replace(/__(.*?)__/g, '<strong>$1</strong>');

    // Italic
    html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');
    html = html.replace(/_(.*?)_/g, '<em>$1</em>');

    // Code inline
    html = html.replace(/`(.*?)`/g, '<code>$1</code>');

    // Code blocks
    html = html.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>');

    // Strikethrough
    html = html.replace(/~~(.*?)~~/g, '<del>$1</del>');

    // Links
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');

    // Process nested lists before line breaks
    html = this.convertNestedLists(html);

    // Process blockquotes before line breaks
    html = this.convertBlockquotes(html);

    // Convert remaining line breaks to <br> (but not inside HTML tags)
    // Split by newlines and process each line
    const lines = html.split('\n');
    const processedLines: string[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // Skip empty lines
      if (!line) continue;

      // If line is an HTML tag (starts with <), keep it as is
      if (line.startsWith('<')) {
        processedLines.push(line);
      } else {
        // Regular text line - will be separated by <br> later
        processedLines.push(line);
      }
    }

    html = processedLines.join('');

    return html;
  }

  // Convert nested Markdown lists to HTML
  private convertNestedLists(text: string): string {
    const lines = text.split('\n');
    const result: string[] = [];
    const stack: { level: number; type: 'ul' | 'ol' }[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // Skip empty lines within lists (they don't break the list)
      if (!line.trim() && stack.length > 0) {
        continue;
      }

      // Match list items with indentation
      // Supports *, +, -, and numbered lists (1., 2., etc.)
      const match = line.match(/^(\s*)([\*\+\-]|\d+\.)\s*(.*)$/);

      if (match) {
        const indent = match[1];
        const marker = match[2];
        const content = match[3].trim();

        // Skip if content is empty
        if (!content) {
          continue;
        }

        // Calculate indentation level (4 spaces or 1 tab = 1 level)
        const level = Math.floor(indent.replace(/\t/g, '    ').length / 4);
        const listType: 'ul' | 'ol' = /^\d+\.$/.test(marker) ? 'ol' : 'ul';

        // Close lists if we're going back to a lower level or changing type at same level
        while (stack.length > 0 && (stack[stack.length - 1].level > level ||
               (stack[stack.length - 1].level === level && stack[stack.length - 1].type !== listType))) {
          const closed = stack.pop()!;
          result.push(`</${closed.type}>`);
        }

        // Open new list if needed
        if (stack.length === 0 || stack[stack.length - 1].level < level) {
          result.push(`<${listType}>`);
          stack.push({ level, type: listType });
        }

        // Add list item
        result.push(`<li>${content}</li>`);
      } else {
        // Close all open lists when we encounter a non-list line
        while (stack.length > 0) {
          const closed = stack.pop()!;
          result.push(`</${closed.type}>`);
        }

        // Add the non-list line
        if (line.trim()) {
          result.push(line);
        }
      }
    }

    // Close any remaining open lists
    while (stack.length > 0) {
      const closed = stack.pop()!;
      result.push(`</${closed.type}>`);
    }

    return result.join('\n');
  }

  // Convert Markdown blockquotes to HTML
  private convertBlockquotes(text: string): string {
    const lines = text.split('\n');
    const result: string[] = [];
    let inBlockquote = false;
    let blockquoteLevel = 0;
    let blockquoteContent: string[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // Match blockquote lines with optional nesting (>, >>, >>>)
      const match = line.match(/^(>+)\s*(.*)$/);

      if (match) {
        const level = match[1].length; // Number of > symbols
        const content = match[2];

        if (!inBlockquote) {
          // Start new blockquote
          inBlockquote = true;
          blockquoteLevel = level;
          blockquoteContent = [];
        }

        // Handle nested blockquotes
        if (level > blockquoteLevel) {
          // Increase nesting
          for (let j = blockquoteLevel; j < level; j++) {
            blockquoteContent.push('<blockquote>');
          }
          blockquoteLevel = level;
        } else if (level < blockquoteLevel) {
          // Decrease nesting
          for (let j = level; j < blockquoteLevel; j++) {
            blockquoteContent.push('</blockquote>');
          }
          blockquoteLevel = level;
        }

        blockquoteContent.push(content);
      } else {
        // Not a blockquote line
        if (inBlockquote) {
          // Close all open blockquotes
          for (let j = 0; j < blockquoteLevel; j++) {
            blockquoteContent.push('</blockquote>');
          }

          // Wrap content in blockquote tags
          result.push('<blockquote>' + blockquoteContent.join('\n') + '</blockquote>');

          inBlockquote = false;
          blockquoteLevel = 0;
          blockquoteContent = [];
        }

        // Add the non-blockquote line
        if (line.trim() || !inBlockquote) {
          result.push(line);
        }
      }
    }

    // Close any remaining open blockquotes
    if (inBlockquote) {
      for (let j = 0; j < blockquoteLevel; j++) {
        blockquoteContent.push('</blockquote>');
      }
      result.push('<blockquote>' + blockquoteContent.join('\n') + '</blockquote>');
    }

    return result.join('\n');
  }

  copyToProd() {
    this.adminDialog.showConfirm('Скопировать контент на прод?').then((confirmed) => {
      if (confirmed) {
        this.isLoading = true;
        this.contentService.copyToProd(this.slug).subscribe({
          next: () => {
            this.toasterService.showSuccess('Контент успешно скопирован на прод');
            this.isLoading = false;
          },
          error: () => {
            this.toasterService.showError('Ошибка при копировании на прод');
            this.isLoading = false;
          }
        });
      }
    });
  }
}
