import { AnalyticsService } from '@/services/analytics.service'
import { CommonModule } from '@angular/common'
import { Component, inject } from '@angular/core'
import { FormsModule } from '@angular/forms'
import { Select } from 'primeng/select'

@Component({
    selector: 'app-dashboard',
    imports: [CommonModule, FormsModule, Select],
    templateUrl: './dashboard.component.html',
    styleUrl: './dashboard.component.scss'
})
export class DashboardComponent {
    analyticsService = inject(AnalyticsService)
    stats: any = null
    loading = true
    selectedPeriod: string = 'all'

    periods = [
        { value: 'all', label: 'За все время' },
        { value: 'month', label: 'За месяц' },
        { value: 'week', label: 'За неделю' },
        { value: 'day', label: 'За день' }
    ]

    ngOnInit() {
        this.loadStats()
    }

    loadStats() {
        this.loading = true
        this.analyticsService.getDashboardStats(this.selectedPeriod).subscribe({
            next: (data) => {
                this.stats = data
                this.loading = false
            },
            error: () => {
                this.loading = false
            }
        })
    }

    onPeriodChange() {
        this.loadStats()
    }
}

