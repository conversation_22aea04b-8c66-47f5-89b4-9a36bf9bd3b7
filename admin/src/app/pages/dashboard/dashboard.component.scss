.dashboard {
  padding: 20px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  gap: 20px;
  flex-wrap: wrap;
}

.dashboard-title {
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.period-selector {
  width: 200px;
}

.period-selector p-select {
  width: 100%;
  background: white;
}

.loading {
  text-align: center;
  padding: 40px;
  font-size: 18px;
  color: #6b7280;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-top: 20px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: transform 0.2s, box-shadow 0.2s;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

.stat-icon {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  &.users-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }

  &.content-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
  }

  &.library-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
  }

  &.registrations-icon {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: white;
  }

  &.donations-icon {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a6f 100%);
    color: white;
  }

  &.subscriptions-icon {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: white;
  }

  &.purchases-icon {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    color: white;
  }
}

.stat-content {
  flex: 1;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
  font-weight: 500;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
}

:host-context(.dark) {
  .dashboard-title {
    color: #f9fafb;
  }

  .stat-card {
    background: #1f2937;
  }

  .stat-label {
    color: #9ca3af;
  }

  .stat-value {
    color: #f9fafb;
  }
}

