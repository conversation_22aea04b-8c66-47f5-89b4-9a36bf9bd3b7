<div class="dashboard">
  <div class="dashboard-header">
    <h1 class="dashboard-title">Dashboard</h1>

    <div class="period-selector">
      <p-select
        [options]="periods"
        [(ngModel)]="selectedPeriod"
        (onChange)="onPeriodChange()"
        optionValue="value"
        optionLabel="label"
        placeholder="За все время">
      </p-select>
    </div>
  </div>

  <div *ngIf="loading" class="loading">
    Загрузка...
  </div>

  <div *ngIf="!loading && stats" class="stats-grid">
    <div class="stat-card">
      <div class="stat-icon users-icon">
        <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
          <circle cx="9" cy="7" r="4"></circle>
          <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
          <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
        </svg>
      </div>
      <div class="stat-content">
        <div class="stat-label">Зарегистрированных пользователей</div>
        <div class="stat-value">{{ stats.totalUsers }}</div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon registrations-icon">
        <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
          <circle cx="8.5" cy="7" r="4"></circle>
          <line x1="20" y1="8" x2="20" y2="14"></line>
          <line x1="23" y1="11" x2="17" y2="11"></line>
        </svg>
      </div>
      <div class="stat-content">
        <div class="stat-label">Новых регистраций</div>
        <div class="stat-value">{{ stats.recentRegistrations }}</div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon content-icon">
        <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
          <polyline points="14 2 14 8 20 8"></polyline>
          <line x1="16" y1="13" x2="8" y2="13"></line>
          <line x1="16" y1="17" x2="8" y2="17"></line>
          <polyline points="10 9 9 9 8 9"></polyline>
        </svg>
      </div>
      <div class="stat-content">
        <div class="stat-label">Просмотров статей</div>
        <div class="stat-value">{{ stats.contentViews }}</div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon library-icon">
        <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path>
          <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"></path>
        </svg>
      </div>
      <div class="stat-content">
        <div class="stat-label">Просмотров книг</div>
        <div class="stat-value">{{ stats.libraryViews }}</div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon donations-icon">
        <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
        </svg>
      </div>
      <div class="stat-content">
        <div class="stat-label">Пожертвования</div>
        <div class="stat-value">€{{ stats.donationsTotal | number:'1.0-0' }}</div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon subscriptions-icon">
        <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect>
          <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
        </svg>
      </div>
      <div class="stat-content">
        <div class="stat-label">Новые подписки ({{ stats.subscriptionsCount }})</div>
        <div class="stat-value">€{{ stats.subscriptionsTotal | number:'1.0-0' }}</div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon purchases-icon">
        <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="9" cy="21" r="1"></circle>
          <circle cx="20" cy="21" r="1"></circle>
          <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
        </svg>
      </div>
      <div class="stat-content">
        <div class="stat-label">Покупки ({{ stats.purchasesCount }})</div>
        <div class="stat-value">€{{ stats.purchasesTotal | number:'1.0-0' }}</div>
      </div>
    </div>
  </div>
</div>

